#include "HarmonyRewardsSystem.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "AbilitySystemComponent.h"
#include "AbilitySystemInterface.h"
#include "GameplayEffect.h"
#include "Engine/World.h"
#include "Engine/GameInstance.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"

UHarmonyRewardsSystem::UHarmonyRewardsSystem()
{
    // Default configuration
    MaxRewardsPerSession = 10;
    RewardCooldownTime = 60.0f; // 1 minute between rewards
    bEnableProgressiveRewards = true;
    bEnableSpecialEvents = true;
    
    // Initialize special event state
    bSpecialEventActive = false;
    CurrentEventMultiplier = 1.0f;
    
    // Initialize tier requirements
    InitializeTierRequirements();
    
    // Initialize default rewards
    InitializeDefaultRewards();
}

void UHarmonyRewardsSystem::ProcessKindnessReward(const FString& PlayerID, const FKindnessReward& Reward)
{
    if (PlayerID.IsEmpty())
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Cannot process reward for empty PlayerID"));
        return;
    }
    
    // Check reward cooldown
    if (IsPlayerOnRewardCooldown(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s is on reward cooldown"), *PlayerID);
        return;
    }
    
    // Check session reward limit
    if (HasPlayerReachedSessionLimit(PlayerID))
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s has reached session reward limit"), *PlayerID);
        return;
    }
    
    // Get or create player progress
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    
    // Apply special event bonus if active
    FKindnessReward ProcessedReward = Reward;
    if (bSpecialEventActive)
    {
        ProcessSpecialEventBonus(PlayerID, ProcessedReward);
    }
    
    // Update player progress
    Progress.TotalKindnessPoints += ProcessedReward.KindnessPoints;
    Progress.LastRewardTime = FDateTime::Now();
    Progress.RewardsThisSession++;
    
    // Update category progress
    ERewardCategory Category = DetermineRewardCategory(ProcessedReward);
    int32& CategoryPoints = Progress.CategoryProgress.FindOrAdd(Category);
    CategoryPoints += ProcessedReward.KindnessPoints;
    
    // Check for tier promotion
    CheckForTierPromotion(PlayerID);
    
    // Apply reward effects
    ApplyRewardEffects(PlayerID, ProcessedReward);
    
    // Update reward cooldown
    LastRewardTimes.Add(PlayerID, FDateTime::Now());
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Processed kindness reward for player %s: %d points"), 
        *PlayerID, ProcessedReward.KindnessPoints);
}

bool UHarmonyRewardsSystem::GrantReward(const FString& PlayerID, const FString& RewardID)
{
    if (!ValidateRewardEligibility(PlayerID, RewardID))
    {
        return false;
    }
    
    const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
    if (!Reward)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("Reward not found: %s"), *RewardID);
        return false;
    }
    
    // Get player progress
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    
    // Check if player has enough kindness points
    if (Progress.TotalKindnessPoints < Reward->KindnessPointsRequired)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s doesn't have enough kindness points for reward %s"), 
            *PlayerID, *RewardID);
        return false;
    }
    
    // Grant the reward
    Progress.UnlockedRewards.AddUnique(RewardID);
    
    // Deduct kindness points if it's a purchasable reward
    if (!Reward->bIsOneTimeReward)
    {
        Progress.TotalKindnessPoints -= Reward->KindnessPointsRequired;
    }
    
    // Apply reward effects
    ApplyRewardEffects(PlayerID, *Reward);
    
    // Notify player
    NotifyPlayerOfReward(PlayerID, *Reward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Granted reward %s to player %s"), *RewardID, *PlayerID);
    
    return true;
}

TArray<FHarmonyReward> UHarmonyRewardsSystem::GetAvailableRewards(const FString& PlayerID)
{
    TArray<FHarmonyReward> AvailableRewards;
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return AvailableRewards;
    }
    
    for (const auto& RewardPair : RegisteredRewards)
    {
        const FHarmonyReward& Reward = RewardPair.Value;
        
        // Check if reward is visible and player meets requirements
        if (Reward.bIsVisible && 
            Progress->TotalKindnessPoints >= Reward.KindnessPointsRequired &&
            !Progress->UnlockedRewards.Contains(Reward.RewardID))
        {
            AvailableRewards.Add(Reward);
        }
    }
    
    // Sort by kindness points required (lowest first)
    AvailableRewards.Sort([](const FHarmonyReward& A, const FHarmonyReward& B) {
        return A.KindnessPointsRequired < B.KindnessPointsRequired;
    });
    
    return AvailableRewards;
}

TArray<FHarmonyReward> UHarmonyRewardsSystem::GetUnlockedRewards(const FString& PlayerID)
{
    TArray<FHarmonyReward> UnlockedRewards;
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return UnlockedRewards;
    }
    
    for (const FString& RewardID : Progress->UnlockedRewards)
    {
        const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
        if (Reward)
        {
            UnlockedRewards.Add(*Reward);
        }
    }
    
    return UnlockedRewards;
}

ERewardTier UHarmonyRewardsSystem::CalculatePlayerTier(int32 TotalKindnessPoints)
{
    // Determine tier based on total kindness points
    if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Legendary])
    {
        return ERewardTier::Legendary;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Diamond])
    {
        return ERewardTier::Diamond;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Platinum])
    {
        return ERewardTier::Platinum;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Gold])
    {
        return ERewardTier::Gold;
    }
    else if (TotalKindnessPoints >= TierKindnessRequirements[ERewardTier::Silver])
    {
        return ERewardTier::Silver;
    }
    
    return ERewardTier::Bronze;
}

float UHarmonyRewardsSystem::GetProgressToNextTier(const FString& PlayerID)
{
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return 0.0f;
    }
    
    ERewardTier CurrentTier = CalculatePlayerTier(Progress->TotalKindnessPoints);
    ERewardTier NextTier = GetNextTier(CurrentTier);
    
    if (NextTier == CurrentTier)
    {
        return 1.0f; // Already at maximum tier
    }
    
    int32 CurrentTierRequirement = TierKindnessRequirements[CurrentTier];
    int32 NextTierRequirement = TierKindnessRequirements[NextTier];
    int32 PointsInCurrentTier = Progress->TotalKindnessPoints - CurrentTierRequirement;
    int32 PointsNeededForNextTier = NextTierRequirement - CurrentTierRequirement;
    
    return static_cast<float>(PointsInCurrentTier) / PointsNeededForNextTier;
}

// Private helper function implementations

void UHarmonyRewardsSystem::InitializeTierRequirements()
{
    TierKindnessRequirements.Add(ERewardTier::Bronze, 0);
    TierKindnessRequirements.Add(ERewardTier::Silver, 100);
    TierKindnessRequirements.Add(ERewardTier::Gold, 500);
    TierKindnessRequirements.Add(ERewardTier::Platinum, 1500);
    TierKindnessRequirements.Add(ERewardTier::Diamond, 5000);
    TierKindnessRequirements.Add(ERewardTier::Legendary, 15000);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initialized tier requirements"));
}

void UHarmonyRewardsSystem::InitializeDefaultRewards()
{
    // Create default rewards for each category and tier
    
    // Kindness rewards
    CreateDefaultReward(TEXT("First_Kindness"), ERewardCategory::Kindness, ERewardTier::Bronze, 
        TEXT("First Act of Kindness"), TEXT("Your first step towards building a better community!"), 10);
    
    CreateDefaultReward(TEXT("Kindness_Champion"), ERewardCategory::Kindness, ERewardTier::Gold, 
        TEXT("Kindness Champion"), TEXT("A true champion of kindness and positivity!"), 500);
    
    // Mentorship rewards
    CreateDefaultReward(TEXT("Helpful_Guide"), ERewardCategory::Mentorship, ERewardTier::Silver, 
        TEXT("Helpful Guide"), TEXT("You've shown others the way to improvement!"), 200);
    
    CreateDefaultReward(TEXT("Master_Mentor"), ERewardCategory::Mentorship, ERewardTier::Diamond, 
        TEXT("Master Mentor"), TEXT("Your guidance has transformed countless players!"), 2000);
    
    // Leadership rewards
    CreateDefaultReward(TEXT("Team_Leader"), ERewardCategory::Leadership, ERewardTier::Gold, 
        TEXT("Natural Leader"), TEXT("Your leadership inspires others to be their best!"), 750);
    
    // Healing rewards
    CreateDefaultReward(TEXT("Community_Healer"), ERewardCategory::Healing, ERewardTier::Platinum, 
        TEXT("Community Healer"), TEXT("You've helped heal the wounds of toxicity!"), 1000);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Initialized %d default rewards"), RegisteredRewards.Num());
}

void UHarmonyRewardsSystem::CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier, 
    const FString& Name, const FString& Description, int32 KindnessPointsRequired)
{
    FHarmonyReward Reward;
    Reward.RewardID = RewardID;
    Reward.Category = Category;
    Reward.Tier = Tier;
    Reward.RewardName = Name;
    Reward.Description = Description;
    Reward.KindnessPointsRequired = KindnessPointsRequired;
    Reward.ExperienceBonus = CalculateTierExperienceBonus(Tier);
    Reward.CurrencyBonus = CalculateTierCurrencyBonus(Tier);
    Reward.bIsOneTimeReward = true;
    Reward.bIsVisible = true;
    
    // Add appropriate tags
    switch (Category)
    {
        case ERewardCategory::Kindness:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Kindness);
            break;
        case ERewardCategory::Mentorship:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Mentorship);
            break;
        case ERewardCategory::Healing:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_Healing);
            break;
        case ERewardCategory::Leadership:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_CommunityHero);
            break;
    }
    
    RegisteredRewards.Add(RewardID, Reward);
}

bool UHarmonyRewardsSystem::ValidateRewardEligibility(const FString& PlayerID, const FString& RewardID)
{
    if (PlayerID.IsEmpty() || RewardID.IsEmpty())
    {
        return false;
    }
    
    const FHarmonyReward* Reward = RegisteredRewards.Find(RewardID);
    if (!Reward)
    {
        return false;
    }
    
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return false;
    }
    
    // Check if already unlocked (for one-time rewards)
    if (Reward->bIsOneTimeReward && Progress->UnlockedRewards.Contains(RewardID))
    {
        return false;
    }
    
    // Check kindness points requirement
    if (Progress->TotalKindnessPoints < Reward->KindnessPointsRequired)
    {
        return false;
    }
    
    return true;
}

bool UHarmonyRewardsSystem::IsPlayerOnRewardCooldown(const FString& PlayerID)
{
    const FDateTime* LastRewardTime = LastRewardTimes.Find(PlayerID);
    if (!LastRewardTime)
    {
        return false;
    }
    
    FDateTime CurrentTime = FDateTime::Now();
    float TimeSinceLastReward = (CurrentTime - *LastRewardTime).GetTotalSeconds();
    
    return TimeSinceLastReward < RewardCooldownTime;
}

bool UHarmonyRewardsSystem::HasPlayerReachedSessionLimit(const FString& PlayerID)
{
    const FPlayerRewardProgress* Progress = PlayerProgress.Find(PlayerID);
    if (!Progress)
    {
        return false;
    }
    
    return Progress->RewardsThisSession >= MaxRewardsPerSession;
}

void UHarmonyRewardsSystem::CheckForTierPromotion(const FString& PlayerID)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    
    ERewardTier NewTier = CalculatePlayerTier(Progress.TotalKindnessPoints);
    
    if (NewTier != Progress.CurrentTier)
    {
        ProcessTierPromotion(PlayerID, NewTier);
    }
    
    // Update tier progress
    CalculateTierProgress(PlayerID);
}

void UHarmonyRewardsSystem::ProcessTierPromotion(const FString& PlayerID, ERewardTier NewTier)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);
    ERewardTier OldTier = Progress.CurrentTier;
    
    Progress.CurrentTier = NewTier;
    Progress.TierProgress = 0.0f; // Reset progress for new tier
    
    // Create tier promotion reward
    FHarmonyReward TierReward;
    TierReward.RewardID = FString::Printf(TEXT("TierPromotion_%s_%d"), *PlayerID, static_cast<int32>(NewTier));
    TierReward.Category = ERewardCategory::Consistency;
    TierReward.Tier = NewTier;
    TierReward.RewardName = FString::Printf(TEXT("%s Tier Achieved!"), *GetTierDisplayName(NewTier));
    TierReward.Description = FString::Printf(TEXT("Congratulations on reaching %s tier through consistent positive behavior!"), *GetTierDisplayName(NewTier));
    TierReward.ExperienceBonus = CalculateTierExperienceBonus(NewTier);
    TierReward.CurrencyBonus = CalculateTierCurrencyBonus(NewTier);
    TierReward.bIsOneTimeReward = true;
    
    // Apply tier promotion effects
    ApplyRewardEffects(PlayerID, TierReward);
    
    // Notify player of promotion
    NotifyPlayerOfReward(PlayerID, TierReward);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s promoted from %s to %s tier"), 
        *PlayerID, *GetTierDisplayName(OldTier), *GetTierDisplayName(NewTier));
}

void UHarmonyRewardsSystem::ApplyRewardEffects(const FString& PlayerID, const FHarmonyReward& Reward)
{
    UAbilitySystemComponent* ASC = GetPlayerAbilitySystemComponent(PlayerID);
    if (!ASC)
    {
        UE_LOG(LogHarmonyEngine, Warning, TEXT("No AbilitySystemComponent found for player %s"), *PlayerID);
        return;
    }
    
    // Apply gameplay effects
    if (Reward.RewardGameplayEffect)
    {
        GrantGameplayEffects(PlayerID, Reward);
    }
    
    // Grant abilities
    if (Reward.UnlockedAbilities.Num() > 0)
    {
        GrantAbilities(PlayerID, Reward);
    }
    
    // Apply experience bonus
    if (Reward.ExperienceBonus > 0.0f)
    {
        ApplyExperienceBonus(PlayerID, Reward.ExperienceBonus);
    }
    
    // Apply currency bonus
    if (Reward.CurrencyBonus > 0.0f)
    {
        ApplyCurrencyBonus(PlayerID, Reward.CurrencyBonus);
    }
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied reward effects for %s to player %s"), 
        *Reward.RewardName, *PlayerID);
}

void UHarmonyRewardsSystem::GrantGameplayEffects(const FString& PlayerID, const FHarmonyReward& Reward)
{
    UAbilitySystemComponent* ASC = GetPlayerAbilitySystemComponent(PlayerID);
    if (!ASC || !Reward.RewardGameplayEffect)
    {
        return;
    }
    
    // Create effect context
    FGameplayEffectContextHandle EffectContext = ASC->MakeEffectContext();
    EffectContext.AddSourceObject(this);
    
    // Create and apply effect spec
    FGameplayEffectSpecHandle SpecHandle = ASC->MakeOutgoingSpec(Reward.RewardGameplayEffect, 1.0f, EffectContext);
    if (SpecHandle.IsValid())
    {
        ASC->ApplyGameplayEffectSpecToSelf(*SpecHandle.Data.Get());
        UE_LOG(LogHarmonyEngine, Log, TEXT("Applied gameplay effect for reward %s"), *Reward.RewardName);
    }
}

void UHarmonyRewardsSystem::GrantAbilities(const FString& PlayerID, const FHarmonyReward& Reward)
{
    UAbilitySystemComponent* ASC = GetPlayerAbilitySystemComponent(PlayerID);
    if (!ASC)
    {
        return;
    }
    
    for (TSubclassOf<UGameplayAbility> AbilityClass : Reward.UnlockedAbilities)
    {
        if (AbilityClass)
        {
            // Grant ability to player
            FGameplayAbilitySpec AbilitySpec(AbilityClass, 1, INDEX_NONE, this);
            ASC->GiveAbility(AbilitySpec);
            
            UE_LOG(LogHarmonyEngine, Log, TEXT("Granted ability %s for reward %s"), 
                *AbilityClass->GetName(), *Reward.RewardName);
        }
    }
}

ERewardCategory UHarmonyRewardsSystem::DetermineRewardCategory(const FKindnessReward& Reward)
{
    // Analyze reward tags to determine category
    if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Behavior_Mentoring))
    {
        return ERewardCategory::Mentorship;
    }
    else if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Behavior_Healing))
    {
        return ERewardCategory::Healing;
    }
    else if (Reward.RewardTags.HasTag(HarmonyEngineGameplayTags::Reward_CommunityHero))
    {
        return ERewardCategory::Leadership;
    }
    
    return ERewardCategory::Kindness; // Default category
}

float UHarmonyRewardsSystem::CalculateTierExperienceBonus(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return 100.0f;
        case ERewardTier::Silver: return 250.0f;
        case ERewardTier::Gold: return 500.0f;
        case ERewardTier::Platinum: return 1000.0f;
        case ERewardTier::Diamond: return 2500.0f;
        case ERewardTier::Legendary: return 5000.0f;
        default: return 0.0f;
    }
}

float UHarmonyRewardsSystem::CalculateTierCurrencyBonus(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return 50.0f;
        case ERewardTier::Silver: return 150.0f;
        case ERewardTier::Gold: return 400.0f;
        case ERewardTier::Platinum: return 1000.0f;
        case ERewardTier::Diamond: return 2500.0f;
        case ERewardTier::Legendary: return 10000.0f;
        default: return 0.0f;
    }
}

FString UHarmonyRewardsSystem::GetTierDisplayName(ERewardTier Tier)
{
    switch (Tier)
    {
        case ERewardTier::Bronze: return TEXT("Bronze");
        case ERewardTier::Silver: return TEXT("Silver");
        case ERewardTier::Gold: return TEXT("Gold");
        case ERewardTier::Platinum: return TEXT("Platinum");
        case ERewardTier::Diamond: return TEXT("Diamond");
        case ERewardTier::Legendary: return TEXT("Legendary");
        default: return TEXT("Unknown");
    }
}

ERewardTier UHarmonyRewardsSystem::GetNextTier(ERewardTier CurrentTier)
{
    switch (CurrentTier)
    {
        case ERewardTier::Bronze: return ERewardTier::Silver;
        case ERewardTier::Silver: return ERewardTier::Gold;
        case ERewardTier::Gold: return ERewardTier::Platinum;
        case ERewardTier::Platinum: return ERewardTier::Diamond;
        case ERewardTier::Diamond: return ERewardTier::Legendary;
        case ERewardTier::Legendary: return ERewardTier::Legendary; // Max tier
        default: return ERewardTier::Bronze;
    }
}

// Implementation of missing functions

void UHarmonyRewardsSystem::ProcessSpecialEventBonus(const FString& PlayerID, FKindnessReward& Reward)
{
    if (!bSpecialEventActive)
    {
        return;
    }

    // Apply event multiplier to kindness points
    int32 OriginalPoints = Reward.KindnessPoints;
    Reward.KindnessPoints = FMath::RoundToInt(Reward.KindnessPoints * CurrentEventMultiplier);

    // Apply multiplier to experience bonus
    Reward.ExperienceMultiplier *= CurrentEventMultiplier;

    // Update reward description to indicate special event
    Reward.RewardDescription += FString::Printf(TEXT(" (Special Event: %s - %.1fx bonus!)"), *CurrentEventName, CurrentEventMultiplier);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied special event bonus to player %s: %d -> %d points"),
        *PlayerID, OriginalPoints, Reward.KindnessPoints);
}

void UHarmonyRewardsSystem::CalculateTierProgress(const FString& PlayerID)
{
    FPlayerRewardProgress& Progress = PlayerProgress.FindOrAdd(PlayerID);

    ERewardTier CurrentTier = Progress.CurrentTier;
    ERewardTier NextTier = GetNextTier(CurrentTier);

    if (NextTier == CurrentTier)
    {
        Progress.TierProgress = 1.0f; // Already at max tier
        return;
    }

    int32 CurrentTierRequirement = TierKindnessRequirements[CurrentTier];
    int32 NextTierRequirement = TierKindnessRequirements[NextTier];
    int32 PointsInCurrentTier = Progress.TotalKindnessPoints - CurrentTierRequirement;
    int32 PointsNeededForNextTier = NextTierRequirement - CurrentTierRequirement;

    Progress.TierProgress = static_cast<float>(PointsInCurrentTier) / PointsNeededForNextTier;
    Progress.TierProgress = FMath::Clamp(Progress.TierProgress, 0.0f, 1.0f);
}

void UHarmonyRewardsSystem::ApplyExperienceBonus(const FString& PlayerID, float ExperienceBonus)
{
    // Apply experience bonus to player
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC || !PC->GetPlayerState<APlayerState>())
    {
        return;
    }

    // In a full implementation, this would integrate with the game's experience system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied experience bonus of %.2f to player %s"), ExperienceBonus, *PlayerID);

    // Example integration:
    // if (UExperienceComponent* ExpComp = PC->GetPlayerState()->FindComponentByClass<UExperienceComponent>())
    // {
    //     ExpComp->AddExperience(ExperienceBonus);
    // }
}

void UHarmonyRewardsSystem::ApplyCurrencyBonus(const FString& PlayerID, float CurrencyBonus)
{
    // Apply currency bonus to player
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC || !PC->GetPlayerState<APlayerState>())
    {
        return;
    }

    // In a full implementation, this would integrate with the game's currency system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Applied currency bonus of %.2f to player %s"), CurrencyBonus, *PlayerID);

    // Example integration:
    // if (UCurrencyComponent* CurrencyComp = PC->GetPlayerState()->FindComponentByClass<UCurrencyComponent>())
    // {
    //     CurrencyComp->AddCurrency(CurrencyBonus);
    // }
}

void UHarmonyRewardsSystem::NotifyPlayerOfReward(const FString& PlayerID, const FHarmonyReward& Reward)
{
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC)
    {
        return;
    }

    // Create notification message
    FString NotificationMessage = FString::Printf(
        TEXT("🎉 Reward Unlocked: %s! %s"),
        *Reward.RewardName,
        *Reward.Description
    );

    // In a full implementation, this would integrate with the game's UI notification system
    UE_LOG(LogHarmonyEngine, Log, TEXT("Notifying player %s of reward: %s"), *PlayerID, *NotificationMessage);

    // Example integration:
    // if (UNotificationComponent* NotificationComp = PC->FindComponentByClass<UNotificationComponent>())
    // {
    //     NotificationComp->ShowRewardNotification(Reward);
    // }
}

UAbilitySystemComponent* UHarmonyRewardsSystem::GetPlayerAbilitySystemComponent(const FString& PlayerID)
{
    APlayerController* PC = GetPlayerController(PlayerID);
    if (!PC)
    {
        return nullptr;
    }

    // Try to get ASC from pawn
    if (PC->GetPawn() && PC->GetPawn()->Implements<UAbilitySystemInterface>())
    {
        return Cast<IAbilitySystemInterface>(PC->GetPawn())->GetAbilitySystemComponent();
    }

    // Try to get ASC from player state
    if (PC->GetPlayerState<APlayerState>() && PC->GetPlayerState<APlayerState>()->Implements<UAbilitySystemInterface>())
    {
        return Cast<IAbilitySystemInterface>(PC->GetPlayerState<APlayerState>())->GetAbilitySystemComponent();
    }

    return nullptr;
}

APlayerController* UHarmonyRewardsSystem::GetPlayerController(const FString& PlayerID)
{
    // Get player controller through HarmonyEngineSubsystem
    UHarmonyEngineSubsystem* HarmonySubsystem = GetWorld()->GetGameInstance()->GetSubsystem<UHarmonyEngineSubsystem>();
    if (!HarmonySubsystem)
    {
        return nullptr;
    }

    // In a full implementation, HarmonyEngineSubsystem would provide player lookup
    // For now, we'll search through all player controllers
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPlayerState<APlayerState>())
            {
                FString CurrentPlayerID = PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
                if (CurrentPlayerID == PlayerID)
                {
                    return PC;
                }
            }
        }
    }

    return nullptr;
}

void UHarmonyRewardsSystem::CreateDefaultReward(const FString& RewardID, ERewardCategory Category, ERewardTier Tier, const FString& Name, const FString& Description)
{
    FHarmonyReward Reward;
    Reward.RewardID = RewardID;
    Reward.RewardName = Name;
    Reward.Description = Description;
    Reward.Category = Category;
    Reward.Tier = Tier;
    Reward.bIsActive = true;
    Reward.bIsRepeatable = false;

    // Set tier-based bonuses
    Reward.ExperienceBonus = CalculateTierExperienceBonus(Tier);
    Reward.CurrencyBonus = CalculateTierCurrencyBonus(Tier);

    // Add appropriate tags based on category
    switch (Category)
    {
        case ERewardCategory::Kindness:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Positive);
            break;
        case ERewardCategory::Mentorship:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Mentoring);
            break;
        case ERewardCategory::Leadership:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Reward_CommunityHero);
            break;
        case ERewardCategory::Healing:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Healing);
            break;
        default:
            Reward.RewardTags.AddTag(HarmonyEngineGameplayTags::Behavior_Positive);
            break;
    }

    // Store the reward
    RegisteredRewards.Add(RewardID, Reward);

    UE_LOG(LogHarmonyEngine, Log, TEXT("Created default reward: %s (%s)"), *Name, *RewardID);
}
