using UnrealBuildTool;
public class AuracronMetaHumanBridge : ModuleRules
{
    public AuracronMetaHumanBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        // Core UE5.6 dependencies
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "InputCore",
                "RenderCore",
                "RHI",
                "Json"
            }
        );
        // Editor-only dependencies
        if (Target.bBuildEditor)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "UnrealEd",
                "ToolMenus",
                "EditorStyle",
                "EditorWidgets",

                "PropertyEditor",
                "DetailCustomizations",
                "ComponentVisualizers",
                "LevelEditor",
                "SceneOutliner",
                "GraphEditor",
                "EditorSubsystem",
                "EngineSettings",
                "DeveloperSettings",
                "Projects",
                "ToolMenus",
                "ApplicationCore",
                "WidgetCarousel",
                "LogVisualizer",
                "OutputLog",
                "AnimationBlueprintLibrary",
                "AnimGraph",
                "BlueprintGraph",
                "KismetCompiler",
                "Kismet",
                "KismetWidgets",
                "Persona",
                "SkeletalMeshEditor",
                "AnimationBlueprintEditor",
                "AnimationEditor",
                "SequencerCore",
                "Sequencer",
                "MovieSceneTools",
                "LevelSequenceEditor",
                "ClothingSystemEditor",
                "ChaosClothEditor",
                "HairStrandsEditor",
                "MaterialEditor",
                "MaterialUtilities",
                "TextureEditor",
                "DeveloperToolSettings",
                "MeshUtilities",
                "LiveLinkEditor"
            });
        }
        // MetaHuman and Animation specific dependencies
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "AnimationCore",
                "AnimGraphRuntime",
                "MovieScene",
                "MovieSceneTracks",
                "ClothingSystemRuntimeInterface",
                "ClothingSystemRuntimeCommon",
                "ClothingSystemRuntimeNv",
                "ChaosCloth",
                "ChaosCore",
                "PhysicsCore",
                "HairStrandsCore",
                "ImageWrapper",
                "ImageCore",
                "MeshDescription",
                "StaticMeshDescription",
                "SkeletalMeshDescription",
                "MeshConversion",
                "MeshUtilitiesCommon",
                "GeometryCore",
                "DynamicMesh",
                "GeometryFramework",
                "ModelingComponents",
                "ModelingOperators",

                "LiveLinkInterface",
                "LiveLinkComponents",
                "LiveLinkAnimationCore",
                "LiveLinkMovieScene",
                "FacialAnimation",
                "AudioSynesthesia",
                "AudioAnalyzer",
                "TraceLog",


                "MessageLog",
                "Networking",
                "Sockets",
                "RSA",
                "RenderCore",
                "RHI"
            }
        );
        // Enable RTTI for this module (required for MetaHuman DNA)
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanBridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronMetaHumanBridge/Private"
        });
        // MetaHuman DNA Calibration support
        PublicDefinitions.Add("WITH_METAHUMAN_DNA_CALIBRATION=1");
        // Enable Python bindings
        PublicDefinitions.Add("WITH_PYTHON_BINDINGS=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDefinitions.Add("PLATFORM_WINDOWS=1");
            // Add Windows-specific libraries if needed
            PublicSystemLibraries.AddRange(new string[]
            {
                "kernel32.lib",
                "user32.lib",
                "gdi32.lib",
                "winspool.lib",
                "comdlg32.lib",
                "advapi32.lib",
                "shell32.lib",
                "ole32.lib",
                "oleaut32.lib",
                "uuid.lib",
                "odbc32.lib",
                "odbccp32.lib"
            });
        }
        else if (Target.Platform == UnrealTargetPlatform.Mac)
        {
            PublicDefinitions.Add("PLATFORM_MAC=1");
        }
        else if (Target.Platform == UnrealTargetPlatform.Linux)
        {
            PublicDefinitions.Add("PLATFORM_LINUX=1");
        }
        // Development and debugging settings
        if (Target.Configuration == UnrealTargetConfiguration.Debug || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_DEBUG=1");
            PublicDefinitions.Add("AURACRON_ENABLE_DETAILED_LOGGING=1");
        }
        // Shipping optimizations
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_SHIPPING=1");
            PublicDefinitions.Add("AURACRON_DISABLE_DEBUG_FEATURES=1");
        }
        // Enable Unity builds for faster compilation
        bUseUnity = true;
        // Enable Include-What-You-Use for better compile times
        IWYUSupport = IWYUSupport.Full;
        // Precompiled header settings
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PrivatePCHHeaderFile = "AuracronMetaHumanBridge.h";
        // C++ standard - Using Cpp20 for UE 5.6 compatibility
        CppStandard = CppStandardVersion.Cpp20;
        // Treat warnings as errors in development builds
        if (Target.Configuration != UnrealTargetConfiguration.Shipping)
        {
            bTreatAsEngineModule = false;
            bWarningsAsErrors = false; // Set to true for stricter builds
        }
    }
}

