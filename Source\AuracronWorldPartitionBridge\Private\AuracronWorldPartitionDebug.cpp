// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Debugging Tools Implementation
// Bridge 3.12: World Partition - Debugging Tools

#include "AuracronWorldPartitionDebug.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// Debug visualization includes
#include "DrawDebugHelpers.h"
#include "Engine/Canvas.h"
#include "Engine/Engine.h"
#include "Debug/DebugDrawService.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/HUD.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"

// =============================================================================
// WORLD PARTITION DEBUG MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionDebugManager* UAuracronWorldPartitionDebugManager::Instance = nullptr;

UAuracronWorldPartitionDebugManager* UAuracronWorldPartitionDebugManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionDebugManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionDebugManager::Initialize(const FAuracronDebugConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Debug Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize debug state
    bIsProfiling = false;
    ProfilingStartTime = FDateTime::Now();
    LastProfilerUpdate = 0.0f;
    LastInspectorRefresh = 0.0f;
    
    // Clear collections
    CellDebugInfo.Empty();
    PerformanceHistory.Empty();
    StreamingEventLog.Empty();
    InspectorData.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Debug Manager initialized with visualization mode: %s"), 
                         *UEnum::GetValueAsString(Configuration.VisualizationMode));
}

void UAuracronWorldPartitionDebugManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Stop profiling
    StopProfiling();
    
    // Clear all data
    CellDebugInfo.Empty();
    PerformanceHistory.Empty();
    StreamingEventLog.Empty();
    InspectorData.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Debug Manager shutdown completed"));
}

bool UAuracronWorldPartitionDebugManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionDebugManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    UpdateDebugData(DeltaTime);
}

void UAuracronWorldPartitionDebugManager::EnableDebugVisualization(bool bEnabled)
{
    Configuration.bEnableDebugVisualization = bEnabled;
    
    if (bEnabled)
    {
        AURACRON_WP_LOG_INFO(TEXT("Debug visualization enabled"));
    }
    else
    {
        AURACRON_WP_LOG_INFO(TEXT("Debug visualization disabled"));
    }
}

bool UAuracronWorldPartitionDebugManager::IsDebugVisualizationEnabled() const
{
    return Configuration.bEnableDebugVisualization;
}

void UAuracronWorldPartitionDebugManager::SetVisualizationMode(EAuracronDebugVisualizationMode Mode)
{
    Configuration.VisualizationMode = Mode;
    
    AURACRON_WP_LOG_INFO(TEXT("Visualization mode set to: %s"), *UEnum::GetValueAsString(Mode));
}

EAuracronDebugVisualizationMode UAuracronWorldPartitionDebugManager::GetVisualizationMode() const
{
    return Configuration.VisualizationMode;
}

void UAuracronWorldPartitionDebugManager::DrawDebugVisualization(UWorld* World) const
{
    if (!Configuration.bEnableDebugVisualization || !World)
    {
        return;
    }

    switch (Configuration.VisualizationMode)
    {
        case EAuracronDebugVisualizationMode::CellBounds:
            DrawCellBounds(World);
            break;
            
        case EAuracronDebugVisualizationMode::StreamingStates:
            DrawStreamingStates(World);
            break;
            
        case EAuracronDebugVisualizationMode::LoadingProgress:
            DrawLoadingProgress(World);
            break;
            
        case EAuracronDebugVisualizationMode::PerformanceMetrics:
            DrawPerformanceMetrics(World);
            break;
            
        case EAuracronDebugVisualizationMode::All:
            DrawCellBounds(World);
            DrawStreamingStates(World);
            DrawLoadingProgress(World);
            DrawPerformanceMetrics(World);
            break;
            
        default:
            break;
    }
    
    if (Configuration.bShowInspectorWindow)
    {
        DrawInspectorWindow(World);
    }
}

void UAuracronWorldPartitionDebugManager::DrawCellBounds(UWorld* World) const
{
    if (!Configuration.bShowCellBounds || !World)
    {
        return;
    }

    FScopeLock Lock(&DebugLock);
    
    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        FColor BoundsColor = GetColorForStreamingState(CellInfo.StreamingState);
        
        // Draw cell bounds
        DrawDebugBox(World, CellInfo.CellBounds.GetCenter(), CellInfo.CellBounds.GetExtent(), 
                    BoundsColor, false, -1.0f, 0, 2.0f);
        
        // Draw cell ID
        if (Configuration.InfoLevel >= EAuracronDebugInfoLevel::Basic)
        {
            FString CellText = CellInfo.CellId;
            if (Configuration.InfoLevel >= EAuracronDebugInfoLevel::Detailed)
            {
                CellText += FString::Printf(TEXT("\nActors: %d"), CellInfo.ActorCount);
            }
            if (Configuration.InfoLevel >= EAuracronDebugInfoLevel::Verbose)
            {
                CellText += FString::Printf(TEXT("\nMemory: %.1fMB"), CellInfo.MemoryUsageMB);
                CellText += FString::Printf(TEXT("\nDistance: %.1fm"), CellInfo.DistanceToViewer);
            }
            
            DrawDebugText(World, CellInfo.CellBounds.GetCenter() + FVector(0, 0, 100), 
                         CellText, BoundsColor, Configuration.DebugTextScale);
        }
    }
}

void UAuracronWorldPartitionDebugManager::DrawStreamingStates(UWorld* World) const
{
    if (!Configuration.bShowStreamingStates || !World)
    {
        return;
    }

    FScopeLock Lock(&DebugLock);
    
    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        FColor StateColor = GetColorForStreamingState(CellInfo.StreamingState);
        FVector CellCenter = CellInfo.CellBounds.GetCenter();
        
        // Draw streaming state indicator
        DrawDebugSphere(World, CellCenter + FVector(0, 0, 200), 50.0f, 12, StateColor, false, -1.0f, 0, 3.0f);
        
        // Draw state text
        FString StateText = UEnum::GetValueAsString(CellInfo.StreamingState);
        DrawDebugText(World, CellCenter + FVector(0, 0, 300), StateText, StateColor, Configuration.DebugTextScale);
    }
}

void UAuracronWorldPartitionDebugManager::DrawLoadingProgress(UWorld* World) const
{
    if (!Configuration.bShowLoadingProgress || !World)
    {
        return;
    }

    FScopeLock Lock(&DebugLock);
    
    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        if (CellInfo.StreamingState == EAuracronCellStreamingState::Loading ||
            CellInfo.StreamingState == EAuracronCellStreamingState::Unloading)
        {
            FColor ProgressColor = GetColorForLoadingProgress(CellInfo.LoadingProgress);
            FVector CellCenter = CellInfo.CellBounds.GetCenter();
            
            // Draw progress bar
            float BarWidth = 200.0f;
            float BarHeight = 20.0f;
            FVector BarStart = CellCenter + FVector(-BarWidth * 0.5f, 0, 400);
            FVector BarEnd = BarStart + FVector(BarWidth * CellInfo.LoadingProgress, 0, 0);
            
            // Background
            DrawDebugBox(World, BarStart + FVector(BarWidth * 0.5f, 0, 0), 
                        FVector(BarWidth * 0.5f, 5.0f, BarHeight * 0.5f), 
                        FColor::Black, false, -1.0f, 0, 1.0f);
            
            // Progress
            if (CellInfo.LoadingProgress > 0.0f)
            {
                DrawDebugBox(World, BarStart + FVector(BarWidth * CellInfo.LoadingProgress * 0.5f, 0, 0), 
                            FVector(BarWidth * CellInfo.LoadingProgress * 0.5f, 5.0f, BarHeight * 0.5f), 
                            ProgressColor, false, -1.0f, 0, 2.0f);
            }
            
            // Progress text
            FString ProgressText = FString::Printf(TEXT("%.1f%%"), CellInfo.LoadingProgress * 100.0f);
            DrawDebugText(World, CellCenter + FVector(0, 0, 450), ProgressText, ProgressColor, Configuration.DebugTextScale);
        }
    }
}

FAuracronDebugCellInfo UAuracronWorldPartitionDebugManager::GetCellDebugInfo(const FString& CellId) const
{
    FScopeLock Lock(&DebugLock);

    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        if (CellInfo.CellId == CellId)
        {
            return CellInfo;
        }
    }

    return FAuracronDebugCellInfo();
}

TArray<FAuracronDebugCellInfo> UAuracronWorldPartitionDebugManager::GetAllCellDebugInfo() const
{
    FScopeLock Lock(&DebugLock);
    return CellDebugInfo;
}

void UAuracronWorldPartitionDebugManager::EnableStreamingDebug(bool bEnabled)
{
    Configuration.bEnableStreamingDebug = bEnabled;

    if (bEnabled)
    {
        AURACRON_WP_LOG_INFO(TEXT("Streaming debug enabled"));
    }
    else
    {
        AURACRON_WP_LOG_INFO(TEXT("Streaming debug disabled"));
    }
}

bool UAuracronWorldPartitionDebugManager::IsStreamingDebugEnabled() const
{
    return Configuration.bEnableStreamingDebug;
}

void UAuracronWorldPartitionDebugManager::LogStreamingEvent(const FString& EventType, const FString& CellId, const FString& Details)
{
    if (!Configuration.bLogStreamingEvents)
    {
        return;
    }

    FScopeLock Lock(&DebugLock);

    FString EventEntry = FString::Printf(TEXT("[%s] %s - %s: %s"),
                                       *FDateTime::Now().ToString(),
                                       *EventType,
                                       *CellId,
                                       *Details);

    StreamingEventLog.Add(EventEntry);

    // Trim log if too large
    TrimStreamingEventLog();

    OnStreamingEvent.Broadcast(CellId, Details);

    if (Configuration.bEnableDebugLogging)
    {
        AURACRON_WP_LOG_INFO(TEXT("Streaming Event: %s"), *EventEntry);
    }
}

TArray<FString> UAuracronWorldPartitionDebugManager::GetStreamingEventLog() const
{
    FScopeLock Lock(&DebugLock);
    return StreamingEventLog;
}

void UAuracronWorldPartitionDebugManager::ClearStreamingEventLog()
{
    FScopeLock Lock(&DebugLock);
    StreamingEventLog.Empty();

    AURACRON_WP_LOG_INFO(TEXT("Streaming event log cleared"));
}

void UAuracronWorldPartitionDebugManager::EnablePerformanceProfiler(bool bEnabled)
{
    Configuration.bEnablePerformanceProfiler = bEnabled;

    if (bEnabled)
    {
        StartProfiling();
    }
    else
    {
        StopProfiling();
    }
}

bool UAuracronWorldPartitionDebugManager::IsPerformanceProfilerEnabled() const
{
    return Configuration.bEnablePerformanceProfiler;
}

void UAuracronWorldPartitionDebugManager::StartProfiling()
{
    if (bIsProfiling)
    {
        return;
    }

    bIsProfiling = true;
    ProfilingStartTime = FDateTime::Now();
    LastProfilerUpdate = 0.0f;

    // Clear previous data
    {
        FScopeLock Lock(&DebugLock);
        PerformanceHistory.Empty();
    }

    AURACRON_WP_LOG_INFO(TEXT("Performance profiling started"));
}

void UAuracronWorldPartitionDebugManager::StopProfiling()
{
    if (!bIsProfiling)
    {
        return;
    }

    bIsProfiling = false;

    AURACRON_WP_LOG_INFO(TEXT("Performance profiling stopped"));
}

FAuracronDebugPerformanceData UAuracronWorldPartitionDebugManager::GetCurrentPerformanceData() const
{
    FScopeLock Lock(&DebugLock);

    if (PerformanceHistory.Num() > 0)
    {
        return PerformanceHistory.Last();
    }

    return FAuracronDebugPerformanceData();
}

TArray<FAuracronDebugPerformanceData> UAuracronWorldPartitionDebugManager::GetPerformanceHistory(int32 MaxSamples) const
{
    FScopeLock Lock(&DebugLock);

    if (MaxSamples <= 0 || MaxSamples >= PerformanceHistory.Num())
    {
        return PerformanceHistory;
    }
    else
    {
        int32 StartIndex = FMath::Max(0, PerformanceHistory.Num() - MaxSamples);
        return TArray<FAuracronDebugPerformanceData>(PerformanceHistory.GetData() + StartIndex, MaxSamples);
    }
}

void UAuracronWorldPartitionDebugManager::DrawPerformanceMetrics(UWorld* World) const
{
    if (!Configuration.bShowPerformanceMetrics || !World)
    {
        return;
    }

    FAuracronDebugPerformanceData CurrentData = GetCurrentPerformanceData();

    FVector MetricsLocation = FVector(0, 0, 2000);
    FColor MetricsColor = FColor::White;

    FString MetricsText = FString::Printf(TEXT("Performance Metrics\nFrame: %.2fms\nStreaming: %.2fms\nLoaded Cells: %d\nMemory: %.1fMB\nBandwidth: %.1fMB/s"),
                                        CurrentData.FrameTime,
                                        CurrentData.StreamingTime,
                                        CurrentData.LoadedCells,
                                        CurrentData.TotalMemoryUsageMB,
                                        CurrentData.StreamingBandwidthMBps);

    DrawDebugText(World, MetricsLocation, MetricsText, MetricsColor, Configuration.DebugTextScale * 1.2f);
}

void UAuracronWorldPartitionDebugManager::EnableWorldPartitionInspector(bool bEnabled)
{
    Configuration.bEnableWorldPartitionInspector = bEnabled;
    Configuration.bShowInspectorWindow = bEnabled;

    if (bEnabled)
    {
        RefreshInspectorData();
        AURACRON_WP_LOG_INFO(TEXT("World Partition Inspector enabled"));
    }
    else
    {
        AURACRON_WP_LOG_INFO(TEXT("World Partition Inspector disabled"));
    }
}

bool UAuracronWorldPartitionDebugManager::IsWorldPartitionInspectorEnabled() const
{
    return Configuration.bEnableWorldPartitionInspector;
}

void UAuracronWorldPartitionDebugManager::RefreshInspectorData()
{
    FScopeLock Lock(&DebugLock);

    InspectorData.Empty();

    // Collect general World Partition info
    InspectorData.Add(TEXT("Total Cells"), FString::Printf(TEXT("%d"), CellDebugInfo.Num()));

    int32 LoadedCells = 0;
    int32 StreamingCells = 0;
    int32 TotalActors = 0;
    float TotalMemory = 0.0f;

    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        if (CellInfo.StreamingState == EAuracronCellStreamingState::Loaded)
        {
            LoadedCells++;
        }
        else if (CellInfo.StreamingState == EAuracronCellStreamingState::Loading ||
                 CellInfo.StreamingState == EAuracronCellStreamingState::Unloading)
        {
            StreamingCells++;
        }

        TotalActors += CellInfo.ActorCount;
        TotalMemory += CellInfo.MemoryUsageMB;
    }

    InspectorData.Add(TEXT("Loaded Cells"), FString::Printf(TEXT("%d"), LoadedCells));
    InspectorData.Add(TEXT("Streaming Cells"), FString::Printf(TEXT("%d"), StreamingCells));
    InspectorData.Add(TEXT("Total Actors"), FString::Printf(TEXT("%d"), TotalActors));
    InspectorData.Add(TEXT("Total Memory"), FString::Printf(TEXT("%.1f MB"), TotalMemory));

    // Add performance data
    if (PerformanceHistory.Num() > 0)
    {
        const FAuracronDebugPerformanceData& PerfData = PerformanceHistory.Last();
        InspectorData.Add(TEXT("Frame Time"), FString::Printf(TEXT("%.2f ms"), PerfData.FrameTime));
        InspectorData.Add(TEXT("Streaming Time"), FString::Printf(TEXT("%.2f ms"), PerfData.StreamingTime));
        InspectorData.Add(TEXT("Streaming Bandwidth"), FString::Printf(TEXT("%.1f MB/s"), PerfData.StreamingBandwidthMBps));
    }

    LastInspectorRefresh = 0.0f;
}

TMap<FString, FString> UAuracronWorldPartitionDebugManager::GetInspectorData() const
{
    FScopeLock Lock(&DebugLock);
    return InspectorData;
}

void UAuracronWorldPartitionDebugManager::DrawInspectorWindow(UWorld* World) const
{
    if (!Configuration.bShowInspectorWindow || !World)
    {
        return;
    }

    FVector WindowLocation = FVector(0, 0, 3000);
    FColor WindowColor = FColor::Cyan;

    FString WindowText = TEXT("World Partition Inspector\n");

    FScopeLock Lock(&DebugLock);
    for (const auto& DataPair : InspectorData)
    {
        WindowText += FString::Printf(TEXT("%s: %s\n"), *DataPair.Key, *DataPair.Value);
    }

    DrawDebugText(World, WindowLocation, WindowText, WindowColor, Configuration.DebugTextScale);
}

void UAuracronWorldPartitionDebugManager::ExecuteDebugCommand(const FString& Command)
{
    if (Command.IsEmpty())
    {
        return;
    }

    ProcessDebugCommand(Command);

    OnDebugEvent.Broadcast(TEXT("Command"), FString::Printf(TEXT("Executed: %s"), *Command));
}

TArray<FString> UAuracronWorldPartitionDebugManager::GetAvailableDebugCommands() const
{
    TArray<FString> Commands;

    Commands.Add(TEXT("wp.debug.enable"));
    Commands.Add(TEXT("wp.debug.disable"));
    Commands.Add(TEXT("wp.debug.mode <mode>"));
    Commands.Add(TEXT("wp.debug.info <level>"));
    Commands.Add(TEXT("wp.debug.colors <scheme>"));
    Commands.Add(TEXT("wp.streaming.log"));
    Commands.Add(TEXT("wp.profiler.start"));
    Commands.Add(TEXT("wp.profiler.stop"));
    Commands.Add(TEXT("wp.inspector.refresh"));
    Commands.Add(TEXT("wp.snapshot.take"));
    Commands.Add(TEXT("wp.data.export"));

    return Commands;
}

FString UAuracronWorldPartitionDebugManager::GetDebugCommandHelp(const FString& Command) const
{
    if (Command == TEXT("wp.debug.enable"))
    {
        return TEXT("Enable debug visualization");
    }
    else if (Command == TEXT("wp.debug.disable"))
    {
        return TEXT("Disable debug visualization");
    }
    else if (Command == TEXT("wp.debug.mode"))
    {
        return TEXT("Set visualization mode: CellBounds, StreamingStates, LoadingProgress, PerformanceMetrics, All");
    }
    else if (Command == TEXT("wp.debug.info"))
    {
        return TEXT("Set info level: Basic, Detailed, Verbose, Expert");
    }
    else if (Command == TEXT("wp.debug.colors"))
    {
        return TEXT("Set color scheme: Default, HighContrast, ColorBlind, Monochrome");
    }
    else if (Command == TEXT("wp.streaming.log"))
    {
        return TEXT("Toggle streaming event logging");
    }
    else if (Command == TEXT("wp.profiler.start"))
    {
        return TEXT("Start performance profiling");
    }
    else if (Command == TEXT("wp.profiler.stop"))
    {
        return TEXT("Stop performance profiling");
    }
    else if (Command == TEXT("wp.inspector.refresh"))
    {
        return TEXT("Refresh inspector data");
    }
    else if (Command == TEXT("wp.snapshot.take"))
    {
        return TEXT("Take debug snapshot");
    }
    else if (Command == TEXT("wp.data.export"))
    {
        return TEXT("Export debug data to file");
    }

    return TEXT("Unknown command");
}

void UAuracronWorldPartitionDebugManager::TakeDebugSnapshot()
{
    FScopeLock Lock(&DebugLock);

    // Collect current debug state
    CollectCellDebugInfo();
    CollectPerformanceData();
    UpdateInspectorData();

    OnDebugEvent.Broadcast(TEXT("Snapshot"), TEXT("Debug snapshot taken"));

    AURACRON_WP_LOG_INFO(TEXT("Debug snapshot taken"));
}

bool UAuracronWorldPartitionDebugManager::SaveDebugSnapshot(const FString& FilePath) const
{
    // In a real implementation, this would serialize debug data to JSON or XML
    FString SnapshotContent = FString::Printf(TEXT("Debug Snapshot\nTimestamp: %s\nCells: %d\nPerformance Samples: %d\n"),
                                            *FDateTime::Now().ToString(),
                                            CellDebugInfo.Num(),
                                            PerformanceHistory.Num());

    return FFileHelper::SaveStringToFile(SnapshotContent, *FilePath);
}

bool UAuracronWorldPartitionDebugManager::LoadDebugSnapshot(const FString& FilePath)
{
    FString SnapshotContent;
    if (FFileHelper::LoadFileToString(SnapshotContent, *FilePath))
    {
        // In a real implementation, this would deserialize debug data
        AURACRON_WP_LOG_INFO(TEXT("Debug snapshot loaded from: %s"), *FilePath);
        return true;
    }

    return false;
}

void UAuracronWorldPartitionDebugManager::ExportDebugData(const FString& FilePath) const
{
    FString ExportContent = TEXT("World Partition Debug Data Export\n");
    ExportContent += FString::Printf(TEXT("Export Time: %s\n\n"), *FDateTime::Now().ToString());

    // Export cell debug info
    ExportContent += TEXT("=== CELL DEBUG INFO ===\n");
    for (const FAuracronDebugCellInfo& CellInfo : CellDebugInfo)
    {
        ExportContent += FString::Printf(TEXT("Cell: %s, State: %s, Actors: %d, Memory: %.1fMB\n"),
                                       *CellInfo.CellId,
                                       *UEnum::GetValueAsString(CellInfo.StreamingState),
                                       CellInfo.ActorCount,
                                       CellInfo.MemoryUsageMB);
    }

    // Export performance history
    ExportContent += TEXT("\n=== PERFORMANCE HISTORY ===\n");
    for (const FAuracronDebugPerformanceData& PerfData : PerformanceHistory)
    {
        ExportContent += FString::Printf(TEXT("Frame: %.2fms, Streaming: %.2fms, Memory: %.1fMB\n"),
                                       PerfData.FrameTime,
                                       PerfData.StreamingTime,
                                       PerfData.TotalMemoryUsageMB);
    }

    // Export streaming events
    ExportContent += TEXT("\n=== STREAMING EVENTS ===\n");
    for (const FString& Event : StreamingEventLog)
    {
        ExportContent += Event + TEXT("\n");
    }

    FFileHelper::SaveStringToFile(ExportContent, *FilePath);

    AURACRON_WP_LOG_INFO(TEXT("Debug data exported to: %s"), *FilePath);
}

void UAuracronWorldPartitionDebugManager::SetConfiguration(const FAuracronDebugConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Debug configuration updated"));
}

FAuracronDebugConfiguration UAuracronWorldPartitionDebugManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionDebugManager::SetDebugInfoLevel(EAuracronDebugInfoLevel Level)
{
    Configuration.InfoLevel = Level;

    AURACRON_WP_LOG_INFO(TEXT("Debug info level set to: %s"), *UEnum::GetValueAsString(Level));
}

EAuracronDebugInfoLevel UAuracronWorldPartitionDebugManager::GetDebugInfoLevel() const
{
    return Configuration.InfoLevel;
}

void UAuracronWorldPartitionDebugManager::SetColorScheme(EAuracronDebugColorScheme Scheme)
{
    Configuration.ColorScheme = Scheme;

    AURACRON_WP_LOG_INFO(TEXT("Debug color scheme set to: %s"), *UEnum::GetValueAsString(Scheme));
}

EAuracronDebugColorScheme UAuracronWorldPartitionDebugManager::GetColorScheme() const
{
    return Configuration.ColorScheme;
}

FColor UAuracronWorldPartitionDebugManager::GetDebugColor(const FString& ColorName) const
{
    // Return colors based on current color scheme
    if (Configuration.ColorScheme == EAuracronDebugColorScheme::HighContrast)
    {
        if (ColorName == TEXT("Loaded")) return FColor::White;
        if (ColorName == TEXT("Loading")) return FColor::Yellow;
        if (ColorName == TEXT("Unloaded")) return FColor::Black;
        if (ColorName == TEXT("Error")) return FColor::Red;
    }
    else if (Configuration.ColorScheme == EAuracronDebugColorScheme::ColorBlind)
    {
        if (ColorName == TEXT("Loaded")) return FColor::Blue;
        if (ColorName == TEXT("Loading")) return FColor::Orange;
        if (ColorName == TEXT("Unloaded")) return FColor(128, 128, 128);
        if (ColorName == TEXT("Error")) return FColor::Magenta;
    }
    else if (Configuration.ColorScheme == EAuracronDebugColorScheme::Monochrome)
    {
        if (ColorName == TEXT("Loaded")) return FColor::White;
        if (ColorName == TEXT("Loading")) return FColor(192, 192, 192);
        if (ColorName == TEXT("Unloaded")) return FColor(64, 64, 64);
        if (ColorName == TEXT("Error")) return FColor::Black;
    }
    else // Default
    {
        if (ColorName == TEXT("Loaded")) return FColor::Green;
        if (ColorName == TEXT("Loading")) return FColor::Yellow;
        if (ColorName == TEXT("Unloaded")) return FColor::Red;
        if (ColorName == TEXT("Error")) return FColor::Magenta;
    }

    return FColor::White;
}

void UAuracronWorldPartitionDebugManager::UpdateDebugData(float DeltaTime)
{
    LastProfilerUpdate += DeltaTime;
    LastInspectorRefresh += DeltaTime;

    // Update profiler data
    if (bIsProfiling && LastProfilerUpdate >= Configuration.ProfilerUpdateInterval)
    {
        CollectPerformanceData();
        LastProfilerUpdate = 0.0f;
    }

    // Update inspector data
    if (Configuration.bAutoRefreshInspector && LastInspectorRefresh >= Configuration.InspectorRefreshRate)
    {
        UpdateInspectorData();
        LastInspectorRefresh = 0.0f;
    }

    // Collect cell debug info
    CollectCellDebugInfo();
}

void UAuracronWorldPartitionDebugManager::CollectCellDebugInfo()
{
    // Get grid manager to collect cell information
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    FScopeLock Lock(&DebugLock);
    CellDebugInfo.Empty();

    // Simulate cell debug info collection
    // In a real implementation, this would query actual cell data
    TArray<FAuracronGridCell> AllCells = GridManager->GetAllCells();

    for (const FAuracronGridCell& Cell : AllCells)
    {
        FAuracronDebugCellInfo DebugInfo;
        DebugInfo.CellId = Cell.CellId;
        DebugInfo.CellBounds = Cell.CellBounds;
        DebugInfo.StreamingState = Cell.StreamingState;
        DebugInfo.LoadingProgress = Cell.LoadingProgress;
        DebugInfo.ActorCount = Cell.ActorCount;
        DebugInfo.MemoryUsageMB = Cell.MemoryUsageMB;
        DebugInfo.DistanceToViewer = Cell.DistanceToViewer;
        DebugInfo.LastAccessTime = Cell.LastAccessTime;

        CellDebugInfo.Add(DebugInfo);
    }
}

void UAuracronWorldPartitionDebugManager::CollectPerformanceData()
{
    FAuracronDebugPerformanceData PerfData;

    // Simulate performance data collection
    // In a real implementation, this would use actual profiling APIs
    PerfData.FrameTime = FMath::RandRange(8.0f, 25.0f);
    PerfData.StreamingTime = FMath::RandRange(1.0f, 8.0f);
    PerfData.LoadingTime = FMath::RandRange(0.5f, 5.0f);
    PerfData.UnloadingTime = FMath::RandRange(0.2f, 2.0f);
    PerfData.LoadedCells = CellDebugInfo.Num();
    PerfData.StreamingCells = FMath::RandRange(0, 5);
    PerfData.TotalMemoryUsageMB = FMath::RandRange(512.0f, 2048.0f);
    PerfData.StreamingBandwidthMBps = FMath::RandRange(10.0f, 100.0f);
    PerfData.TotalActors = FMath::RandRange(1000, 5000);
    PerfData.Timestamp = FDateTime::Now();

    FScopeLock Lock(&DebugLock);
    PerformanceHistory.Add(PerfData);

    // Trim history if too large
    TrimPerformanceHistory();

    // Check for performance alerts
    if (PerfData.FrameTime > 33.0f) // > 30 FPS
    {
        OnPerformanceAlert.Broadcast(PerfData);
    }
}

void UAuracronWorldPartitionDebugManager::UpdateInspectorData()
{
    RefreshInspectorData();
}

void UAuracronWorldPartitionDebugManager::ValidateConfiguration()
{
    // Validate debug distances and scales
    Configuration.DebugDrawDistance = FMath::Max(1000.0f, Configuration.DebugDrawDistance);
    Configuration.DebugTextScale = FMath::Clamp(Configuration.DebugTextScale, 0.1f, 5.0f);

    // Validate profiler settings
    Configuration.ProfilerUpdateInterval = FMath::Max(0.1f, Configuration.ProfilerUpdateInterval);
    Configuration.MaxProfilerSamples = FMath::Max(10, Configuration.MaxProfilerSamples);

    // Validate inspector settings
    Configuration.InspectorRefreshRate = FMath::Max(0.5f, Configuration.InspectorRefreshRate);
}

FColor UAuracronWorldPartitionDebugManager::GetColorForStreamingState(EAuracronCellStreamingState State) const
{
    switch (State)
    {
        case EAuracronCellStreamingState::Loaded:
            return GetDebugColor(TEXT("Loaded"));
        case EAuracronCellStreamingState::Loading:
        case EAuracronCellStreamingState::Unloading:
            return GetDebugColor(TEXT("Loading"));
        case EAuracronCellStreamingState::Unloaded:
            return GetDebugColor(TEXT("Unloaded"));
        case EAuracronCellStreamingState::Failed:
            return GetDebugColor(TEXT("Error"));
        default:
            return FColor::White;
    }
}

FColor UAuracronWorldPartitionDebugManager::GetColorForLoadingProgress(float Progress) const
{
    // Interpolate between red and green based on progress
    float Red = (1.0f - Progress) * 255.0f;
    float Green = Progress * 255.0f;

    return FColor(static_cast<uint8>(Red), static_cast<uint8>(Green), 0, 255);
}

void UAuracronWorldPartitionDebugManager::DrawDebugText(UWorld* World, const FVector& Location, const FString& Text, const FColor& Color, float Scale) const
{
    if (!World)
    {
        return;
    }

    DrawDebugString(World, Location, Text, nullptr, Color, -1.0f, true, Scale);
}

void UAuracronWorldPartitionDebugManager::ProcessDebugCommand(const FString& Command)
{
    TArray<FString> CommandParts;
    Command.ParseIntoArray(CommandParts, TEXT(" "), true);

    if (CommandParts.Num() == 0)
    {
        return;
    }

    FString MainCommand = CommandParts[0].ToLower();

    if (MainCommand == TEXT("wp.debug.enable"))
    {
        EnableDebugVisualization(true);
    }
    else if (MainCommand == TEXT("wp.debug.disable"))
    {
        EnableDebugVisualization(false);
    }
    else if (MainCommand == TEXT("wp.debug.mode") && CommandParts.Num() > 1)
    {
        FString Mode = CommandParts[1].ToLower();
        if (Mode == TEXT("cellbounds"))
        {
            SetVisualizationMode(EAuracronDebugVisualizationMode::CellBounds);
        }
        else if (Mode == TEXT("streamingstates"))
        {
            SetVisualizationMode(EAuracronDebugVisualizationMode::StreamingStates);
        }
        else if (Mode == TEXT("loadingprogress"))
        {
            SetVisualizationMode(EAuracronDebugVisualizationMode::LoadingProgress);
        }
        else if (Mode == TEXT("performancemetrics"))
        {
            SetVisualizationMode(EAuracronDebugVisualizationMode::PerformanceMetrics);
        }
        else if (Mode == TEXT("all"))
        {
            SetVisualizationMode(EAuracronDebugVisualizationMode::All);
        }
    }
    else if (MainCommand == TEXT("wp.streaming.log"))
    {
        Configuration.bLogStreamingEvents = !Configuration.bLogStreamingEvents;
    }
    else if (MainCommand == TEXT("wp.profiler.start"))
    {
        StartProfiling();
    }
    else if (MainCommand == TEXT("wp.profiler.stop"))
    {
        StopProfiling();
    }
    else if (MainCommand == TEXT("wp.inspector.refresh"))
    {
        RefreshInspectorData();
    }
    else if (MainCommand == TEXT("wp.snapshot.take"))
    {
        TakeDebugSnapshot();
    }
    else if (MainCommand == TEXT("wp.data.export"))
    {
        FString FilePath = TEXT("DebugData_") + FDateTime::Now().ToString() + TEXT(".txt");
        ExportDebugData(FilePath);
    }
}

void UAuracronWorldPartitionDebugManager::TrimPerformanceHistory()
{
    if (PerformanceHistory.Num() > Configuration.MaxProfilerSamples)
    {
        int32 ExcessCount = PerformanceHistory.Num() - Configuration.MaxProfilerSamples;
        PerformanceHistory.RemoveAt(0, ExcessCount);
    }
}

void UAuracronWorldPartitionDebugManager::TrimStreamingEventLog()
{
    const int32 MaxLogEntries = 1000;
    if (StreamingEventLog.Num() > MaxLogEntries)
    {
        int32 ExcessCount = StreamingEventLog.Num() - MaxLogEntries;
        StreamingEventLog.RemoveAt(0, ExcessCount);
    }
}
