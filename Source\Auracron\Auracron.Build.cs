﻿// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class Auracron : ModuleRules
{
	public Auracron(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
	
		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"EnhancedInput",
			"AuracronMetaHumanBridge",
			"AuracronAnalyticsBridge",
			"AuracronAudioBridge",
			"AuracronVFXBridge",
			"AuracronPhysicsBridge",
			"AuracronNaniteBridge",
			"AuracronVoiceBridge",
			"AuracronAntiCheatBridge",
			"AuracronTutorialBridge",
			"AuracronLoreBridge",
			"AuracronMonetizationBridge",
			"AuracronEOSBridge",
			"AuracronCombatBridge",
			"AuracronChampionsBridge",
			"AuracronNetworkingBridge",
			"AuracronProgressionBridge",
			"AuracronRealmsBridge",
			"AuracronSigilosBridge",
			"AuracronUIBridge",
			"AuracronMetaHumanFramework"
		});

		PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "AuracronFoliageBridge",
                "AuracronPCGBridge",
                "AuracronLumenBridge",
                "AuracronWorldPartitionBridge",
                "AuracronAbismoUmbrioBridge",
                "AuracronAdaptiveCreaturesBridge",
                "AuracronVerticalTransitionsBridge",
                "AuracronQABridge"
            }
        );

		// Uncomment if you are using Slate UI
		// PrivateDependencyModuleNames.AddRange(new string[] { "Slate", "SlateCore" });
		
		// Uncomment if you are using online features
		// PrivateDependencyModuleNames.Add("OnlineSubsystem");

		// To include OnlineSubsystemSteam, add it to the plugins section in your uproject file with the Enabled attribute set to true
	}
}

