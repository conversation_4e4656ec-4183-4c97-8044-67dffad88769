#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "Engine/DataTable.h"
#include "AuracronHarmonyEngineBridge.h"
#include "EmotionalIntelligenceComponent.generated.h"

class APlayerController;
class UAbilitySystemComponent;

/**
 * Emotional Intelligence Component
 * Advanced AI system that monitors player emotional state and provides predictive intervention
 */
UCLASS(BlueprintType, Blueprintable, ClassGroup=(HarmonyEngine), meta=(BlueprintSpawnableComponent))
class AURACRONHARMONYENGINEBRIDGE_API UEmotionalIntelligenceComponent : public UActorComponent
{
    GENERATED_BODY()

public:
    UEmotionalIntelligenceComponent();

    // UActorComponent interface
    virtual void BeginPlay() override;
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    // Core Emotional Intelligence Functions
    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    void StartEmotionalMonitoring(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    void StopEmotionalMonitoring(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    EEmotionalState AnalyzeCurrentEmotionalState(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    float PredictFrustrationLevel(const FString& PlayerID, float TimeHorizon = 60.0f);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    bool DetectEmotionalEscalation(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    void ProvideEmotionalSupport(const FString& PlayerID, const FString& SupportMessage);

    // Behavioral Pattern Analysis
    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    TArray<FString> GetEmotionalTriggers(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    float CalculateEmotionalStability(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    bool IsPlayerInOptimalState(const FString& PlayerID);

    // Intervention Recommendations
    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    FHarmonyInterventionData RecommendIntervention(const FString& PlayerID);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    TArray<FString> GenerateSupportMessages(EEmotionalState EmotionalState);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    bool ShouldSuggestBreak(const FString& PlayerID);

    // Machine Learning Integration
    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    void UpdateMLModel(const FPlayerBehaviorSnapshot& TrainingData);

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    void TrainEmotionalPredictionModel();

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    float GetModelAccuracy() const;

    UFUNCTION(BlueprintCallable, Category = "Emotional Intelligence")
    EEmotionalState GetCurrentEmotionalState(const FString& PlayerID) const;

protected:
    // Configuration
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    float MonitoringInterval;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    float FrustrationThreshold;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    float EscalationDetectionWindow;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    int32 MaxSupportMessagesPerSession;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    bool bEnablePredictiveAnalysis;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Emotional Intelligence Config")
    bool bEnableMLTraining;

    // Data Tables
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Data")
    TObjectPtr<UDataTable> EmotionalPatternsDataTable;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Data")
    TObjectPtr<UDataTable> SupportMessagesDataTable;

    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Data")
    TObjectPtr<UDataTable> InterventionStrategiesDataTable;

    // Runtime Data
    UPROPERTY()
    TMap<FString, FPlayerBehaviorSnapshot> PlayerEmotionalHistory;

    UPROPERTY()
    TMap<FString, EEmotionalState> CurrentEmotionalStates;

    UPROPERTY()
    TMap<FString, float> PlayerFrustrationLevels;

    UPROPERTY()
    TMap<FString, FDateTime> LastInterventionTimes;

    UPROPERTY()
    TMap<FString, int32> SupportMessagesCount;

    // ML Model Data
    UPROPERTY()
    TArray<FPlayerBehaviorSnapshot> TrainingDataset;

    UPROPERTY()
    float CurrentModelAccuracy;

    UPROPERTY()
    int32 TrainingIterations;

    // Timers
    FTimerHandle EmotionalAnalysisTimer;
    FTimerHandle MLTrainingTimer;
    FTimerHandle DataPersistenceTimer;

private:
    // Internal analysis functions
    void PerformEmotionalAnalysis();
    void UpdateEmotionalStates();
    void ProcessEmotionalTrends();
    
    // Pattern recognition
    bool AnalyzeGameplayPatterns(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData);
    bool DetectCommunicationPatterns(const FString& PlayerID);
    bool AnalyzePerformancePatterns(const FString& PlayerID);
    
    // Prediction algorithms
    float PredictEmotionalTrajectory(const FString& PlayerID, float TimeHorizon);
    EEmotionalState PredictNextEmotionalState(const FString& PlayerID);
    float CalculateInterventionUrgency(const FString& PlayerID);
    
    // Support system
    FString SelectOptimalSupportMessage(EEmotionalState EmotionalState, const FString& PlayerID);
    void DeliverEmotionalSupport(const FString& PlayerID, const FString& Message);
    bool ValidateSupportMessageFrequency(const FString& PlayerID);
    
    // Machine learning helpers
    void CollectTrainingData(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData);
    void ProcessMLTrainingBatch();
    void ValidateMLModelPerformance();
    void UpdateMLModelWeights();
    
    // Data persistence
    void SaveEmotionalData();
    void LoadEmotionalData();
    void CleanupOldData();
    
    // Utility functions
    float CalculateEmotionalDistance(EEmotionalState State1, EEmotionalState State2);
    bool IsEmotionalStateStable(const FString& PlayerID, float TimeWindow = 300.0f);
    float GetEmotionalVolatility(const FString& PlayerID);
    
    // Configuration validation
    bool ValidateConfiguration();
    void InitializeDefaultConfiguration();
};
