/**
 * Auracron Sigil Effects - Specific implementations for each Sigil type
 * 
 * Contains the specific GameplayEffect implementations for all 16 Sigil subtypes
 * Uses UE 5.6 Gameplay Ability System APIs for robust effect management
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemGlobals.h"
#include "GameplayEffectExtension.h"
#include "GameplayEffectTypes.h"
#include "GameplayEffectAttributeCaptureDefinition.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "AuracronSigilosBridge.h"
#include "AuracronSigilEffects.generated.h"

/**
 * Attribute Set for Auracron Sigil System
 * Defines all attributes that can be modified by Sigils
 */
UCLASS(BlueprintType)
class AURACRONSIGILOSBRIDGE_API UAuracronSigilAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    UAuracronSigilAttributeSet();

protected:
    // === Core Attributes ===

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilPower)
    FGameplayAttributeData SigilPower;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilDuration)
    FGameplayAttributeData SigilDuration;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilCooldownReduction)
    FGameplayAttributeData SigilCooldownReduction;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_FusionEnergy)
    FGameplayAttributeData FusionEnergy;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_ArchetypeLevel)
    FGameplayAttributeData ArchetypeLevel;

    // === Aegis Attributes ===

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_ShieldStrength)
    FGameplayAttributeData ShieldStrength;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_DamageReflection)
    FGameplayAttributeData DamageReflection;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_TimeDistortion)
    FGameplayAttributeData TimeDistortion;

    // === Ruin Attributes ===

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_ElementalDamage)
    FGameplayAttributeData ElementalDamage;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_ArmorPenetration)
    FGameplayAttributeData ArmorPenetration;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_CriticalChance)
    FGameplayAttributeData CriticalChance;

    // === Vesper Attributes ===

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_HealingPower)
    FGameplayAttributeData HealingPower;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_MovementSpeed)
    FGameplayAttributeData MovementSpeed;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_VisionRange)
    FGameplayAttributeData VisionRange;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_TeleportRange)
    FGameplayAttributeData TeleportRange;

public:
    // === Helper Functions for Attributes (Following UE 5.6 Official Documentation) ===

    // Core Attributes Helper Functions
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, SigilPower);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(SigilPower);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(SigilPower);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(SigilPower);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, SigilDuration);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(SigilDuration);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(SigilDuration);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(SigilDuration);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, SigilCooldownReduction);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(SigilCooldownReduction);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(SigilCooldownReduction);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(SigilCooldownReduction);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, FusionEnergy);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(FusionEnergy);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(FusionEnergy);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(FusionEnergy);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, ArchetypeLevel);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(ArchetypeLevel);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(ArchetypeLevel);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(ArchetypeLevel);

    // Aegis Attributes Helper Functions
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, ShieldStrength);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(ShieldStrength);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(ShieldStrength);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(ShieldStrength);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, DamageReflection);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(DamageReflection);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(DamageReflection);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(DamageReflection);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, TimeDistortion);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(TimeDistortion);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(TimeDistortion);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(TimeDistortion);

    // Ruin Attributes Helper Functions
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, ElementalDamage);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(ElementalDamage);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(ElementalDamage);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(ElementalDamage);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, ArmorPenetration);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(ArmorPenetration);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(ArmorPenetration);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(ArmorPenetration);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, CriticalChance);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(CriticalChance);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(CriticalChance);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(CriticalChance);

    // Vesper Attributes Helper Functions
    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, HealingPower);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(HealingPower);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(HealingPower);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(HealingPower);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, MovementSpeed);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(MovementSpeed);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(MovementSpeed);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(MovementSpeed);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, VisionRange);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(VisionRange);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(VisionRange);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(VisionRange);

    GAMEPLAYATTRIBUTE_PROPERTY_GETTER(UAuracronSigilAttributeSet, TeleportRange);
    GAMEPLAYATTRIBUTE_VALUE_GETTER(TeleportRange);
    GAMEPLAYATTRIBUTE_VALUE_SETTER(TeleportRange);
    GAMEPLAYATTRIBUTE_VALUE_INITTER(TeleportRange);

    // === Replication Functions ===

    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
    // === Replication Callbacks ===
    
    UFUNCTION()
    virtual void OnRep_SigilPower(const FGameplayAttributeData& OldSigilPower);

    UFUNCTION()
    virtual void OnRep_SigilDuration(const FGameplayAttributeData& OldSigilDuration);

    UFUNCTION()
    virtual void OnRep_SigilCooldownReduction(const FGameplayAttributeData& OldSigilCooldownReduction);

    UFUNCTION()
    virtual void OnRep_FusionEnergy(const FGameplayAttributeData& OldFusionEnergy);

    UFUNCTION()
    virtual void OnRep_ArchetypeLevel(const FGameplayAttributeData& OldArchetypeLevel);

    UFUNCTION()
    virtual void OnRep_ShieldStrength(const FGameplayAttributeData& OldShieldStrength);

    UFUNCTION()
    virtual void OnRep_DamageReflection(const FGameplayAttributeData& OldDamageReflection);

    UFUNCTION()
    virtual void OnRep_TimeDistortion(const FGameplayAttributeData& OldTimeDistortion);

    UFUNCTION()
    virtual void OnRep_ElementalDamage(const FGameplayAttributeData& OldElementalDamage);

    UFUNCTION()
    virtual void OnRep_ArmorPenetration(const FGameplayAttributeData& OldArmorPenetration);

    UFUNCTION()
    virtual void OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance);

    UFUNCTION()
    virtual void OnRep_HealingPower(const FGameplayAttributeData& OldHealingPower);

    UFUNCTION()
    virtual void OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed);

    UFUNCTION()
    virtual void OnRep_VisionRange(const FGameplayAttributeData& OldVisionRange);

    UFUNCTION()
    virtual void OnRep_TeleportRange(const FGameplayAttributeData& OldTeleportRange);


};

/**
 * Base GameplayAbility for Auracron Sigils
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class AURACRONSIGILOSBRIDGE_API UAuracronSigilAbility : public UGameplayAbility
{
    GENERATED_BODY()

public:
    UAuracronSigilAbility();

protected:
    /** Type of Sigil this ability belongs to */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    /** Power scaling factor */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float PowerScaling = 1.0f;

    /** Base duration of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float BaseDuration = 5.0f;

    /** Base cooldown of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "1.0", ClampMax = "180.0"))
    float BaseCooldown = 30.0f;

    /** Mana cost of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.0", ClampMax = "500.0"))
    float ManaCost = 50.0f;

    /** Visual effects for this ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    TSoftObjectPtr<UNiagaraSystem> AbilityVFX;

    /** Audio effects for this ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    TSoftObjectPtr<UMetaSoundSource> AbilityAudio;

    // === UGameplayAbility Interface ===
    
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const override;

    // === Sigil-specific Implementation ===
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Sigil Ability")
    void OnSigilActivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Sigil Ability")
    void OnSigilDeactivated();

    UFUNCTION(BlueprintNativeEvent, Category = "Sigil Ability")
    void ApplySigilEffect();
    virtual void ApplySigilEffect_Implementation();
};
