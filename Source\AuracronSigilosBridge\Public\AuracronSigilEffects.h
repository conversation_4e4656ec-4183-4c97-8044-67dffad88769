/**
 * Auracron Sigil Effects - Specific implementations for each Sigil type
 * 
 * Contains the specific GameplayEffect implementations for all 16 Sigil subtypes
 * Uses UE 5.6 Gameplay Ability System APIs for robust effect management
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

#pragma once

#include "CoreMinimal.h"
#include "AttributeSet.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemGlobals.h"
#include "GameplayEffectExtension.h"
#include "GameplayEffectTypes.h"
#include "GameplayEffectAttributeCaptureDefinition.h"
#include "Net/UnrealNetwork.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayEffectTypes.h"
#include "AuracronSigilosBridge.h"
#include "AuracronSigilEffects.generated.h"

/**
 * Attribute Set for Auracron Sigil System
 * Defines all attributes that can be modified by Sigils
 */
UCLASS(BlueprintType)
class AURACRONSIGILOSBRIDGE_API UAuracronSigilAttributeSet : public UAttributeSet
{
    GENERATED_BODY()

public:
    UAuracronSigilAttributeSet();

    // === Core Attributes ===
    
    UPROPERTY(BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilPower)
    FGameplayAttributeData SigilPower;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, SigilPower)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilDuration)
    FGameplayAttributeData SigilDuration;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, SigilDuration)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_SigilCooldownReduction)
    FGameplayAttributeData SigilCooldownReduction;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, SigilCooldownReduction)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_FusionEnergy)
    FGameplayAttributeData FusionEnergy;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, FusionEnergy)

    UPROPERTY(BlueprintReadOnly, Category = "Sigil Attributes", ReplicatedUsing = OnRep_ArchetypeLevel)
    FGameplayAttributeData ArchetypeLevel;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, ArchetypeLevel)

    // === Aegis Attributes ===

    UPROPERTY(BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_ShieldStrength)
    FGameplayAttributeData ShieldStrength;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, ShieldStrength)

    UPROPERTY(BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_DamageReflection)
    FGameplayAttributeData DamageReflection;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, DamageReflection)

    UPROPERTY(BlueprintReadOnly, Category = "Aegis Attributes", ReplicatedUsing = OnRep_TimeDistortion)
    FGameplayAttributeData TimeDistortion;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, TimeDistortion)

    // === Ruin Attributes ===

    UPROPERTY(BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_ElementalDamage)
    FGameplayAttributeData ElementalDamage;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, ElementalDamage)

    UPROPERTY(BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_ArmorPenetration)
    FGameplayAttributeData ArmorPenetration;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, ArmorPenetration)

    UPROPERTY(BlueprintReadOnly, Category = "Ruin Attributes", ReplicatedUsing = OnRep_CriticalChance)
    FGameplayAttributeData CriticalChance;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, CriticalChance)

    // === Vesper Attributes ===

    UPROPERTY(BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_HealingPower)
    FGameplayAttributeData HealingPower;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, HealingPower)

    UPROPERTY(BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_MovementSpeed)
    FGameplayAttributeData MovementSpeed;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, MovementSpeed)

    UPROPERTY(BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_VisionRange)
    FGameplayAttributeData VisionRange;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, VisionRange)

    UPROPERTY(BlueprintReadOnly, Category = "Vesper Attributes", ReplicatedUsing = OnRep_TeleportRange)
    FGameplayAttributeData TeleportRange;
    ATTRIBUTE_ACCESSORS(UAuracronSigilAttributeSet, TeleportRange)

    // === Replication Functions ===
    
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
    // === Replication Callbacks ===
    
    UFUNCTION()
    virtual void OnRep_SigilPower(const FGameplayAttributeData& OldSigilPower);

    UFUNCTION()
    virtual void OnRep_SigilDuration(const FGameplayAttributeData& OldSigilDuration);

    UFUNCTION()
    virtual void OnRep_SigilCooldownReduction(const FGameplayAttributeData& OldSigilCooldownReduction);

    UFUNCTION()
    virtual void OnRep_FusionEnergy(const FGameplayAttributeData& OldFusionEnergy);

    UFUNCTION()
    virtual void OnRep_ArchetypeLevel(const FGameplayAttributeData& OldArchetypeLevel);

    UFUNCTION()
    virtual void OnRep_ShieldStrength(const FGameplayAttributeData& OldShieldStrength);

    UFUNCTION()
    virtual void OnRep_DamageReflection(const FGameplayAttributeData& OldDamageReflection);

    UFUNCTION()
    virtual void OnRep_TimeDistortion(const FGameplayAttributeData& OldTimeDistortion);

    UFUNCTION()
    virtual void OnRep_ElementalDamage(const FGameplayAttributeData& OldElementalDamage);

    UFUNCTION()
    virtual void OnRep_ArmorPenetration(const FGameplayAttributeData& OldArmorPenetration);

    UFUNCTION()
    virtual void OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance);

    UFUNCTION()
    virtual void OnRep_HealingPower(const FGameplayAttributeData& OldHealingPower);

    UFUNCTION()
    virtual void OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed);

    UFUNCTION()
    virtual void OnRep_VisionRange(const FGameplayAttributeData& OldVisionRange);

    UFUNCTION()
    virtual void OnRep_TeleportRange(const FGameplayAttributeData& OldTeleportRange);


};

/**
 * Base GameplayAbility for Auracron Sigils
 */
UCLASS(Abstract, BlueprintType, Blueprintable)
class AURACRONSIGILOSBRIDGE_API UAuracronSigilAbility : public UGameplayAbility
{
    GENERATED_BODY()

public:
    UAuracronSigilAbility();

protected:
    /** Type of Sigil this ability belongs to */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    EAuracronSigiloType SigiloType = EAuracronSigiloType::None;

    /** Power scaling factor */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.1", ClampMax = "5.0"))
    float PowerScaling = 1.0f;

    /** Base duration of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.1", ClampMax = "30.0"))
    float BaseDuration = 5.0f;

    /** Base cooldown of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "1.0", ClampMax = "180.0"))
    float BaseCooldown = 30.0f;

    /** Mana cost of the ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability", meta = (ClampMin = "0.0", ClampMax = "500.0"))
    float ManaCost = 50.0f;

    /** Visual effects for this ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    TSoftObjectPtr<UNiagaraSystem> AbilityVFX;

    /** Audio effects for this ability */
    UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Sigil Ability")
    TSoftObjectPtr<UMetaSoundSource> AbilityAudio;

    // === UGameplayAbility Interface ===
    
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;
    virtual bool CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const override;

    // === Sigil-specific Implementation ===
    
    UFUNCTION(BlueprintImplementableEvent, Category = "Sigil Ability")
    void OnSigilActivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Sigil Ability")
    void OnSigilDeactivated();

    UFUNCTION(BlueprintNativeEvent, Category = "Sigil Ability")
    void ApplySigilEffect();
    virtual void ApplySigilEffect_Implementation();
};
