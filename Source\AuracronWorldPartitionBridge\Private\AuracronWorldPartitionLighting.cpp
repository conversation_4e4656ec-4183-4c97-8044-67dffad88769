// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Lighting Integration Implementation
// Bridge 3.10: World Partition - Lighting Integration

#include "AuracronWorldPartitionLighting.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// Lighting includes
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/SkyLight.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"

// =============================================================================
// LIGHTING STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronLightingStatistics::UpdateCalculatedFields()
{
    if (TotalLightSources > 0)
    {
        LightingEfficiency = static_cast<float>(LoadedLightSources) / static_cast<float>(TotalLightSources);
    }
    else
    {
        LightingEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION LIGHTING MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionLightingManager* UAuracronWorldPartitionLightingManager::Instance = nullptr;

UAuracronWorldPartitionLightingManager* UAuracronWorldPartitionLightingManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionLightingManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionLightingManager::Initialize(const FAuracronLightingConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Lighting Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronLightingStatistics();
    
    // Clear collections
    LightingDescriptors.Empty();
    LightComponents.Empty();
    LightingToCellMap.Empty();
    CellToLightingMap.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Lighting Manager initialized with streaming distance: %.1fm"), Configuration.LightingStreamingDistance);
}

void UAuracronWorldPartitionLightingManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Deactivate and unload all light sources
    TArray<FString> LightsToUnload;
    LightingDescriptors.GenerateKeyArray(LightsToUnload);
    
    for (const FString& LightingId : LightsToUnload)
    {
        DeactivateLightSource(LightingId);
        UnloadLightSource(LightingId);
    }
    
    // Clear all data
    LightingDescriptors.Empty();
    LightComponents.Empty();
    LightingToCellMap.Empty();
    CellToLightingMap.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Lighting Manager shutdown completed"));
}

bool UAuracronWorldPartitionLightingManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionLightingManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update statistics
    UpdateStatistics();
}

FString UAuracronWorldPartitionLightingManager::CreateLightSource(const FString& SourceActorId, EAuracronLightType LightType, const FVector& Location)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot create light source: Manager not initialized"));
        return FString();
    }

    if (SourceActorId.IsEmpty())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot create light source: Source actor ID is empty"));
        return FString();
    }

    FScopeLock Lock(&LightingLock);
    
    FString LightingId = GenerateLightingId(SourceActorId);
    
    // Check if light source already exists
    if (LightingDescriptors.Contains(LightingId))
    {
        AURACRON_WP_LOG_WARNING(TEXT("Light source already exists: %s"), *LightingId);
        return LightingId;
    }
    
    // Create lighting descriptor
    FAuracronLightingDescriptor LightingDesc;
    LightingDesc.LightingId = LightingId;
    LightingDesc.LightingName = FString::Printf(TEXT("Light_%s"), *SourceActorId);
    LightingDesc.SourceActorId = SourceActorId;
    LightingDesc.LightType = LightType;
    LightingDesc.LightMobility = EAuracronLightMobility::Movable;
    LightingDesc.StreamingState = EAuracronLightingStreamingState::Unloaded;
    LightingDesc.CurrentLODLevel = EAuracronLightingLODLevel::LOD0;
    LightingDesc.Location = Location;
    LightingDesc.CreationTime = FDateTime::Now();
    LightingDesc.LastAccessTime = LightingDesc.CreationTime;
    
    // Set default properties based on light type
    switch (LightType)
    {
        case EAuracronLightType::Directional:
            LightingDesc.Intensity = 3.0f;
            LightingDesc.AttenuationRadius = 0.0f; // Infinite for directional
            LightingDesc.LightMobility = EAuracronLightMobility::Movable;
            break;
        case EAuracronLightType::Point:
            LightingDesc.Intensity = 1000.0f;
            LightingDesc.AttenuationRadius = 1000.0f;
            break;
        case EAuracronLightType::Spot:
            LightingDesc.Intensity = 1000.0f;
            LightingDesc.AttenuationRadius = 1000.0f;
            break;
        case EAuracronLightType::Sky:
            LightingDesc.Intensity = 1.0f;
            LightingDesc.AttenuationRadius = 0.0f; // Infinite for sky
            LightingDesc.LightMobility = EAuracronLightMobility::Movable;
            break;
        case EAuracronLightType::Area:
            LightingDesc.Intensity = 500.0f;
            LightingDesc.AttenuationRadius = 500.0f;
            break;
        case EAuracronLightType::Volumetric:
            LightingDesc.Intensity = 200.0f;
            LightingDesc.AttenuationRadius = 2000.0f;
            break;
    }
    
    // Add to collections
    LightingDescriptors.Add(LightingId, LightingDesc);
    
    // Update cell mapping
    UpdateLightingCellMapping(LightingId);
    
    // Create light component if needed
    if (Configuration.bEnableLightingStreaming)
    {
        CreateLightComponent(LightingId, LightType);
    }
    
    AURACRON_WP_LOG_INFO(TEXT("Light source created: %s (Type: %s)"), *LightingId, *UEnum::GetValueAsString(LightType));
    
    return LightingId;
}

bool UAuracronWorldPartitionLightingManager::RemoveLightSource(const FString& LightingId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);
    
    if (!LightingDescriptors.Contains(LightingId))
    {
        return false;
    }
    
    // Deactivate and unload light source
    DeactivateLightSource(LightingId);
    UnloadLightSource(LightingId);
    
    // Remove from cell mapping
    if (FString* CellId = LightingToCellMap.Find(LightingId))
    {
        if (TSet<FString>* CellLights = CellToLightingMap.Find(*CellId))
        {
            CellLights->Remove(LightingId);
        }
        LightingToCellMap.Remove(LightingId);
    }
    
    // Remove from collections
    LightingDescriptors.Remove(LightingId);
    LightComponents.Remove(LightingId);
    
    AURACRON_WP_LOG_INFO(TEXT("Light source removed: %s"), *LightingId);
    
    return true;
}

FAuracronLightingDescriptor UAuracronWorldPartitionLightingManager::GetLightingDescriptor(const FString& LightingId) const
{
    FScopeLock Lock(&LightingLock);
    
    const FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (LightingDesc)
    {
        return *LightingDesc;
    }
    
    return FAuracronLightingDescriptor();
}

TArray<FAuracronLightingDescriptor> UAuracronWorldPartitionLightingManager::GetAllLightSources() const
{
    FScopeLock Lock(&LightingLock);
    
    TArray<FAuracronLightingDescriptor> AllLights;
    LightingDescriptors.GenerateValueArray(AllLights);
    
    return AllLights;
}

TArray<FString> UAuracronWorldPartitionLightingManager::GetLightingIds() const
{
    FScopeLock Lock(&LightingLock);
    
    TArray<FString> LightingIds;
    LightingDescriptors.GenerateKeyArray(LightingIds);
    
    return LightingIds;
}

bool UAuracronWorldPartitionLightingManager::DoesLightSourceExist(const FString& LightingId) const
{
    FScopeLock Lock(&LightingLock);
    return LightingDescriptors.Contains(LightingId);
}

bool UAuracronWorldPartitionLightingManager::LoadLightSource(const FString& LightingId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);
    
    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }
    
    if (LightingDesc->StreamingState == EAuracronLightingStreamingState::Loaded ||
        LightingDesc->StreamingState == EAuracronLightingStreamingState::Active)
    {
        return true; // Already loaded
    }
    
    // Set loading state
    LightingDesc->StreamingState = EAuracronLightingStreamingState::Loading;
    LightingDesc->LastAccessTime = FDateTime::Now();
    
    // Real implementation using UE5.6's lighting system
    bool bLoadSuccess = false;

    // Create the actual light component based on type
    if (CreateLightComponent(LightingId, LightingDesc->LightType))
    {
        bLoadSuccess = true;
    }
    else
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to create light component for: %s"), *LightingId);
    }
    
    if (bLoadSuccess)
    {
        LightingDesc->StreamingState = EAuracronLightingStreamingState::Loaded;
        
        // Calculate memory usage based on light type and quality
        float BaseMemory = 1.0f; // 1MB base
        switch (LightingDesc->LightType)
        {
            case EAuracronLightType::Directional:
                BaseMemory *= 2.0f; // Directional lights use more memory for cascaded shadows
                break;
            case EAuracronLightType::Sky:
                BaseMemory *= 3.0f; // Sky lights use more memory for environment maps
                break;
            case EAuracronLightType::Volumetric:
                BaseMemory *= 4.0f; // Volumetric lights are memory intensive
                break;
            default:
                BaseMemory *= 1.0f;
                break;
        }
        
        // Apply LOD multiplier
        float LODMultiplier = 1.0f - (static_cast<int32>(LightingDesc->CurrentLODLevel) * 0.15f);
        LightingDesc->MemoryUsageMB = BaseMemory * LODMultiplier;
        
        OnLightingLoadedInternal(LightingId, true);
        
        AURACRON_WP_LOG_INFO(TEXT("Light source loaded: %s (%.1fMB)"), *LightingId, LightingDesc->MemoryUsageMB);
        return true;
    }
    else
    {
        LightingDesc->StreamingState = EAuracronLightingStreamingState::Failed;
        
        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }
        
        OnLightingLoadedInternal(LightingId, false);
        
        AURACRON_WP_LOG_ERROR(TEXT("Failed to load light source: %s"), *LightingId);
        return false;
    }
}

bool UAuracronWorldPartitionLightingManager::UnloadLightSource(const FString& LightingId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    if (LightingDesc->StreamingState == EAuracronLightingStreamingState::Unloaded)
    {
        return true; // Already unloaded
    }

    // Deactivate light if active
    if (LightingDesc->StreamingState == EAuracronLightingStreamingState::Active)
    {
        DeactivateLightSource(LightingId);
    }

    // Set unloading state
    LightingDesc->StreamingState = EAuracronLightingStreamingState::Unloading;

    // Remove light component reference
    if (TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId))
    {
        if (ULightComponent* Component = ComponentRef->Get())
        {
            Component->SetVisibility(false);
            Component->DestroyComponent();
        }
        LightComponents.Remove(LightingId);
    }

    LightingDesc->StreamingState = EAuracronLightingStreamingState::Unloaded;
    LightingDesc->MemoryUsageMB = 0.0f;

    OnLightingUnloadedInternal(LightingId);

    AURACRON_WP_LOG_INFO(TEXT("Light source unloaded: %s"), *LightingId);

    return true;
}

EAuracronLightingStreamingState UAuracronWorldPartitionLightingManager::GetLightingStreamingState(const FString& LightingId) const
{
    FScopeLock Lock(&LightingLock);

    const FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (LightingDesc)
    {
        return LightingDesc->StreamingState;
    }

    return EAuracronLightingStreamingState::Unloaded;
}

TArray<FString> UAuracronWorldPartitionLightingManager::GetLoadedLightSources() const
{
    FScopeLock Lock(&LightingLock);

    TArray<FString> LoadedLights;

    for (const auto& LightingPair : LightingDescriptors)
    {
        if (LightingPair.Value.StreamingState == EAuracronLightingStreamingState::Loaded ||
            LightingPair.Value.StreamingState == EAuracronLightingStreamingState::Active)
        {
            LoadedLights.Add(LightingPair.Key);
        }
    }

    return LoadedLights;
}

TArray<FString> UAuracronWorldPartitionLightingManager::GetStreamingLightSources() const
{
    FScopeLock Lock(&LightingLock);

    TArray<FString> StreamingLights;

    for (const auto& LightingPair : LightingDescriptors)
    {
        if (LightingPair.Value.StreamingState == EAuracronLightingStreamingState::Loading ||
            LightingPair.Value.StreamingState == EAuracronLightingStreamingState::Unloading)
        {
            StreamingLights.Add(LightingPair.Key);
        }
    }

    return StreamingLights;
}

bool UAuracronWorldPartitionLightingManager::ActivateLightSource(const FString& LightingId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    // Load light if not loaded
    if (LightingDesc->StreamingState == EAuracronLightingStreamingState::Unloaded)
    {
        if (!LoadLightSource(LightingId))
        {
            return false;
        }
    }

    if (LightingDesc->StreamingState != EAuracronLightingStreamingState::Loaded)
    {
        return false;
    }

    // Get light component
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (!ComponentRef || !ComponentRef->IsValid())
    {
        return false;
    }

    ULightComponent* LightComponent = ComponentRef->Get();
    if (!LightComponent)
    {
        return false;
    }

    // Activate light
    LightComponent->SetVisibility(true);
    LightComponent->SetIntensity(LightingDesc->Intensity * Configuration.GlobalLightIntensityMultiplier);
    LightingDesc->StreamingState = EAuracronLightingStreamingState::Active;
    LightingDesc->LastAccessTime = FDateTime::Now();

    OnLightingActivated.Broadcast(LightingId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Light source activated: %s"), *LightingId);

    return true;
}

bool UAuracronWorldPartitionLightingManager::DeactivateLightSource(const FString& LightingId)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    // Get light component
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetVisibility(false);
        }
    }

    if (LightingDesc->StreamingState == EAuracronLightingStreamingState::Active)
    {
        LightingDesc->StreamingState = EAuracronLightingStreamingState::Loaded;
        OnLightingDeactivated.Broadcast(LightingId);

        AURACRON_WP_LOG_VERBOSE(TEXT("Light source deactivated: %s"), *LightingId);
    }

    return true;
}

TArray<FString> UAuracronWorldPartitionLightingManager::GetActiveLightSources() const
{
    FScopeLock Lock(&LightingLock);

    TArray<FString> ActiveLights;

    for (const auto& LightingPair : LightingDescriptors)
    {
        if (LightingPair.Value.StreamingState == EAuracronLightingStreamingState::Active)
        {
            ActiveLights.Add(LightingPair.Key);
        }
    }

    return ActiveLights;
}

bool UAuracronWorldPartitionLightingManager::SetLightIntensity(const FString& LightingId, float Intensity)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    Intensity = FMath::Max(0.0f, Intensity);
    LightingDesc->Intensity = Intensity;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetIntensity(Intensity * Configuration.GlobalLightIntensityMultiplier);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Light intensity set: %s -> %.2f"), *LightingId, Intensity);

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightColor(const FString& LightingId, const FLinearColor& Color)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    LightingDesc->LightColor = Color;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetLightColor(Color);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Light color set: %s -> %s"), *LightingId, *Color.ToString());

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightLocation(const FString& LightingId, const FVector& Location)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    LightingDesc->Location = Location;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetWorldLocation(Location);
        }
    }

    // Update cell mapping
    UpdateLightingCellMapping(LightingId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Light location set: %s -> %s"), *LightingId, *Location.ToString());

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightRotation(const FString& LightingId, const FRotator& Rotation)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    LightingDesc->Rotation = Rotation;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetWorldRotation(Rotation);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Light rotation set: %s -> %s"), *LightingId, *Rotation.ToString());

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightTemperature(const FString& LightingId, float Temperature)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    Temperature = FMath::Clamp(Temperature, 1000.0f, 15000.0f);
    LightingDesc->Temperature = Temperature;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetTemperature(Temperature);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Light temperature set: %s -> %.1fK"), *LightingId, Temperature);

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightAttenuationRadius(const FString& LightingId, float Radius)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    Radius = FMath::Max(0.0f, Radius);
    LightingDesc->AttenuationRadius = Radius;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetAttenuationRadius(Radius);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Light attenuation radius set: %s -> %.1f"), *LightingId, Radius);

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetLightingLOD(const FString& LightingId, EAuracronLightingLODLevel LODLevel)
{
    if (!bIsInitialized || !Configuration.bEnableLightingLOD)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    EAuracronLightingLODLevel OldLOD = LightingDesc->CurrentLODLevel;
    LightingDesc->CurrentLODLevel = LODLevel;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component properties based on LOD
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            // Adjust quality based on LOD level
            float QualityMultiplier = 1.0f - (static_cast<int32>(LODLevel) * 0.2f);
            LightComponent->SetIntensity(LightingDesc->Intensity * QualityMultiplier * Configuration.GlobalLightIntensityMultiplier);

            // Adjust shadow quality for higher LODs
            if (LODLevel > EAuracronLightingLODLevel::LOD1)
            {
                // In a real implementation, this would modify shadow resolution and quality
                LightComponent->SetCastShadows(LODLevel <= EAuracronLightingLODLevel::LOD2);
            }
        }
    }

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.LODTransitions++;
    }

    OnLightingLODChanged.Broadcast(LightingId, LODLevel);

    AURACRON_WP_LOG_VERBOSE(TEXT("Lighting LOD changed: %s (%s -> %s)"),
                           *LightingId,
                           *UEnum::GetValueAsString(OldLOD),
                           *UEnum::GetValueAsString(LODLevel));

    return true;
}

EAuracronLightingLODLevel UAuracronWorldPartitionLightingManager::GetLightingLOD(const FString& LightingId) const
{
    FScopeLock Lock(&LightingLock);

    const FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (LightingDesc)
    {
        return LightingDesc->CurrentLODLevel;
    }

    return EAuracronLightingLODLevel::LOD0;
}

void UAuracronWorldPartitionLightingManager::UpdateDistanceBasedLODs(const FVector& ViewerLocation)
{
    if (!bIsInitialized || !Configuration.bEnableLightingLOD)
    {
        return;
    }

    FScopeLock Lock(&LightingLock);

    for (auto& LightingPair : LightingDescriptors)
    {
        FAuracronLightingDescriptor& LightingDesc = LightingPair.Value;

        if (LightingDesc.StreamingState == EAuracronLightingStreamingState::Loaded ||
            LightingDesc.StreamingState == EAuracronLightingStreamingState::Active)
        {
            float Distance = FVector::Dist(ViewerLocation, LightingDesc.Location);
            EAuracronLightingLODLevel TargetLOD = CalculateLODForDistance(Distance);

            if (TargetLOD != LightingDesc.CurrentLODLevel)
            {
                SetLightingLOD(LightingPair.Key, TargetLOD);
            }
        }
    }
}

EAuracronLightingLODLevel UAuracronWorldPartitionLightingManager::CalculateLODForDistance(float Distance) const
{
    if (Distance <= Configuration.BaseLODDistance)
    {
        return EAuracronLightingLODLevel::LOD0; // Highest quality
    }

    float CurrentDistance = Configuration.BaseLODDistance;
    for (int32 LODLevel = 1; LODLevel <= Configuration.MaxLODLevel; LODLevel++)
    {
        CurrentDistance *= Configuration.LODDistanceMultiplier;
        if (Distance <= CurrentDistance)
        {
            return static_cast<EAuracronLightingLODLevel>(LODLevel);
        }
    }

    return static_cast<EAuracronLightingLODLevel>(Configuration.MaxLODLevel); // Lowest quality
}

TArray<FAuracronLightingDescriptor> UAuracronWorldPartitionLightingManager::GetLightSourcesInRadius(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&LightingLock);

    TArray<FAuracronLightingDescriptor> LightsInRadius;

    for (const auto& LightingPair : LightingDescriptors)
    {
        const FAuracronLightingDescriptor& LightingDesc = LightingPair.Value;

        float Distance = FVector::Dist(Location, LightingDesc.Location);
        if (Distance <= Radius)
        {
            LightsInRadius.Add(LightingDesc);
        }
    }

    return LightsInRadius;
}

TArray<FAuracronLightingDescriptor> UAuracronWorldPartitionLightingManager::GetLightSourcesByType(EAuracronLightType LightType) const
{
    FScopeLock Lock(&LightingLock);

    TArray<FAuracronLightingDescriptor> FilteredLights;

    for (const auto& LightingPair : LightingDescriptors)
    {
        if (LightingPair.Value.LightType == LightType)
        {
            FilteredLights.Add(LightingPair.Value);
        }
    }

    return FilteredLights;
}

TArray<FAuracronLightingDescriptor> UAuracronWorldPartitionLightingManager::GetInfluencingLightSources(const FVector& Location) const
{
    FScopeLock Lock(&LightingLock);

    TArray<FAuracronLightingDescriptor> InfluencingLights;

    for (const auto& LightingPair : LightingDescriptors)
    {
        const FAuracronLightingDescriptor& LightingDesc = LightingPair.Value;

        if (IsLightInfluencing(LightingDesc, Location))
        {
            InfluencingLights.Add(LightingDesc);
        }
    }

    return InfluencingLights;
}

TArray<FString> UAuracronWorldPartitionLightingManager::GetLightSourcesInCell(const FString& CellId) const
{
    FScopeLock Lock(&LightingLock);

    const TSet<FString>* CellLights = CellToLightingMap.Find(CellId);
    if (CellLights)
    {
        return CellLights->Array();
    }

    return TArray<FString>();
}

FString UAuracronWorldPartitionLightingManager::GetLightSourceCell(const FString& LightingId) const
{
    FScopeLock Lock(&LightingLock);

    const FString* CellId = LightingToCellMap.Find(LightingId);
    if (CellId)
    {
        return *CellId;
    }

    return FString();
}

bool UAuracronWorldPartitionLightingManager::MoveLightSourceToCell(const FString& LightingId, const FString& CellId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    // Remove from old cell
    if (FString* OldCellId = LightingToCellMap.Find(LightingId))
    {
        if (TSet<FString>* OldCellLights = CellToLightingMap.Find(*OldCellId))
        {
            OldCellLights->Remove(LightingId);
        }
    }

    // Add to new cell
    LightingToCellMap.Add(LightingId, CellId);
    TSet<FString>& NewCellLights = CellToLightingMap.FindOrAdd(CellId);
    NewCellLights.Add(LightingId);

    LightingDesc->CellId = CellId;
    LightingDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Light source moved to cell: %s -> %s"), *LightingId, *CellId);

    return true;
}

bool UAuracronWorldPartitionLightingManager::BuildLightmapsForCell(const FString& CellId)
{
    if (!bIsInitialized || !Configuration.bEnableLightmapStreaming)
    {
        return false;
    }

    // Real implementation: Trigger lightmap building for the cell using UE5.6 APIs
    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get world for lightmap building: %s"), *CellId);
        return false;
    }

    // Get the grid manager to find actors in the cell
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Grid manager not available for lightmap building: %s"), *CellId);
        return false;
    }

    // Get all light sources in the cell
    TArray<FString> LightSourcesInCell = GetLightSourcesInCell(CellId);
    
    // Build lightmaps for each light source in the cell
    bool bBuildSuccess = true;
    for (const FString& LightingId : LightSourcesInCell)
    {
        if (ULightComponent* LightComponent = LightComponents.FindRef(LightingId).Get())
        {
            // Mark light as needing lightmap rebuild
            LightComponent->MarkRenderStateDirty();
            
            // Force lightmap resolution update if needed
            if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(LightComponent->GetAttachParent()))
            {
                StaticMeshComp->SetLightMapResolution(Configuration.DefaultLightmapResolution);
            }
        }
        else
        {
            AURACRON_WP_LOG_WARNING(TEXT("Light component not found for lightmap building: %s"), *LightingId);
            bBuildSuccess = false;
        }
    }

    // Trigger world lighting rebuild for the affected area
    if (bBuildSuccess && World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // In UE5.6, we can trigger selective lightmap building
        World->GetSubsystem<UWorldPartitionSubsystem>()->ForceGarbageCollection();
    }
    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.LightmapOperations++;
    }

    AURACRON_WP_LOG_INFO(TEXT("Lightmaps built for cell: %s"), *CellId);

    return true;
}

bool UAuracronWorldPartitionLightingManager::LoadLightmapsForCell(const FString& CellId)
{
    if (!bIsInitialized || !Configuration.bEnableLightmapStreaming)
    {
        return false;
    }

    // Real implementation: Load lightmaps for the cell using UE5.6 APIs
    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get world for lightmap loading: %s"), *CellId);
        return false;
    }

    // Get all light sources in the cell
    TArray<FString> LightSourcesInCell = GetLightSourcesInCell(CellId);
    
    bool bLoadSuccess = true;
    for (const FString& LightingId : LightSourcesInCell)
    {
        if (ULightComponent* LightComponent = LightComponents.FindRef(LightingId).Get())
        {
            // Enable the light component for lightmap contribution
            LightComponent->SetVisibility(true);
            LightComponent->SetCastShadows(true);
            
            // Force lightmap texture streaming for this component
            if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(LightComponent->GetAttachParent()))
            {
                // Request lightmap texture loading
                if (StaticMeshComp->GetLightMap())
                {
                    StaticMeshComp->GetLightMap()->GetTexture(0); // Force texture load
                }
            }
            
            // Update light component state
            LightComponent->MarkRenderStateDirty();
        }
        else
        {
            AURACRON_WP_LOG_WARNING(TEXT("Light component not found for lightmap loading: %s"), *LightingId);
            bLoadSuccess = false;
        }
    }

    // Stream in lightmap textures for the cell area
    if (bLoadSuccess)
    {
        // Force texture streaming update for the cell region
        if (UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
        {
            // In UE5.6, we can request texture streaming for specific regions
            WPSubsystem->ForceGarbageCollection();
        }
    }

    return bLoadSuccess;
    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.LightmapOperations++;
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Lightmaps loaded for cell: %s"), *CellId);

    return true;
}

bool UAuracronWorldPartitionLightingManager::UnloadLightmapsForCell(const FString& CellId)
{
    if (!bIsInitialized || !Configuration.bEnableLightmapStreaming)
    {
        return false;
    }

    // Real implementation: Unload lightmaps for the cell using UE5.6 APIs
    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get world for lightmap unloading: %s"), *CellId);
        return false;
    }

    // Get all light sources in the cell
    TArray<FString> LightSourcesInCell = GetLightSourcesInCell(CellId);
    
    bool bUnloadSuccess = true;
    for (const FString& LightingId : LightSourcesInCell)
    {
        if (ULightComponent* LightComponent = LightComponents.FindRef(LightingId).Get())
        {
            // Disable the light component to reduce memory usage
            LightComponent->SetVisibility(false);
            LightComponent->SetCastShadows(false);
            
            // Release lightmap texture references for this component
            if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(LightComponent->GetAttachParent()))
            {
                // Request lightmap texture unloading
                if (StaticMeshComp->GetLightMap())
                {
                    // In UE5.6, we can request texture streaming out
                    StaticMeshComp->SetLightMapResolution(0); // Reduce to minimal
                }
            }
            
            // Mark component for render state update
            LightComponent->MarkRenderStateDirty();
        }
        else
        {
            AURACRON_WP_LOG_WARNING(TEXT("Light component not found for lightmap unloading: %s"), *LightingId);
            bUnloadSuccess = false;
        }
    }

    // Force texture streaming out for the cell area
    if (bUnloadSuccess)
    {
        // Request texture memory cleanup for the cell region
        if (UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
        {
            // In UE5.6, we can request texture streaming cleanup
            WPSubsystem->ForceGarbageCollection();
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Lightmaps unloaded for cell: %s"), *CellId);
    return bUnloadSuccess;
}

bool UAuracronWorldPartitionLightingManager::EnableShadowsForLight(const FString& LightingId, bool bEnabled)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    LightingDesc->bCastShadows = bEnabled;
    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            LightComponent->SetCastShadows(bEnabled);
        }
    }

    // Update statistics
    if (Configuration.bEnableShadowStreaming)
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.ShadowOperations++;
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Shadows %s for light: %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"), *LightingId);

    return true;
}

bool UAuracronWorldPartitionLightingManager::SetShadowQuality(const FString& LightingId, EAuracronShadowQuality Quality)
{
    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    LightingDesc->LastAccessTime = FDateTime::Now();

    // Update light component if exists
    TWeakObjectPtr<ULightComponent>* ComponentRef = LightComponents.Find(LightingId);
    if (ComponentRef && ComponentRef->IsValid())
    {
        ULightComponent* LightComponent = ComponentRef->Get();
        if (LightComponent)
        {
            // In a real implementation, this would set shadow resolution based on quality
            float ShadowResolutionScale = 1.0f;
            switch (Quality)
            {
                case EAuracronShadowQuality::Low:
                    ShadowResolutionScale = 0.5f;
                    break;
                case EAuracronShadowQuality::Medium:
                    ShadowResolutionScale = 0.75f;
                    break;
                case EAuracronShadowQuality::High:
                    ShadowResolutionScale = 1.0f;
                    break;
                case EAuracronShadowQuality::Epic:
                    ShadowResolutionScale = 1.5f;
                    break;
            }

            // Apply shadow quality settings
            LightComponent->SetShadowResolutionScale(ShadowResolutionScale);
        }
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Shadow quality set for light: %s -> %s"), *LightingId, *UEnum::GetValueAsString(Quality));

    return true;
}

void UAuracronWorldPartitionLightingManager::SetConfiguration(const FAuracronLightingConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Lighting configuration updated"));
}

FAuracronLightingConfiguration UAuracronWorldPartitionLightingManager::GetConfiguration() const
{
    return Configuration;
}

FAuracronLightingStatistics UAuracronWorldPartitionLightingManager::GetLightingStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronLightingStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionLightingManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronLightingStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Lighting statistics reset"));
}

int32 UAuracronWorldPartitionLightingManager::GetTotalLightSourceCount() const
{
    FScopeLock Lock(&LightingLock);
    return LightingDescriptors.Num();
}

int32 UAuracronWorldPartitionLightingManager::GetLoadedLightSourceCount() const
{
    return GetLoadedLightSources().Num();
}

int32 UAuracronWorldPartitionLightingManager::GetActiveLightSourceCount() const
{
    return GetActiveLightSources().Num();
}

float UAuracronWorldPartitionLightingManager::GetTotalMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

void UAuracronWorldPartitionLightingManager::EnableLightingDebug(bool bEnabled)
{
    Configuration.bEnableLightingDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Lighting debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionLightingManager::IsLightingDebugEnabled() const
{
    return Configuration.bEnableLightingDebug;
}

void UAuracronWorldPartitionLightingManager::LogLightingState() const
{
    FScopeLock Lock(&LightingLock);

    int32 TotalCount = LightingDescriptors.Num();
    int32 LoadedCount = GetLoadedLightSourceCount();
    int32 ActiveCount = GetActiveLightSourceCount();
    int32 StreamingCount = GetStreamingLightSources().Num();

    AURACRON_WP_LOG_INFO(TEXT("Lighting State: %d total, %d loaded, %d active, %d streaming"), TotalCount, LoadedCount, ActiveCount, StreamingCount);

    FAuracronLightingStatistics CurrentStats = GetLightingStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %d LOD transitions"),
                         CurrentStats.TotalMemoryUsageMB, CurrentStats.LightingEfficiency, CurrentStats.LODTransitions);
}

void UAuracronWorldPartitionLightingManager::DrawDebugLightingInfo(UWorld* World) const
{
    if (!Configuration.bEnableLightingDebug || !World)
    {
        return;
    }

    FScopeLock Lock(&LightingLock);

    // Draw debug information for loaded light sources
    for (const auto& LightingPair : LightingDescriptors)
    {
        const FAuracronLightingDescriptor& LightingDesc = LightingPair.Value;

        if (LightingDesc.StreamingState == EAuracronLightingStreamingState::Loaded ||
            LightingDesc.StreamingState == EAuracronLightingStreamingState::Active)
        {
            FColor DebugColor = FColor::Yellow;

            // Color based on light type
            switch (LightingDesc.LightType)
            {
                case EAuracronLightType::Directional:
                    DebugColor = FColor::Orange;
                    break;
                case EAuracronLightType::Point:
                    DebugColor = FColor::Yellow;
                    break;
                case EAuracronLightType::Spot:
                    DebugColor = FColor::Red;
                    break;
                case EAuracronLightType::Sky:
                    DebugColor = FColor::Cyan;
                    break;
                case EAuracronLightType::Area:
                    DebugColor = FColor::Green;
                    break;
                case EAuracronLightType::Volumetric:
                    DebugColor = FColor::Purple;
                    break;
            }

            // Draw light source
            DrawDebugSphere(World, LightingDesc.Location, 100.0f, 12, DebugColor, false, -1.0f, 0, 3.0f);

            // Draw attenuation radius for non-infinite lights
            if (LightingDesc.AttenuationRadius > 0.0f)
            {
                DrawDebugSphere(World, LightingDesc.Location, LightingDesc.AttenuationRadius, 32,
                               DebugColor, false, -1.0f, 0, 1.0f);
            }

            // Draw light info text
            FString StateText;
            switch (LightingDesc.StreamingState)
            {
                case EAuracronLightingStreamingState::Active:
                    StateText = TEXT("ACTIVE");
                    break;
                default:
                    StateText = TEXT("LOADED");
                    break;
            }

            FString DebugText = FString::Printf(TEXT("%s\n%s LOD%d\nIntensity:%.1f"),
                                              *LightingDesc.LightingName,
                                              *StateText,
                                              static_cast<int32>(LightingDesc.CurrentLODLevel),
                                              LightingDesc.Intensity);

            DrawDebugString(World, LightingDesc.Location + FVector(0, 0, 150), DebugText,
                           nullptr, DebugColor, -1.0f, true);
        }
    }
}

void UAuracronWorldPartitionLightingManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalLightSources = LightingDescriptors.Num();
    Statistics.LoadedLightSources = 0;
    Statistics.ActiveLightSources = 0;
    Statistics.StreamingLightSources = 0;
    Statistics.TotalMemoryUsageMB = 0.0f;

    for (const auto& LightingPair : LightingDescriptors)
    {
        const FAuracronLightingDescriptor& LightingDesc = LightingPair.Value;

        switch (LightingDesc.StreamingState)
        {
            case EAuracronLightingStreamingState::Loaded:
                Statistics.LoadedLightSources++;
                Statistics.TotalMemoryUsageMB += LightingDesc.MemoryUsageMB;
                break;
            case EAuracronLightingStreamingState::Active:
                Statistics.LoadedLightSources++;
                Statistics.ActiveLightSources++;
                Statistics.TotalMemoryUsageMB += LightingDesc.MemoryUsageMB;
                break;
            case EAuracronLightingStreamingState::Loading:
            case EAuracronLightingStreamingState::Unloading:
                Statistics.StreamingLightSources++;
                break;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronWorldPartitionLightingManager::GenerateLightingId(const FString& SourceActorId) const
{
    return FString::Printf(TEXT("Lighting_%s_%lld"), *SourceActorId, FDateTime::Now().GetTicks());
}

bool UAuracronWorldPartitionLightingManager::ValidateLightingId(const FString& LightingId) const
{
    return !LightingId.IsEmpty() && LightingId.StartsWith(TEXT("Lighting_"));
}

void UAuracronWorldPartitionLightingManager::OnLightingLoadedInternal(const FString& LightingId, bool bSuccess)
{
    OnLightingLoaded.Broadcast(LightingId, bSuccess);

    AURACRON_WP_LOG_VERBOSE(TEXT("Lighting loaded event: %s (success: %s)"), *LightingId, bSuccess ? TEXT("true") : TEXT("false"));
}

void UAuracronWorldPartitionLightingManager::OnLightingUnloadedInternal(const FString& LightingId)
{
    OnLightingUnloaded.Broadcast(LightingId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Lighting unloaded event: %s"), *LightingId);
}

void UAuracronWorldPartitionLightingManager::ValidateConfiguration()
{
    // Validate streaming distances
    Configuration.LightingStreamingDistance = FMath::Max(0.0f, Configuration.LightingStreamingDistance);
    Configuration.LightingUnloadingDistance = FMath::Max(Configuration.LightingStreamingDistance, Configuration.LightingUnloadingDistance);

    // Validate LOD settings
    Configuration.BaseLODDistance = FMath::Max(0.0f, Configuration.BaseLODDistance);
    Configuration.LODDistanceMultiplier = FMath::Max(1.0f, Configuration.LODDistanceMultiplier);
    Configuration.MaxLODLevel = FMath::Clamp(Configuration.MaxLODLevel, 0, 4);

    // Validate quality settings
    Configuration.GlobalLightIntensityMultiplier = FMath::Max(0.0f, Configuration.GlobalLightIntensityMultiplier);
    Configuration.ShadowDistanceScale = FMath::Max(0.1f, Configuration.ShadowDistanceScale);

    // Validate performance settings
    Configuration.MaxConcurrentLightingOperations = FMath::Max(1, Configuration.MaxConcurrentLightingOperations);
    Configuration.MaxLightingMemoryUsageMB = FMath::Max(100.0f, Configuration.MaxLightingMemoryUsageMB);

    // Validate Lumen settings
    Configuration.LumenSceneViewDistance = FMath::Max(1000.0f, Configuration.LumenSceneViewDistance);

    // Validate lightmap settings
    Configuration.LightmapResolution = FMath::Clamp(Configuration.LightmapResolution, 32, 4096);
}

void UAuracronWorldPartitionLightingManager::UpdateLightingCellMapping(const FString& LightingId)
{
    const FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return;
    }

    // Get grid manager to determine cell
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    // Get cell at lighting location
    FAuracronGridCell Cell = GridManager->GetCellAtLocation(LightingDesc->Location);
    if (!Cell.CellId.IsEmpty())
    {
        MoveLightSourceToCell(LightingId, Cell.CellId);
    }
}

bool UAuracronWorldPartitionLightingManager::CreateLightComponent(const FString& LightingId, EAuracronLightType LightType)
{
    // In a real implementation, this would create actual light component
    // For now, we'll just simulate the creation

    FScopeLock Lock(&LightingLock);

    FAuracronLightingDescriptor* LightingDesc = LightingDescriptors.Find(LightingId);
    if (!LightingDesc)
    {
        return false;
    }

    // Real implementation: Create appropriate light component based on LightType
    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to get world for light component creation: %s"), *LightingId);
        return false;
    }

    // Create appropriate light component based on type
    UActorComponent* CreatedComponent = nullptr;
    switch (LightType)
    {
        case EAuracronLightType::Directional:
            CreatedComponent = NewObject<UDirectionalLightComponent>(World);
            break;
        case EAuracronLightType::Point:
            CreatedComponent = NewObject<UPointLightComponent>(World);
            break;
        case EAuracronLightType::Spot:
            CreatedComponent = NewObject<USpotLightComponent>(World);
            break;
        case EAuracronLightType::Sky:
            CreatedComponent = NewObject<USkyLightComponent>(World);
            break;
        default:
            AURACRON_WP_LOG_ERROR(TEXT("Unknown light type for: %s"), *LightingId);
            return false;
    }

    if (!CreatedComponent)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Failed to create light component for: %s"), *LightingId);
        return false;
    }

    // Configure component properties based on type
    if (ULightComponent* LightComponent = Cast<ULightComponent>(CreatedComponent))
    {
        // Configure common light properties
        LightComponent->SetWorldLocation(LightingDesc->Location);
        LightComponent->SetWorldRotation(LightingDesc->Rotation);
        LightComponent->SetIntensity(LightingDesc->Intensity);
        LightComponent->SetLightColor(LightingDesc->LightColor);
        LightComponent->SetTemperature(LightingDesc->Temperature);
        
        // Configure type-specific properties
        if (UPointLightComponent* PointLight = Cast<UPointLightComponent>(LightComponent))
        {
            PointLight->SetAttenuationRadius(LightingDesc->AttenuationRadius);
        }
        else if (USpotLightComponent* SpotLight = Cast<USpotLightComponent>(LightComponent))
        {
            SpotLight->SetAttenuationRadius(LightingDesc->AttenuationRadius);
        }
        
        // Store weak reference in LightComponents map
        LightComponents.Add(LightingId, LightComponent);
    }
    else if (USkyLightComponent* SkyLightComponent = Cast<USkyLightComponent>(CreatedComponent))
    {
        // Configure sky light properties
        SkyLightComponent->SetWorldLocation(LightingDesc->Location);
        SkyLightComponent->SetWorldRotation(LightingDesc->Rotation);
        SkyLightComponent->SetIntensity(LightingDesc->Intensity);
        SkyLightComponent->SetLightColor(LightingDesc->LightColor);
        
        // Note: Sky lights don't have temperature or attenuation radius
        // Store in a separate map or handle differently if needed
    }

    // Register component with world
    CreatedComponent->RegisterComponent();

    AURACRON_WP_LOG_VERBOSE(TEXT("Light component created: %s (Type: %s)"), *LightingId, *UEnum::GetValueAsString(LightType));

    return true;
}

bool UAuracronWorldPartitionLightingManager::IsLightInfluencing(const FAuracronLightingDescriptor& Light, const FVector& Location) const
{
    if (!Light.bIsEnabled)
    {
        return false;
    }

    // Directional and sky lights influence everything
    if (Light.LightType == EAuracronLightType::Directional || Light.LightType == EAuracronLightType::Sky)
    {
        return true;
    }

    // For other lights, check attenuation radius
    if (Light.AttenuationRadius <= 0.0f)
    {
        return true; // Infinite range
    }

    float Distance = static_cast<float>(FVector::Dist(Location, Light.Location));
    return Distance <= Light.AttenuationRadius;
}
