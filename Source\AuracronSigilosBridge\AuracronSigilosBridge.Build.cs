﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de SÃƒÂ­gilos Auracron Bridge Build Configuration
using UnrealBuildTool;
public class AuracronSigilosBridge : ModuleRules
{
    public AuracronSigilosBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicIncludePaths.AddRange(
            new string[] {
                // ... add public include paths required here ...
            }
        );
        PrivateIncludePaths.AddRange(
            new string[] {
                // ... add other private include paths required here ...
            }
        );
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "AIModule",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "UMG",
                "Slate",
                "SlateCore",
                "InputCore",
                "EnhancedInput",
                "DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "NiagaraCore",
                "NiagaraShader",
                "ChaosCore",
                "PhysicsCore",
                "AudioMixer",
                "MetasoundFrontend",
                "MetasoundEngine",
                "MetasoundStandardNodes",
                "SignalProcessing",
                "Json",
                "OnlineSubsystemUtils",
                "Sockets",
                "Networking",
                "PacketHandler",
                "ReliabilityHandlerComponent"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "PropertyEditor",
                    "KismetCompiler",
                    "BlueprintGraph",
                    "Kismet",
                    "ToolMenus"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {
                // ... add any modules that your module loads dynamically here ...
            }
        );
        // Enable optimization for shipping builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            OptimizeCode = CodeOptimization.InShippingBuildsOnly;
            bUseUnity = true;
        }
        // Enable additional features for development builds
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_SIGILOS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_SIGILOS_PROFILING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_SIGILOS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_SIGILOS_PROFILING=0");
        }
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=1");
            // Mobile-specific optimizations
            bUseUnity = true;
            MinFilesUsingPrecompiledHeaderOverride = 1;
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_PLATFORM=0");
        }
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_GAMEPLAY_ABILITY_SYSTEM=1");
        PublicDefinitions.Add("WITH_ENHANCED_INPUT=1");
        PublicDefinitions.Add("WITH_COMMON_UI=1");
    }
}


