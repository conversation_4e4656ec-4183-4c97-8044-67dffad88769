/**
 * AuracronMasterOrchestrator.Build.cs
 * 
 * Build configuration for Auracron Master Orchestrator module.
 * Coordinates all Auracron bridges and subsystems for seamless integration.
 * 
 * Uses UE 5.6 modern build system for production-ready compilation.
 */

using UnrealBuildTool;

public class AuracronMasterOrchestrator : ModuleRules
{
    public AuracronMasterOrchestrator(ReadOnlyTargetRules Target) : base(Target)
    {
        // UE 5.6 PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable modern C++ features
        CppStandard = CppStandardVersion.Cpp20;
        bUseUnity = true;
        
        // Core dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities",
            "UMG",
            "Slate",
            "SlateCore",
            "InputCore",
            "EnhancedInput",
            "NetCore",
            "OnlineSubsystem",
            "OnlineSubsystemUtils",
            "Sockets",
            "Networking",
            "Json",
            "JsonObjectConverter",
            "HTTP",
            "AudioMixer",
            "AudioExtensions",
            "MetasoundEngine",
            "MetasoundFrontend",
            "MetasoundStandardNodes",
            "Niagara",
            "NiagaraCore",
            "NiagaraShader",
            "RenderCore",
            "RHI",
            "Renderer",
            "DeveloperSettings",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "ApplicationCore",
            "ToolWidgets",
            "StatusBar",
            "MainFrame",
            "LevelEditor",
            "SceneOutliner",
            "ContentBrowser",
            "AssetTools",
            "AssetRegistry",
            "EditorSubsystem",
            "UnrealEdMessages",
            "SourceControl",
            "Projects",
            "TargetPlatform",
            "DesktopPlatform",
            "LauncherPlatform",
            "GameProjectGeneration",
            "AddContentDialog",
            "GameProjectUtils",
            "HardwareTargeting",
            "LocalizationService",
            "TranslationEditor",
            "Localization",
            "InternationalizationSettings",
            "PacketHandler",
            "ReliabilityHandlerComponent",
            "Analytics",
            "AnalyticsET",
            "CrashReportCore",
            "PerfCounters",
            "TraceLog",
            "TraceAnalysis",
            "TraceServices",
            "TraceInsights",
            "ConcertSyncCore",
            "ConcertSyncClient",
            "ConcertSyncServer",
            "ConcertSharedSlate",
            "ConcertTransport",
            "Concert",
            "MultiUserClient",
            "UdpMessaging",
            "TcpMessaging",
            "MessagingCommon",
            "Messaging",
            "SerializedRecorderInterface",
            "FunctionalTesting",
            "AutomationController",
            "AutomationWorker",
            "AutomationMessages",
            "UnrealAutomationCommon",
            "ScreenShotComparison",
            "ImageWrapper",
            "RawMesh",
            "MeshDescription",
            "StaticMeshDescription",
            "SkeletalMeshDescription",
            "MeshConversion",
            "MeshUtilities",
            "MeshUtilitiesCommon",
            "ProceduralMeshComponent",
            "GeometryCollectionEngine",
            "ChaosSolverEngine",
            "Chaos",
            "ChaosCore",
            "PhysicsCore",
            "FieldSystemEngine",
            "GeometryFramework",
            "DynamicMesh",
            "GeometryAlgorithms",
            "ModelingComponents",
            "ModelingComponentsEditorOnly",
            "InteractiveToolsFramework",
            "EditorInteractiveToolsFramework",
            "ToolkitSteamworks",
            "OnlineSubsystemSteam",
            "Steamworks",
            "OnlineSubsystemEOS",
            "EOSShared",
            "EOSSDK",
            "OnlineServicesEOS",
            "OnlineServicesEOSGS",
            "OnlineServices",
            "OnlineServicesInterface",
            "OnlineBase"
        });

        // Auracron bridge dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "AuracronDynamicRealmBridge",
            "AuracronHarmonyEngineBridge", 
            "AuracronSigilosBridge",
            "AuracronPCGBridge",
            "AuracronNexusCommunityBridge",
            "AuracronLivingWorldBridge",
            "AuracronAdaptiveEngagementBridge",
            "AuracronQuantumConsciousnessBridge",
            "AuracronIntelligentDocumentationBridge",
            "AuracronNetworkingBridge",
            "AuracronAnalyticsBridge",
            "AuracronUIBridge",
            "AuracronAudioBridge",
            "AuracronVFXBridge",
            "AuracronTutorialBridge",
            "AuracronAbismoUmbrioBridge",
            "AuracronFirmamentoZephyrBridge",
            "AuracronPlanicieRadianteBridge"
        });

        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "EditorStyle",
            "EditorWidgets",
            "GraphEditor",
            "KismetCompiler",
            "BlueprintGraph",
            "KismetWidgets",
            "PropertyEditor",
            "SharedSettingsWidgets",
            "ContentBrowser",
            "WorkspaceMenuStructure",
            "AssetDefinition",
            "ToolMenus",
            "StatusBar",
            "OutputLog",
            "MessageLog",
            "CollectionManager",
            "AddContentDialog",
            "MeshPaint",
            "MeshPaintMode",
            "UnrealEd",
            "LevelEditor",
            "Settings",
            "IntroTutorials",
            "HeadMountedDisplay",
            "VREditor",
            "CommonMenuExtensions",
            "DesktopWidgets",
            "MainFrame",
            "Documentation",
            "UATHelper",
            "TargetDeviceServices",
            "LauncherServices",
            "ProjectLauncher",
            "DeviceProfileServices",
            "ScreenShotComparison",
            "AutomationWindow",
            "Profiler",
            "TaskGraph",
            "RenderCore",
            "ApplicationCore",
            "PreLoadScreen",
            "MoviePlayer",
            "HeadMountedDisplay",
            "AugmentedReality",
            "LocationServicesBPLibrary",
            "MobilePatchingUtils",
            "BuildPatchServices",
            "PakFile",
            "SandboxFile",
            "StreamingPauseRendering"
        });

        // Platform-specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "XAudio2",
                "AudioMixerXAudio2"
            });
        }

        // Development and editor dependencies
        if (Target.bBuildDeveloperTools)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "MessageLog",
                "CollisionAnalyzer",
                "LogVisualizer",
                "MeshUtilities",
                "TargetPlatform",
                "PlatformCryptoTypes",
                "PlatformCrypto",
                "DesktopPlatform",
                "LauncherPlatform",
                "LocalizationService",
                "SharedSettingsWidgets",
                "GameProjectGeneration",
                "HardwareTargeting",
                "CrashReportCore",
                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "ScreenShotComparison",
                "UnrealAutomationCommon",
                "AutomationWindow",
                "SessionServices",
                "Profiler",
                "ProfilerClient",
                "TraceLog",
                "TraceAnalysis",
                "TraceServices",
                "TraceInsights"
            });
        }

        // Optimization settings for production builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_OPTIMIZED=1");
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_DEVELOPMENT=1");
        }

        // Enable advanced features
        PublicDefinitions.Add("WITH_AURACRON_MASTER_ORCHESTRATOR=1");
        PublicDefinitions.Add("WITH_BRIDGE_COORDINATION=1");
        PublicDefinitions.Add("WITH_SYSTEM_HEALTH_MONITORING=1");
        PublicDefinitions.Add("WITH_PERFORMANCE_OPTIMIZATION=1");
        PublicDefinitions.Add("WITH_ERROR_RECOVERY=1");
        PublicDefinitions.Add("WITH_QUALITY_ASSURANCE=1");

        // Version information
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_MASTER_ORCHESTRATOR_VERSION_PATCH=0");
    }
}
