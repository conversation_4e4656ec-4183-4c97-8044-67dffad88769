/**
 * AuracronAutomatedQABridge.Build.cs
 * 
 * Build configuration for Auracron Automated QA Bridge module.
 * Comprehensive automated QA and validation system for all Auracron systems.
 * 
 * Uses UE 5.6 modern build system for production-ready compilation.
 */

using UnrealBuildTool;

public class AuracronAutomatedQABridge : ModuleRules
{
    public AuracronAutomatedQABridge(ReadOnlyTargetRules Target) : base(Target)
    {
        // UE 5.6 PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Enable modern C++ features
        CppStandard = CppStandardVersion.Cpp20;
        bUseUnity = true;
        
        // Core dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "GameplayTags",
            "GameplayTasks",
            "GameplayAbilities",
            "UMG",
            "Slate",
            "SlateCore",
            "InputCore",
            "EnhancedInput",
            "NetCore",
            "OnlineSubsystem",
            "OnlineSubsystemUtils",
            "Sockets",
            "Networking",
            "Json",
            "JsonObjectConverter",
            "HTTP",
            "AutomationController",
            "AutomationWorker",
            "AutomationMessages",
            "UnrealAutomationCommon",
            "FunctionalTesting",
            "ScreenShotComparison",
            "ImageWrapper",
            "Analytics",
            "AnalyticsET",
            "CrashReportCore",
            "PerfCounters",
            "TraceLog",
            "TraceAnalysis",
            "TraceServices",
            "TraceInsights",
            "SessionServices",
            "Profiler",
            "ProfilerClient",
            "TaskGraph",
            "RenderCore",
            "ApplicationCore",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "UnrealEd",
            "PropertyEditor",
            "DetailCustomizations",
            "ComponentVisualizers",
            "EngineSettings",
            "DeveloperSettings",
            "StatusBar",
            "MainFrame",
            "LevelEditor",
            "SceneOutliner",
            "ContentBrowser",
            "AssetTools",
            "AssetRegistry",
            "EditorSubsystem",
            "UnrealEdMessages",
            "SourceControl",
            "Projects",
            "TargetPlatform",
            "DesktopPlatform",
            "LauncherPlatform",
            "GameProjectGeneration",
            "AddContentDialog",
            "GameProjectUtils",
            "HardwareTargeting",
            "LocalizationService",
            "TranslationEditor",
            "Localization",
            "InternationalizationSettings"
        });

        // Auracron bridge dependencies
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "AuracronMasterOrchestrator",
            "AuracronDynamicRealmBridge",
            "AuracronHarmonyEngineBridge", 
            "AuracronSigilosBridge",
            "AuracronPCGBridge",
            "AuracronNexusCommunityBridge",
            "AuracronLivingWorldBridge",
            "AuracronAdaptiveEngagementBridge",
            "AuracronQuantumConsciousnessBridge",
            "AuracronIntelligentDocumentationBridge",
            "AuracronNetworkingBridge",
            "AuracronAnalyticsBridge",
            "AuracronUIBridge",
            "AuracronAudioBridge",
            "AuracronVFXBridge",
            "AuracronTutorialBridge",
            "AuracronAbismoUmbrioBridge",
            "AuracronFirmamentoZephyrBridge",
            "AuracronPlanicieRadianteBridge"
        });

        // Private dependencies for internal functionality
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Slate",
            "SlateCore",
            "EditorStyle",
            "EditorWidgets",
            "GraphEditor",
            "KismetCompiler",
            "BlueprintGraph",
            "KismetWidgets",
            "PropertyEditor",
            "SharedSettingsWidgets",
            "ContentBrowser",
            "WorkspaceMenuStructure",
            "AssetDefinition",
            "ToolMenus",
            "StatusBar",
            "OutputLog",
            "MessageLog",
            "CollectionManager",
            "AddContentDialog",
            "MeshPaint",
            "MeshPaintMode",
            "UnrealEd",
            "LevelEditor",
            "Settings",
            "IntroTutorials",
            "HeadMountedDisplay",
            "VREditor",
            "CommonMenuExtensions",
            "DesktopWidgets",
            "MainFrame",
            "Documentation",
            "UATHelper",
            "TargetDeviceServices",
            "LauncherServices",
            "ProjectLauncher",
            "DeviceProfileServices",
            "ScreenShotComparison",
            "AutomationWindow",
            "Profiler",
            "TaskGraph",
            "RenderCore",
            "ApplicationCore",
            "PreLoadScreen",
            "MoviePlayer",
            "HeadMountedDisplay",
            "AugmentedReality",
            "LocationServicesBPLibrary",
            "MobilePatchingUtils",
            "BuildPatchServices",
            "PakFile",
            "SandboxFile",
            "StreamingPauseRendering",
            "AutomationTest",
            "AutomationTestFramework",
            "AutomationDriver",
            "AutomationDriverCommon",
            "FunctionalTestingEditor",
            "TestHarnessAdapter",
            "EditorTests",
            "EngineTests",
            "CoreTests",
            "RenderDocPlugin",
            "StaticMeshEditor",
            "SkeletalMeshEditor",
            "PersonaToolkit",
            "AnimGraph",
            "AnimGraphRuntime",
            "AnimationCore",
            "AnimationBlueprintLibrary",
            "ControlRig",
            "ControlRigEditor",
            "IKRig",
            "IKRigEditor",
            "Sequencer",
            "MovieScene",
            "MovieSceneTools",
            "MovieSceneCapture",
            "LevelSequence",
            "LevelSequenceEditor",
            "CinematicCamera",
            "GameplayCameras",
            "CameraAnimation",
            "TemplateSequence",
            "ActorSequence",
            "MediaAssets",
            "MediaUtils",
            "MediaIOCore",
            "MediaFrameworkUtilities",
            "ElectraBase",
            "ElectraCodecs",
            "ElectraDecoders",
            "ElectraPlayer",
            "ElectraPlayerRuntime",
            "ElectraSubtitles",
            "WmfMedia",
            "WindowsMediaCodecs",
            "AVEncoder",
            "AVCodecsCore",
            "VideoRecording",
            "GameplayInsights",
            "RewindDebugger",
            "TraceDataFiltering",
            "TraceDataFilters",
            "InsightsCore",
            "TraceInsightsCore",
            "EditorInsights",
            "GameplayInsightsEditor",
            "CookOnTheFly",
            "CookOnTheFlyNetServer",
            "NetworkFileSystem",
            "DerivedDataCache",
            "DerivedDataEditor",
            "TextureCompressor",
            "TextureFormatDXT",
            "TextureFormatASTC",
            "TextureFormatETC2",
            "TextureFormatPVR",
            "TextureFormatUncompressed",
            "AudioFormatADPCM",
            "AudioFormatOgg",
            "AudioFormatOpus",
            "AudioPlatformConfiguration",
            "SignalProcessing",
            "SynthComponents",
            "AudioSynesthesia",
            "AudioWidgets",
            "AudioAnalyzer",
            "Synthesis",
            "MetasoundEngine",
            "MetasoundFrontend",
            "MetasoundStandardNodes",
            "MetasoundGenerator",
            "MetasoundGraphCore",
            "MetasoundEditor",
            "WaveTable",
            "Harmonix",
            "HarmonixMidi",
            "HarmonixDsp",
            "HarmonixMetasound"
        });

        // Platform-specific dependencies
        if (Target.Platform == UnrealTargetPlatform.Win64)
        {
            PublicDependencyModuleNames.AddRange(new string[]
            {
                "XAudio2",
                "AudioMixerXAudio2",
                "WindowsPlatformFeatures"
            });
        }

        // Development and editor dependencies
        if (Target.bBuildDeveloperTools)
        {
            PrivateDependencyModuleNames.AddRange(new string[]
            {
                "MessageLog",
                "CollisionAnalyzer",
                "LogVisualizer",
                "MeshUtilities",
                "TargetPlatform",
                "PlatformCryptoTypes",
                "PlatformCrypto",
                "DesktopPlatform",
                "LauncherPlatform",
                "LocalizationService",
                "SharedSettingsWidgets",
                "GameProjectGeneration",
                "HardwareTargeting",
                "CrashReportCore",
                "AutomationController",
                "AutomationWorker",
                "AutomationMessages",
                "FunctionalTesting",
                "ScreenShotComparison",
                "UnrealAutomationCommon",
                "AutomationWindow",
                "SessionServices",
                "Profiler",
                "ProfilerClient",
                "TraceLog",
                "TraceAnalysis",
                "TraceServices",
                "TraceInsights",
                "GameplayInsights",
                "RewindDebugger",
                "EditorInsights",
                "GameplayInsightsEditor",
                "InsightsCore",
                "TraceInsightsCore",
                "TraceDataFiltering",
                "TraceDataFilters"
            });
        }

        // Optimization settings for production builds
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_OPTIMIZED=1");
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_SHIPPING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_AUTOMATED_QA_DEVELOPMENT=1");
        }

        // Enable advanced features
        PublicDefinitions.Add("WITH_AURACRON_AUTOMATED_QA=1");
        PublicDefinitions.Add("WITH_AUTOMATED_TESTING=1");
        PublicDefinitions.Add("WITH_CONTINUOUS_VALIDATION=1");
        PublicDefinitions.Add("WITH_PERFORMANCE_MONITORING=1");
        PublicDefinitions.Add("WITH_REGRESSION_TESTING=1");
        PublicDefinitions.Add("WITH_QUALITY_ASSURANCE=1");

        // Version information
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_MAJOR=1");
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_MINOR=0");
        PublicDefinitions.Add("AURACRON_AUTOMATED_QA_VERSION_PATCH=0");
    }
}
