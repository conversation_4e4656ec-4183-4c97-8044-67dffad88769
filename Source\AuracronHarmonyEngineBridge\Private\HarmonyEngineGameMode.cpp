#include "HarmonyEngineGameMode.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSubsystem.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/PlayerState.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

AHarmonyEngineGameMode::AHarmonyEngineGameMode()
{
    // Default configuration
    bEnableHarmonyEngine = true;
    BehaviorAnalysisInterval = 30.0f;
    ToxicityDetectionThreshold = 0.6f;
    bEnableAutomaticInterventions = true;
    bEnableCommunityHealing = true;
}

void AHarmonyEngineGameMode::BeginPlay()
{
    Super::BeginPlay();
    
    if (!bEnableHarmonyEngine)
    {
        UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine disabled in game mode"));
        return;
    }
    
    // Get Harmony Engine subsystem
    if (UGameInstance* GameInstance = GetGameInstance())
    {
        HarmonyEngineSubsystem = GameInstance->GetSubsystem<UHarmonyEngineSubsystem>();
        if (HarmonyEngineSubsystem)
        {
            // Initialize callbacks
            InitializeHarmonyEngineCallbacks();
            UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine GameMode initialized successfully"));
        }
        else
        {
            UE_LOG(LogHarmonyEngine, Error, TEXT("Failed to get HarmonyEngineSubsystem"));
        }
    }
}

void AHarmonyEngineGameMode::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Cleanup Harmony Engine callbacks
    CleanupHarmonyEngineCallbacks();
    
    Super::EndPlay(EndPlayReason);
}

void AHarmonyEngineGameMode::PostLogin(APlayerController* NewPlayer)
{
    Super::PostLogin(NewPlayer);
    
    if (!bEnableHarmonyEngine || !HarmonyEngineSubsystem || !NewPlayer)
    {
        return;
    }
    
    // Register player with Harmony Engine
    HarmonyEngineSubsystem->RegisterPlayer(NewPlayer);
    
    // Initialize player tracking
    FString PlayerID = GetPlayerIDFromController(NewPlayer);
    PlayerJoinTimes.Add(PlayerID, FDateTime::Now());
    PlayerActionCounts.Add(PlayerID, 0);
    PlayerSessionDurations.Add(PlayerID, 0.0f);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s registered with Harmony Engine"), *PlayerID);
}

void AHarmonyEngineGameMode::Logout(AController* Exiting)
{
    if (APlayerController* PC = Cast<APlayerController>(Exiting))
    {
        if (bEnableHarmonyEngine && HarmonyEngineSubsystem)
        {
            // Unregister player from Harmony Engine
            HarmonyEngineSubsystem->UnregisterPlayer(PC);
            
            // Cleanup player tracking data
            FString PlayerID = GetPlayerIDFromController(PC);
            PlayerJoinTimes.Remove(PlayerID);
            PlayerActionCounts.Remove(PlayerID);
            PlayerSessionDurations.Remove(PlayerID);
            
            UE_LOG(LogHarmonyEngine, Log, TEXT("Player %s unregistered from Harmony Engine"), *PlayerID);
        }
    }
    
    Super::Logout(Exiting);
}

void AHarmonyEngineGameMode::OnPlayerChatMessage(APlayerController* Player, const FString& Message)
{
    if (!bEnableHarmonyEngine || !HarmonyEngineSubsystem || !Player)
    {
        return;
    }
    
    FString PlayerID = GetPlayerIDFromController(Player);
    
    // Analyze chat message for toxicity and positivity
    AnalyzeChatMessage(PlayerID, Message);
    
    // Update player behavior metrics
    UpdatePlayerBehaviorMetrics(PlayerID);
    
    // Check if intervention is needed
    CheckForInterventionNeeds(PlayerID);
}

void AHarmonyEngineGameMode::OnPlayerAction(APlayerController* Player, const FString& ActionType, bool bIsPositive)
{
    if (!bEnableHarmonyEngine || !HarmonyEngineSubsystem || !Player)
    {
        return;
    }
    
    FString PlayerID = GetPlayerIDFromController(Player);
    
    // Track player action
    TrackPlayerAction(PlayerID, ActionType, bIsPositive);
    
    // Award kindness points for positive actions
    if (bIsPositive)
    {
        int32 KindnessPoints = CalculateActionKindnessPoints(ActionType);
        if (KindnessPoints > 0)
        {
            HarmonyEngineSubsystem->AwardKindnessPoints(PlayerID, KindnessPoints, 
                FString::Printf(TEXT("Positive action: %s"), *ActionType));
        }
    }
    
    // Update behavior metrics
    UpdatePlayerBehaviorMetrics(PlayerID);
}

void AHarmonyEngineGameMode::OnPlayerKill(APlayerController* Killer, APlayerController* Victim)
{
    if (!bEnableHarmonyEngine || !HarmonyEngineSubsystem)
    {
        return;
    }
    
    if (Killer)
    {
        FString KillerID = GetPlayerIDFromController(Killer);
        
        // Track kill action (neutral by default, context matters)
        TrackPlayerAction(KillerID, TEXT("Kill"), false);
        
        // Check for toxic behavior patterns (spawn camping, griefing, etc.)
        if (IsKillPotentiallyToxic(Killer, Victim))
        {
            // Increase toxicity score
            FPlayerBehaviorSnapshot BehaviorData = CreateBehaviorSnapshot(KillerID);
            BehaviorData.ToxicityScore += 0.1f;
            BehaviorData.NegativeActionsCount++;
            
            HarmonyEngineSubsystem->UpdatePlayerBehavior(KillerID, BehaviorData);
        }
    }
    
    if (Victim)
    {
        FString VictimID = GetPlayerIDFromController(Victim);
        
        // Track death (may increase frustration)
        TrackPlayerAction(VictimID, TEXT("Death"), false);
        
        // Check if victim might need emotional support
        CheckVictimForSupport(VictimID);
    }
}

void AHarmonyEngineGameMode::OnTeamworkAction(APlayerController* Player, const FString& TeamworkType)
{
    if (!bEnableHarmonyEngine || !HarmonyEngineSubsystem || !Player)
    {
        return;
    }
    
    FString PlayerID = GetPlayerIDFromController(Player);
    
    // Award kindness points for teamwork
    int32 TeamworkPoints = CalculateTeamworkKindnessPoints(TeamworkType);
    if (TeamworkPoints > 0)
    {
        HarmonyEngineSubsystem->AwardKindnessPoints(PlayerID, TeamworkPoints, 
            FString::Printf(TEXT("Teamwork: %s"), *TeamworkType));
    }
    
    // Track positive action
    TrackPlayerAction(PlayerID, TeamworkType, true);
    
    // Update behavior metrics
    UpdatePlayerBehaviorMetrics(PlayerID);
}

// Private implementation functions

void AHarmonyEngineGameMode::InitializeHarmonyEngineCallbacks()
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }
    
    // Bind to Harmony Engine events
    HarmonyEngineSubsystem->OnBehaviorDetected.AddDynamic(this, &AHarmonyEngineGameMode::OnHarmonyBehaviorDetected);
    HarmonyEngineSubsystem->OnInterventionTriggered.AddDynamic(this, &AHarmonyEngineGameMode::OnHarmonyInterventionTriggered);
    HarmonyEngineSubsystem->OnCommunityHealing.AddDynamic(this, &AHarmonyEngineGameMode::OnHarmonyCommunityHealing);
    HarmonyEngineSubsystem->OnKindnessReward.AddDynamic(this, &AHarmonyEngineGameMode::OnHarmonyKindnessReward);
    HarmonyEngineSubsystem->OnHarmonyLevelChanged.AddDynamic(this, &AHarmonyEngineGameMode::OnHarmonyLevelChanged);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine callbacks initialized"));
}

void AHarmonyEngineGameMode::CleanupHarmonyEngineCallbacks()
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }
    
    // Unbind from Harmony Engine events
    HarmonyEngineSubsystem->OnBehaviorDetected.RemoveDynamic(this, &AHarmonyEngineGameMode::OnHarmonyBehaviorDetected);
    HarmonyEngineSubsystem->OnInterventionTriggered.RemoveDynamic(this, &AHarmonyEngineGameMode::OnHarmonyInterventionTriggered);
    HarmonyEngineSubsystem->OnCommunityHealing.RemoveDynamic(this, &AHarmonyEngineGameMode::OnHarmonyCommunityHealing);
    HarmonyEngineSubsystem->OnKindnessReward.RemoveDynamic(this, &AHarmonyEngineGameMode::OnHarmonyKindnessReward);
    HarmonyEngineSubsystem->OnHarmonyLevelChanged.RemoveDynamic(this, &AHarmonyEngineGameMode::OnHarmonyLevelChanged);
    
    UE_LOG(LogHarmonyEngine, Log, TEXT("Harmony Engine callbacks cleaned up"));
}

void AHarmonyEngineGameMode::AnalyzeChatMessage(const FString& PlayerID, const FString& Message)
{
    // Analyze message for toxicity
    float ToxicityScore = AnalyzeMessageToxicity(Message);
    
    // Analyze message for positivity
    float PositivityScore = AnalyzeMessagePositivity(Message);
    
    // Create behavior snapshot
    FPlayerBehaviorSnapshot BehaviorData = CreateBehaviorSnapshot(PlayerID);
    BehaviorData.ToxicityScore = FMath::Max(BehaviorData.ToxicityScore, ToxicityScore);
    BehaviorData.PositivityScore = FMath::Max(BehaviorData.PositivityScore, PositivityScore);
    
    // Update action counts based on message analysis
    if (ToxicityScore > 0.5f)
    {
        BehaviorData.NegativeActionsCount++;
    }
    else if (PositivityScore > 0.5f)
    {
        BehaviorData.PositiveActionsCount++;
    }
    
    // Update Harmony Engine
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);
}

float AHarmonyEngineGameMode::AnalyzeMessageToxicity(const FString& Message)
{
    float ToxicityScore = 0.0f;
    FString LowerMessage = Message.ToLower();
    
    // Simple keyword-based toxicity detection
    TArray<FString> ToxicKeywords = {
        TEXT("noob"), TEXT("trash"), TEXT("garbage"), TEXT("idiot"), TEXT("stupid"),
        TEXT("hate"), TEXT("kill yourself"), TEXT("uninstall"), TEXT("ez"), TEXT("rekt")
    };
    
    for (const FString& Keyword : ToxicKeywords)
    {
        if (LowerMessage.Contains(Keyword))
        {
            ToxicityScore += 0.2f;
        }
    }
    
    // Check for excessive caps (shouting)
    int32 CapsCount = 0;
    for (TCHAR Char : Message)
    {
        if (FChar::IsUpper(Char))
        {
            CapsCount++;
        }
    }
    
    if (Message.Len() > 0)
    {
        float CapsRatio = static_cast<float>(CapsCount) / Message.Len();
        if (CapsRatio > 0.7f)
        {
            ToxicityScore += 0.3f;
        }
    }
    
    // Check for excessive punctuation (!!!, ???)
    int32 ExclamationCount = 0;
    int32 QuestionCount = 0;

    // Count characters manually
    for (int32 i = 0; i < Message.Len(); i++)
    {
        if (Message[i] == '!')
        {
            ExclamationCount++;
        }
        else if (Message[i] == '?')
        {
            QuestionCount++;
        }
    }
    
    if (ExclamationCount > 3 || QuestionCount > 3)
    {
        ToxicityScore += 0.1f;
    }
    
    return FMath::Clamp(ToxicityScore, 0.0f, 1.0f);
}

float AHarmonyEngineGameMode::AnalyzeMessagePositivity(const FString& Message)
{
    float PositivityScore = 0.0f;
    FString LowerMessage = Message.ToLower();
    
    // Positive keywords detection
    TArray<FString> PositiveKeywords = {
        TEXT("good job"), TEXT("well done"), TEXT("nice"), TEXT("awesome"), TEXT("great"),
        TEXT("thanks"), TEXT("thank you"), TEXT("please"), TEXT("sorry"), TEXT("good luck"),
        TEXT("gg"), TEXT("wp"), TEXT("well played"), TEXT("amazing"), TEXT("fantastic")
    };
    
    for (const FString& Keyword : PositiveKeywords)
    {
        if (LowerMessage.Contains(Keyword))
        {
            PositivityScore += 0.2f;
        }
    }
    
    // Encouraging phrases
    TArray<FString> EncouragingPhrases = {
        TEXT("you can do it"), TEXT("don't give up"), TEXT("keep trying"), TEXT("we got this"),
        TEXT("team work"), TEXT("let's go"), TEXT("nice try"), TEXT("almost had it")
    };
    
    for (const FString& Phrase : EncouragingPhrases)
    {
        if (LowerMessage.Contains(Phrase))
        {
            PositivityScore += 0.3f;
        }
    }
    
    // Check for helpful behavior
    if (LowerMessage.Contains(TEXT("help")) || LowerMessage.Contains(TEXT("assist")) || 
        LowerMessage.Contains(TEXT("support")))
    {
        PositivityScore += 0.2f;
    }
    
    return FMath::Clamp(PositivityScore, 0.0f, 1.0f);
}

void AHarmonyEngineGameMode::TrackPlayerAction(const FString& PlayerID, const FString& ActionType, bool bIsPositive)
{
    // Update action count
    int32& ActionCount = PlayerActionCounts.FindOrAdd(PlayerID);
    ActionCount++;
    
    // Update session duration
    UpdateSessionDuration(PlayerID);
    
    UE_LOG(LogHarmonyEngine, VeryVerbose, TEXT("Tracked action for player %s: %s (Positive: %s)"), 
        *PlayerID, *ActionType, bIsPositive ? TEXT("Yes") : TEXT("No"));
}

void AHarmonyEngineGameMode::UpdateSessionDuration(const FString& PlayerID)
{
    const FDateTime* JoinTime = PlayerJoinTimes.Find(PlayerID);
    if (JoinTime)
    {
        FDateTime CurrentTime = FDateTime::Now();
        float SessionDuration = (CurrentTime - *JoinTime).GetTotalSeconds();
        PlayerSessionDurations.Add(PlayerID, SessionDuration);
    }
}

FPlayerBehaviorSnapshot AHarmonyEngineGameMode::CreateBehaviorSnapshot(const FString& PlayerID)
{
    FPlayerBehaviorSnapshot Snapshot;
    Snapshot.PlayerID = PlayerID;
    Snapshot.Timestamp = FDateTime::Now();
    
    // Get session duration
    if (const float* Duration = PlayerSessionDurations.Find(PlayerID))
    {
        Snapshot.SessionDuration = *Duration;
    }
    
    // Initialize with current values (would be enhanced with actual behavior analysis)
    Snapshot.ToxicityScore = 0.0f;
    Snapshot.PositivityScore = 0.0f;
    Snapshot.FrustrationLevel = 0.0f;
    Snapshot.PositiveActionsCount = 0;
    Snapshot.NegativeActionsCount = 0;
    Snapshot.EmotionalState = EEmotionalState::Neutral;
    
    return Snapshot;
}

// Event handler implementations

void AHarmonyEngineGameMode::OnHarmonyBehaviorDetected(const FString& PlayerID, const FPlayerBehaviorSnapshot& BehaviorData)
{
    APlayerController* Player = GetPlayerControllerByID(PlayerID);
    if (!Player)
    {
        return;
    }
    
    // Trigger Blueprint events based on behavior type
    if (BehaviorData.ToxicityScore > ToxicityDetectionThreshold)
    {
        OnToxicBehaviorDetected(Player, BehaviorData.ToxicityScore);
    }
    else if (BehaviorData.PositivityScore > 0.6f)
    {
        OnPositiveBehaviorDetected(Player, BehaviorData.PositivityScore);
    }
}

void AHarmonyEngineGameMode::OnHarmonyInterventionTriggered(const FString& PlayerID, const FHarmonyInterventionData& InterventionData)
{
    APlayerController* Player = GetPlayerControllerByID(PlayerID);
    if (Player)
    {
        OnInterventionTriggered(Player, InterventionData.InterventionMessage);
    }
}

void AHarmonyEngineGameMode::OnHarmonyCommunityHealing(const FString& HealerID, const FString& VictimID)
{
    APlayerController* Healer = GetPlayerControllerByID(HealerID);
    APlayerController* Victim = GetPlayerControllerByID(VictimID);
    
    if (Healer && Victim)
    {
        OnCommunityHealingStarted(Victim, Healer);
    }
}

void AHarmonyEngineGameMode::OnHarmonyKindnessReward(const FString& PlayerID, const FKindnessReward& Reward)
{
    APlayerController* Player = GetPlayerControllerByID(PlayerID);
    if (Player)
    {
        OnKindnessRewardAwarded(Player, Reward.KindnessPoints, Reward.RewardDescription);
    }
}

void AHarmonyEngineGameMode::OnHarmonyLevelChanged(int32 NewHarmonyLevel)
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Community harmony level changed to: %d"), NewHarmonyLevel);
    
    // Trigger community-wide events based on harmony level
    if (NewHarmonyLevel > 80)
    {
        TriggerPositiveCommunityEvent();
    }
    else if (NewHarmonyLevel < 30)
    {
        TriggerCommunityHealingEvent();
    }
}

// Utility function implementations

APlayerController* AHarmonyEngineGameMode::GetPlayerControllerByID(const FString& PlayerID)
{
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC && PC->GetPlayerState<APlayerState>())
            {
                FString CurrentPlayerID = PC->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
                if (CurrentPlayerID == PlayerID)
                {
                    return PC;
                }
            }
        }
    }
    return nullptr;
}

FString AHarmonyEngineGameMode::GetPlayerIDFromController(APlayerController* PlayerController)
{
    if (PlayerController && PlayerController->GetPlayerState<APlayerState>())
    {
        return PlayerController->GetPlayerState<APlayerState>()->GetUniqueId().ToString();
    }
    return TEXT("");
}

int32 AHarmonyEngineGameMode::CalculateActionKindnessPoints(const FString& ActionType)
{
    // Award points based on action type
    if (ActionType == TEXT("Help"))
    {
        return 10;
    }
    else if (ActionType == TEXT("Revive"))
    {
        return 15;
    }
    else if (ActionType == TEXT("Share"))
    {
        return 8;
    }
    else if (ActionType == TEXT("Encourage"))
    {
        return 12;
    }
    else if (ActionType == TEXT("Teach"))
    {
        return 20;
    }
    
    return 0;
}

int32 AHarmonyEngineGameMode::CalculateTeamworkKindnessPoints(const FString& TeamworkType)
{
    // Award points based on teamwork type
    if (TeamworkType == TEXT("Assist"))
    {
        return 5;
    }
    else if (TeamworkType == TEXT("Coordinate"))
    {
        return 8;
    }
    else if (TeamworkType == TEXT("Sacrifice"))
    {
        return 15;
    }
    else if (TeamworkType == TEXT("Leadership"))
    {
        return 12;
    }
    
    return 0;
}

// Implementation of missing functions

void AHarmonyEngineGameMode::UpdatePlayerBehaviorMetrics(const FString& PlayerID)
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }

    // Create comprehensive behavior snapshot
    FPlayerBehaviorSnapshot BehaviorData = CreateBehaviorSnapshot(PlayerID);

    // Calculate current metrics based on recent actions
    int32 ActionCount = PlayerActionCounts.Contains(PlayerID) ? PlayerActionCounts[PlayerID] : 0;
    float SessionDuration = PlayerSessionDurations.Contains(PlayerID) ? PlayerSessionDurations[PlayerID] : 0.0f;

    // Estimate frustration based on session length and action patterns
    if (SessionDuration > 7200.0f) // 2 hours
    {
        BehaviorData.FrustrationLevel += 0.1f;
    }
    if (SessionDuration > 10800.0f) // 3 hours
    {
        BehaviorData.FrustrationLevel += 0.2f;
    }

    // Update session duration
    BehaviorData.SessionDuration = SessionDuration;

    // Update Harmony Engine with new behavior data
    HarmonyEngineSubsystem->UpdatePlayerBehavior(PlayerID, BehaviorData);
}

void AHarmonyEngineGameMode::CheckForInterventionNeeds(const FString& PlayerID)
{
    if (!HarmonyEngineSubsystem || !bEnableAutomaticInterventions)
    {
        return;
    }

    // Check if player is at risk
    if (HarmonyEngineSubsystem->IsPlayerAtRisk(PlayerID))
    {
        float ToxicityScore = HarmonyEngineSubsystem->GetPlayerToxicityScore(PlayerID);
        EEmotionalState EmotionalState = HarmonyEngineSubsystem->GetPlayerEmotionalState(PlayerID);

        // Determine intervention type based on risk level
        EInterventionType InterventionType = EInterventionType::Gentle;
        FString Reason = TEXT("Preventive intervention");

        if (ToxicityScore > 0.8f)
        {
            InterventionType = EInterventionType::Strong;
            Reason = TEXT("High toxicity detected");
        }
        else if (ToxicityScore > 0.6f || EmotionalState == EEmotionalState::Angry)
        {
            InterventionType = EInterventionType::Moderate;
            Reason = TEXT("Escalating negative behavior");
        }

        // Trigger intervention
        HarmonyEngineSubsystem->TriggerIntervention(PlayerID, InterventionType, Reason);
    }
}

bool AHarmonyEngineGameMode::IsKillPotentiallyToxic(APlayerController* Killer, APlayerController* Victim)
{
    if (!Killer || !Victim)
    {
        return false;
    }

    FString KillerID = GetPlayerIDFromController(Killer);
    FString VictimID = GetPlayerIDFromController(Victim);

    // Check for spawn camping (killing same player repeatedly in short time)
    static TMap<FString, TMap<FString, FDateTime>> RecentKills;

    FDateTime CurrentTime = FDateTime::Now();
    TMap<FString, FDateTime>& KillerRecentKills = RecentKills.FindOrAdd(KillerID);

    if (const FDateTime* LastKillTime = KillerRecentKills.Find(VictimID))
    {
        float TimeSinceLastKill = (CurrentTime - *LastKillTime).GetTotalSeconds();
        if (TimeSinceLastKill < 30.0f) // Killed same player within 30 seconds
        {
            return true; // Potential spawn camping
        }
    }

    // Update recent kills tracking
    KillerRecentKills.Add(VictimID, CurrentTime);

    // Clean up old entries (older than 5 minutes)
    for (auto It = KillerRecentKills.CreateIterator(); It; ++It)
    {
        if ((CurrentTime - It.Value()).GetTotalSeconds() > 300.0f)
        {
            It.RemoveCurrent();
        }
    }

    return false;
}

void AHarmonyEngineGameMode::CheckVictimForSupport(const FString& VictimID)
{
    if (!HarmonyEngineSubsystem)
    {
        return;
    }

    // Check if victim has died multiple times recently
    static TMap<FString, TArray<FDateTime>> RecentDeaths;

    FDateTime CurrentTime = FDateTime::Now();
    TArray<FDateTime>& VictimDeaths = RecentDeaths.FindOrAdd(VictimID);
    VictimDeaths.Add(CurrentTime);

    // Remove deaths older than 5 minutes
    VictimDeaths.RemoveAll([CurrentTime](const FDateTime& DeathTime) {
        return (CurrentTime - DeathTime).GetTotalSeconds() > 300.0f;
    });

    // If player has died 3+ times in 5 minutes, they might need support
    if (VictimDeaths.Num() >= 3)
    {
        // Create behavior snapshot indicating frustration
        FPlayerBehaviorSnapshot BehaviorData = CreateBehaviorSnapshot(VictimID);
        BehaviorData.FrustrationLevel = 0.7f;
        BehaviorData.EmotionalState = EEmotionalState::Frustrated;
        BehaviorData.NegativeActionsCount += VictimDeaths.Num();

        // Update behavior and potentially trigger intervention
        HarmonyEngineSubsystem->UpdatePlayerBehavior(VictimID, BehaviorData);

        // Suggest community healing if enabled
        if (bEnableCommunityHealing)
        {
            HarmonyEngineSubsystem->InitiateCommunityHealing(VictimID, TEXT(""));
        }
    }
}

void AHarmonyEngineGameMode::TriggerPositiveCommunityEvent()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Triggering positive community event"));

    // Award bonus kindness points to all players
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC)
            {
                FString PlayerID = GetPlayerIDFromController(PC);
                if (!PlayerID.IsEmpty())
                {
                    HarmonyEngineSubsystem->AwardKindnessPoints(PlayerID, 25, TEXT("Community Harmony Bonus"));
                }
            }
        }
    }

    // Broadcast positive message to all players
    BroadcastMessage(TEXT("🌟 COMMUNITY HARMONY HIGH! Everyone receives bonus kindness points! Keep up the amazing positivity! 🌟"));
}

void AHarmonyEngineGameMode::TriggerCommunityHealingEvent()
{
    UE_LOG(LogHarmonyEngine, Log, TEXT("Triggering community healing event"));

    // Broadcast healing message to all players
    BroadcastMessage(TEXT("💙 COMMUNITY HEALING ACTIVATED! Let's support each other and spread kindness! Together we're stronger! 💙"));

    // Activate healing bonuses
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC)
            {
                FString PlayerID = GetPlayerIDFromController(PC);
                if (!PlayerID.IsEmpty())
                {
                    // Double kindness points for next 10 minutes
                    ActivateHealingBonus(PlayerID);
                }
            }
        }
    }
}

void AHarmonyEngineGameMode::BroadcastMessage(const FString& Message)
{
    // Broadcast message to all players
    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            APlayerController* PC = Iterator->Get();
            if (PC)
            {
                // In a full implementation, this would use the game's chat/notification system
                UE_LOG(LogHarmonyEngine, Log, TEXT("Broadcasting to player: %s"), *Message);

                // Example: PC->ClientReceiveSystemMessage(Message);
            }
        }
    }
}

void AHarmonyEngineGameMode::ActivateHealingBonus(const FString& PlayerID)
{
    // Activate temporary healing bonus for player
    UE_LOG(LogHarmonyEngine, Log, TEXT("Activated healing bonus for player: %s"), *PlayerID);

    // In a full implementation, this would apply a temporary GameplayEffect
    // that doubles kindness point rewards for a limited time
}
