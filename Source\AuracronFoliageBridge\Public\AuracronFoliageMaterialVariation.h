// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Material Variation Header
// Bridge 4.11: Foliage - Material Variation

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"
#include "AuracronFoliageInstanced.h"
#include "AuracronFoliageBiome.h"
#include "AuracronFoliageSeasonal.h"
// #include "AuracronPCGMaterialSystem.h" // Will be available when PCG material system is implemented

// UE5.6 Material includes
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Materials/MaterialInstanceConstant.h"

// Foliage includes
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "InstancedFoliageActor.h"

// Texture includes
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/StaticMesh.h"

// Math includes
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Color.h"

// Async includes
#include "Async/Async.h"
#include "Async/TaskGraphInterfaces.h"

#include "AuracronFoliageMaterialVariation.generated.h"

// Forward declarations
class UAuracronFoliageMaterialVariationManager;
class UAuracronFoliageSeasonalManager;

// =============================================================================
// MATERIAL VARIATION TYPES AND ENUMS
// =============================================================================

// Material variation strategy
UENUM(BlueprintType)
enum class EAuracronMaterialVariationStrategy : uint8
{
    None                    UMETA(DisplayName = "None"),
    ColorOnly               UMETA(DisplayName = "Color Only"),
    TextureBlending         UMETA(DisplayName = "Texture Blending"),
    ParameterVariation      UMETA(DisplayName = "Parameter Variation"),
    InstanceSwapping        UMETA(DisplayName = "Instance Swapping"),
    ProceduralGeneration    UMETA(DisplayName = "Procedural Generation"),
    Hybrid                  UMETA(DisplayName = "Hybrid Strategy"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Color variation mode
UENUM(BlueprintType)
enum class EAuracronColorVariationMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    HSV                     UMETA(DisplayName = "HSV Variation"),
    RGB                     UMETA(DisplayName = "RGB Variation"),
    Palette                 UMETA(DisplayName = "Palette Based"),
    Gradient                UMETA(DisplayName = "Gradient Based"),
    Seasonal                UMETA(DisplayName = "Seasonal Colors"),
    Environmental           UMETA(DisplayName = "Environmental"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Texture blending mode
UENUM(BlueprintType)
enum class EAuracronTextureBlendingMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    Multiply                UMETA(DisplayName = "Multiply"),
    Screen                  UMETA(DisplayName = "Screen"),
    Overlay                 UMETA(DisplayName = "Overlay"),
    SoftLight               UMETA(DisplayName = "Soft Light"),
    HardLight               UMETA(DisplayName = "Hard Light"),
    ColorDodge              UMETA(DisplayName = "Color Dodge"),
    ColorBurn               UMETA(DisplayName = "Color Burn"),
    Darken                  UMETA(DisplayName = "Darken"),
    Lighten                 UMETA(DisplayName = "Lighten"),
    Difference              UMETA(DisplayName = "Difference"),
    Exclusion               UMETA(DisplayName = "Exclusion"),
    Custom                  UMETA(DisplayName = "Custom")
};

// Material assignment mode
UENUM(BlueprintType)
enum class EAuracronMaterialAssignmentMode : uint8
{
    Random                  UMETA(DisplayName = "Random"),
    DistanceBased           UMETA(DisplayName = "Distance Based"),
    DensityBased            UMETA(DisplayName = "Density Based"),
    BiomeBased              UMETA(DisplayName = "Biome Based"),
    SeasonalBased           UMETA(DisplayName = "Seasonal Based"),
    AttributeBased          UMETA(DisplayName = "Attribute Based"),
    RuleBased               UMETA(DisplayName = "Rule Based"),
    Custom                  UMETA(DisplayName = "Custom")
};

// =============================================================================
// MATERIAL VARIATION CONFIGURATION
// =============================================================================

/**
 * Foliage Material Variation Configuration
 * Configuration for material variation system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageMaterialVariationConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Variation")
    bool bEnableMaterialVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Variation")
    EAuracronMaterialVariationStrategy VariationStrategy = EAuracronMaterialVariationStrategy::Hybrid;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    bool bEnableColorVariation = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    EAuracronColorVariationMode ColorVariationMode = EAuracronColorVariationMode::HSV;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ColorVariationIntensity = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    FLinearColor BaseColorTint = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float HueVariationRange = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float SaturationVariationRange = 0.2f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float ValueVariationRange = 0.15f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    bool bEnableTextureBlending = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    EAuracronTextureBlendingMode TextureBlendingMode = EAuracronTextureBlendingMode::Overlay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    float BlendingOpacity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    TArray<TSoftObjectPtr<UTexture2D>> BlendingTextures;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Assignment")
    EAuracronMaterialAssignmentMode AssignmentMode = EAuracronMaterialAssignmentMode::BiomeBased;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Assignment")
    TArray<TSoftObjectPtr<UMaterialInterface>> MaterialPool;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Assignment")
    TArray<float> MaterialWeights;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    bool bEnableProceduralMaterials = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    int32 ProceduralSeed = 12345;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Procedural Generation")
    float ProceduralVariationStrength = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncMaterialGeneration = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxMaterialInstancesPerFrame = 50;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaterialUpdateInterval = 0.1f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableMaterialCaching = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxCachedMaterials = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bIntegrateWithSeasonalSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bIntegrateWithBiomeSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Integration")
    bool bIntegrateWithWindSystem = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogMaterialVariations = false;

    FAuracronFoliageMaterialVariationConfiguration()
    {
        bEnableMaterialVariation = true;
        VariationStrategy = EAuracronMaterialVariationStrategy::Hybrid;
        bEnableColorVariation = true;
        ColorVariationMode = EAuracronColorVariationMode::HSV;
        ColorVariationIntensity = 0.3f;
        BaseColorTint = FLinearColor::White;
        HueVariationRange = 0.1f;
        SaturationVariationRange = 0.2f;
        ValueVariationRange = 0.15f;
        bEnableTextureBlending = true;
        TextureBlendingMode = EAuracronTextureBlendingMode::Overlay;
        BlendingOpacity = 0.5f;
        AssignmentMode = EAuracronMaterialAssignmentMode::BiomeBased;
        bEnableProceduralMaterials = true;
        ProceduralSeed = 12345;
        ProceduralVariationStrength = 1.0f;
        bEnableAsyncMaterialGeneration = true;
        MaxMaterialInstancesPerFrame = 50;
        MaterialUpdateInterval = 0.1f;
        bEnableMaterialCaching = true;
        MaxCachedMaterials = 100;
        bIntegrateWithSeasonalSystem = true;
        bIntegrateWithBiomeSystem = true;
        bIntegrateWithWindSystem = true;
        bEnableDebugVisualization = false;
        bLogMaterialVariations = false;
    }
};

// =============================================================================
// MATERIAL VARIATION DATA STRUCTURES
// =============================================================================

/**
 * Color Variation Data
 * Data for color variation of foliage materials
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronColorVariationData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    FString VariationId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    FString FoliageTypeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    EAuracronColorVariationMode VariationMode = EAuracronColorVariationMode::HSV;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    FLinearColor BaseColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    FLinearColor VariationColor = FLinearColor::White;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Color Variation")
    float VariationIntensity = 0.3f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HSV Variation")
    float HueShift = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HSV Variation")
    float SaturationMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HSV Variation")
    float ValueMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Palette")
    TArray<FLinearColor> ColorPalette;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gradient")
    TArray<FLinearColor> GradientColors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gradient")
    TArray<float> GradientStops;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime CreationTime;

    FAuracronColorVariationData()
    {
        VariationMode = EAuracronColorVariationMode::HSV;
        BaseColor = FLinearColor::White;
        VariationColor = FLinearColor::White;
        VariationIntensity = 0.3f;
        HueShift = 0.0f;
        SaturationMultiplier = 1.0f;
        ValueMultiplier = 1.0f;
        bIsActive = true;
        CreationTime = FDateTime::Now();
    }
};

/**
 * Texture Blending Data
 * Data for texture blending in foliage materials
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronTextureBlendingData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    FString BlendingId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    FString FoliageTypeId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    EAuracronTextureBlendingMode BlendingMode = EAuracronTextureBlendingMode::Overlay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    TSoftObjectPtr<UTexture2D> BaseTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    TSoftObjectPtr<UTexture2D> BlendTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    TSoftObjectPtr<UTexture2D> MaskTexture;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    float BlendOpacity = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    FVector2D TextureScale = FVector2D(1.0f, 1.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    FVector2D TextureOffset = FVector2D(0.0f, 0.0f);

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Texture Blending")
    float TextureRotation = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bUseWorldSpaceUV = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
    bool bInvertMask = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsActive = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime CreationTime;

    FAuracronTextureBlendingData()
    {
        BlendingMode = EAuracronTextureBlendingMode::Overlay;
        BlendOpacity = 0.5f;
        TextureScale = FVector2D(1.0f, 1.0f);
        TextureOffset = FVector2D(0.0f, 0.0f);
        TextureRotation = 0.0f;
        bUseWorldSpaceUV = false;
        bInvertMask = false;
        bIsActive = true;
        CreationTime = FDateTime::Now();
    }
};

/**
 * Material Instance Data
 * Data for dynamic material instances
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronMaterialInstanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Instance")
    FString InstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Instance")
    FString FoliageInstanceId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Instance")
    TSoftObjectPtr<UMaterialInterface> BaseMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Instance")
    TWeakObjectPtr<UMaterialInstanceDynamic> DynamicMaterial;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, float> ScalarParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, FLinearColor> VectorParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
    TMap<FString, TSoftObjectPtr<UTexture>> TextureParameters;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    FAuracronColorVariationData ColorVariation;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variation")
    FAuracronTextureBlendingData TextureBlending;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bIsGenerated = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    bool bNeedsUpdate = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
    FDateTime LastUpdateTime;

    FAuracronMaterialInstanceData()
    {
        bIsGenerated = false;
        bNeedsUpdate = false;
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Material Variation Performance Data
 * Performance metrics for material variation system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronMaterialVariationPerformanceData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TotalMaterialInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ActiveMaterialInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 CachedMaterialInstances = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ColorVariations = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 TextureBlends = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaterialGenerationTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float ColorVariationTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float TextureBlendingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MemoryUsageMB = 0.0f;

    FAuracronMaterialVariationPerformanceData()
    {
        TotalMaterialInstances = 0;
        ActiveMaterialInstances = 0;
        CachedMaterialInstances = 0;
        ColorVariations = 0;
        TextureBlends = 0;
        MaterialGenerationTime = 0.0f;
        ColorVariationTime = 0.0f;
        TextureBlendingTime = 0.0f;
        MemoryUsageMB = 0.0f;
    }
};

// =============================================================================
// FOLIAGE MATERIAL VARIATION MANAGER
// =============================================================================

/**
 * Foliage Material Variation Manager
 * Manager for foliage material variation system
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageMaterialVariationManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    static UAuracronFoliageMaterialVariationManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void Initialize(const FAuracronFoliageMaterialVariationConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void Tick(float DeltaTime);

    // Configuration management
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void SetConfiguration(const FAuracronFoliageMaterialVariationConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FAuracronFoliageMaterialVariationConfiguration GetConfiguration() const;

    // Material instance management
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FString CreateMaterialInstance(const FString& FoliageInstanceId, UMaterialInterface* BaseMaterial);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool UpdateMaterialInstance(const FString& InstanceId, const FAuracronMaterialInstanceData& InstanceData);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool RemoveMaterialInstance(const FString& InstanceId);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FAuracronMaterialInstanceData GetMaterialInstance(const FString& InstanceId) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    TArray<FAuracronMaterialInstanceData> GetAllMaterialInstances() const;

    // Color variation
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FString CreateColorVariation(const FString& FoliageTypeId, const FAuracronColorVariationData& ColorData);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool UpdateColorVariation(const FString& VariationId, const FAuracronColorVariationData& ColorData);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool RemoveColorVariation(const FString& VariationId);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FAuracronColorVariationData GetColorVariation(const FString& VariationId) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FLinearColor ApplyColorVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const;

    // Texture blending
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FString CreateTextureBlending(const FString& FoliageTypeId, const FAuracronTextureBlendingData& BlendingData);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool UpdateTextureBlending(const FString& BlendingId, const FAuracronTextureBlendingData& BlendingData);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool RemoveTextureBlending(const FString& BlendingId);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FAuracronTextureBlendingData GetTextureBlending(const FString& BlendingId) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    UTexture2D* ApplyTextureBlending(UTexture2D* BaseTexture, UTexture2D* BlendTexture, const FAuracronTextureBlendingData& BlendingData) const;

    // Procedural material assignment
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    UMaterialInterface* AssignProceduralMaterial(const FString& FoliageInstanceId, const FVector& Location, const FString& BiomeId);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    TArray<UMaterialInterface*> GetMaterialsForBiome(const FString& BiomeId) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    UMaterialInterface* SelectMaterialByDistance(const FVector& Location, float Distance) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    UMaterialInterface* SelectMaterialByDensity(float Density) const;

    // Integration with other systems
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void IntegrateWithSeasonalSystem(UAuracronFoliageSeasonalManager* SeasonalManager);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void ApplySeasonalMaterialChanges(EAuracronSeasonType Season, float SeasonProgress);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void SynchronizeWithBiomeSystem(const FString& BiomeId);

    // Performance monitoring
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    FAuracronMaterialVariationPerformanceData GetPerformanceData() const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void UpdatePerformanceMetrics();

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    int32 GetActiveMaterialInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    float GetMaterialMemoryUsage() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void DrawDebugMaterialInfo(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "Material Variation Manager")
    void LogMaterialVariationStatistics() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMaterialInstanceCreated, FString, InstanceId, UMaterialInstanceDynamic*, MaterialInstance);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMaterialInstanceRemoved, FString, InstanceId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnColorVariationApplied, FString, VariationId, FLinearColor, ResultColor);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTextureBlendingCompleted, FString, BlendingId, UTexture2D*, ResultTexture);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMaterialInstanceCreated OnMaterialInstanceCreated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnMaterialInstanceRemoved OnMaterialInstanceRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnColorVariationApplied OnColorVariationApplied;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnTextureBlendingCompleted OnTextureBlendingCompleted;

private:
    static UAuracronFoliageMaterialVariationManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronFoliageMaterialVariationConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Material variation data
    TMap<FString, FAuracronMaterialInstanceData> MaterialInstances;
    TMap<FString, FAuracronColorVariationData> ColorVariations;
    TMap<FString, FAuracronTextureBlendingData> TextureBlending;

    // Material cache
    TMap<FString, TWeakObjectPtr<UMaterialInstanceDynamic>> MaterialCache;
    TMap<FString, TWeakObjectPtr<UTexture2D>> TextureCache;

    // Integration with other systems
    UPROPERTY()
    TWeakObjectPtr<UAuracronFoliageSeasonalManager> SeasonalManager;

    // Performance data
    FAuracronMaterialVariationPerformanceData PerformanceData;
    float LastPerformanceUpdate = 0.0f;
    float LastMaterialUpdate = 0.0f;

    // Debug settings
    bool bDebugVisualizationEnabled = false;

    // Thread safety
    mutable FCriticalSection MaterialVariationLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateMaterialInstanceId() const;
    FString GenerateColorVariationId() const;
    FString GenerateTextureBlendingId() const;
    void UpdateMaterialInstancesInternal(float DeltaTime);
    void UpdateColorVariationsInternal(float DeltaTime);
    void UpdateTextureBlendingInternal(float DeltaTime);
    void UpdatePerformanceDataInternal();
    UMaterialInstanceDynamic* CreateDynamicMaterialInstanceInternal(UMaterialInterface* BaseMaterial, const FAuracronMaterialInstanceData& InstanceData);
    FLinearColor CalculateHSVVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const;
    FLinearColor CalculateRGBVariation(const FLinearColor& BaseColor, const FAuracronColorVariationData& VariationData) const;
    FLinearColor SelectColorFromPalette(const FAuracronColorVariationData& VariationData, float SelectionValue) const;
    FLinearColor InterpolateGradient(const FAuracronColorVariationData& VariationData, float InterpolationValue) const;
    UTexture2D* BlendTexturesInternal(UTexture2D* BaseTexture, UTexture2D* BlendTexture, const FAuracronTextureBlendingData& BlendingData) const;
    UMaterialInterface* SelectMaterialByRules(const FVector& Location, const FString& BiomeId, const TArray<FString>& Rules) const;
    void CleanupUnusedMaterials();
    void CleanupUnusedTextures();
};
