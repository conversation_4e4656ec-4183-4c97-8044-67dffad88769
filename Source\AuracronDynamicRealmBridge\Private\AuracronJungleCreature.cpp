/**
 * AuracronJungleCreature.cpp
 * 
 * Implementation of jungle creatures that can be adapted by the Adaptive Jungle AI system.
 * Provides AI-driven behavior adaptation, stat modification, and environmental response.
 * 
 * Uses UE 5.6 modern AI, behavior tree, and ability system frameworks.
 */

#include "AuracronJungleCreature.h"
#include "AuracronAdaptiveJungleAI.h"
#include "Components/AudioComponent.h"
#include "NiagaraComponent.h"
#include "AbilitySystemComponent.h"
#include "AttributeSet.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "EngineUtils.h"
#include "Engine/Engine.h"

AAuracronJungleCreature::AAuracronJungleCreature()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    PrimaryActorTick.TickInterval = 0.5f; // 2 FPS for creature updates

    // Create ability system component
    AbilitySystemComponent = CreateDefaultSubobject<UAbilitySystemComponent>(TEXT("AbilitySystemComponent"));
    AbilitySystemComponent->SetIsReplicated(true);
    AbilitySystemComponent->SetReplicationMode(EGameplayEffectReplicationMode::Minimal);

    // Create attribute set
    AttributeSet = CreateDefaultSubobject<UAttributeSet>(TEXT("AttributeSet"));

    // Create audio component
    CreatureAudio = CreateDefaultSubobject<UAudioComponent>(TEXT("CreatureAudio"));
    CreatureAudio->SetupAttachment(RootComponent);
    CreatureAudio->SetAutoActivate(false);

    // Create VFX component
    CreatureVFX = CreateDefaultSubobject<UNiagaraComponent>(TEXT("CreatureVFX"));
    CreatureVFX->SetupAttachment(RootComponent);
    CreatureVFX->SetAutoActivate(false);

    // Initialize default values
    CreatureType = EJungleCreatureType::Predator;
    CurrentBehaviorState = ECreatureBehaviorState::Idle;
    BaseMovementSpeed = 400.0f;
    TerritorialRadius = 800.0f;
    DetectionRange = 1200.0f;
    bEnableAIAdaptation = true;

    // Initialize state
    bIsInitialized = false;
    LastAdaptationTime = 0.0f;
    LastBehaviorUpdateTime = 0.0f;
    RegisteredAISystem = nullptr;
    CurrentTargetPlayer = nullptr;

    // Configure character movement
    if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
    {
        MovementComp->MaxWalkSpeed = BaseMovementSpeed;
        MovementComp->bOrientRotationToMovement = true;
        MovementComp->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
    }

    // Configure collision
    if (UCapsuleComponent* CapsuleComp = GetCapsuleComponent())
    {
        CapsuleComp->SetCollisionObjectType(ECollisionChannel::ECC_Pawn);
        CapsuleComp->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Block);
    }
}

void AAuracronJungleCreature::BeginPlay()
{
    Super::BeginPlay();

    // Initialize jungle creature using UE 5.6 initialization patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Jungle Creature BeginPlay - Type: %s"), *UEnum::GetValueAsString(CreatureType));

    if (GetWorld())
    {
        // Store home location
        HomeLocation = GetActorLocation();
        
        // Delay initialization to ensure all systems are ready
        GetWorld()->GetTimerManager().SetTimerForNextTick([this]()
        {
            InitializeCreatureAI();
        });
    }
}

void AAuracronJungleCreature::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    // Clean up jungle creature using UE 5.6 cleanup patterns
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Jungle Creature EndPlay - Cleaning up..."));

    // Clear all timers
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().ClearAllTimersForObject(this);
    }

    // Unregister from AI system
    if (RegisteredAISystem)
    {
        UnregisterFromAdaptiveAI();
    }

    // Remove any active adaptations
    if (AdaptationStatus.bIsAdapted)
    {
        RemoveAIAdaptation();
    }

    bIsInitialized = false;

    Super::EndPlay(EndPlayReason);
}

void AAuracronJungleCreature::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    if (!bIsInitialized)
    {
        return;
    }

    // Update creature using UE 5.6 tick optimization
    LastBehaviorUpdateTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;

    // Update creature behavior
    UpdateCreatureBehavior(DeltaTime);

    // Update adaptation status
    UpdateAdaptationStatus(DeltaTime);

    // Update territorial behavior if needed
    if (CurrentBehaviorState == ECreatureBehaviorState::Territorial)
    {
        UpdateTerritorialBehavior();
    }

    // Update social behavior if needed
    if (CurrentBehaviorState == ECreatureBehaviorState::Social)
    {
        UpdateSocialBehavior();
    }
}

UAbilitySystemComponent* AAuracronJungleCreature::GetAbilitySystemComponent() const
{
    return AbilitySystemComponent;
}

// === Core Creature Management Implementation ===

EJungleCreatureType AAuracronJungleCreature::GetCreatureType() const
{
    return CreatureType;
}

void AAuracronJungleCreature::SetCreatureType(EJungleCreatureType NewType)
{
    if (CreatureType != NewType)
    {
        CreatureType = NewType;
        
        // Reconfigure creature for new type
        if (bIsInitialized)
        {
            ReconfigureForType();
        }
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature type changed to %s"), *UEnum::GetValueAsString(CreatureType));
    }
}

ECreatureBehaviorState AAuracronJungleCreature::GetBehaviorState() const
{
    return CurrentBehaviorState;
}

void AAuracronJungleCreature::SetBehaviorState(ECreatureBehaviorState NewState)
{
    if (CurrentBehaviorState != NewState)
    {
        ECreatureBehaviorState PreviousState = CurrentBehaviorState;
        CurrentBehaviorState = NewState;
        
        // Process behavior transition
        ProcessBehaviorTransition(NewState);
        
        UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature %s behavior changed from %s to %s"), 
            *GetName(), *UEnum::GetValueAsString(PreviousState), *UEnum::GetValueAsString(NewState));
    }
}

FAuracronCreatureAdaptationStatus AAuracronJungleCreature::GetAdaptationStatus() const
{
    return AdaptationStatus;
}

bool AAuracronJungleCreature::IsCreatureAdapted() const
{
    return AdaptationStatus.bIsAdapted;
}

// === AI Integration Implementation ===

void AAuracronJungleCreature::RegisterWithAdaptiveAI(AAuracronAdaptiveJungleAI* AISystem)
{
    if (!AISystem)
    {
        return;
    }

    // Register with adaptive AI system using UE 5.6 registration system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Registering creature %s with adaptive AI"), *GetName());

    RegisteredAISystem = AISystem;

    // Register with AI system using public method
    AISystem->RegisterCreature(this);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature %s registered with adaptive AI"), *GetName());
}

void AAuracronJungleCreature::UnregisterFromAdaptiveAI()
{
    if (!RegisteredAISystem)
    {
        return;
    }

    // Unregister from adaptive AI system using UE 5.6 cleanup system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Unregistering creature %s from adaptive AI"), *GetName());

    // Unregister from AI system using public method
    RegisteredAISystem->UnregisterCreature(this);

    RegisteredAISystem = nullptr;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature %s unregistered from adaptive AI"), *GetName());
}

void AAuracronJungleCreature::ApplyAIAdaptation(const FAuracronCreatureAdaptation& Adaptation)
{
    if (!bEnableAIAdaptation)
    {
        return;
    }

    // Apply AI adaptation using UE 5.6 adaptation system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Applying AI adaptation to creature %s - Type: %s"), 
        *GetName(), *UEnum::GetValueAsString(Adaptation.AdaptationType));

    // Update adaptation status
    AdaptationStatus.bIsAdapted = true;
    AdaptationStatus.CurrentAdaptationType = static_cast<EJungleCreatureAdaptationType>(Adaptation.AdaptationType);
    AdaptationStatus.AdaptationStrength = Adaptation.AdaptationStrength;
    AdaptationStatus.AdaptationStartTime = GetWorld() ? GetWorld()->GetTimeSeconds() : 0.0f;
    AdaptationStatus.AdaptationDuration = Adaptation.AdaptationDuration;

    // Set behavior state to adapted
    SetBehaviorState(ECreatureBehaviorState::Adapted);

    // Trigger adaptation event
    OnCreatureAdapted(static_cast<EJungleCreatureAdaptationType>(Adaptation.AdaptationType));

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI adaptation applied to creature %s"), *GetName());
}

void AAuracronJungleCreature::RemoveAIAdaptation()
{
    if (!AdaptationStatus.bIsAdapted)
    {
        return;
    }

    // Remove AI adaptation using UE 5.6 cleanup system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Removing AI adaptation from creature %s"), *GetName());

    // Reset adaptation status
    AdaptationStatus.bIsAdapted = false;
    AdaptationStatus.AdaptationStrength = 1.0f;
    AdaptationStatus.TargetPlayer = nullptr;

    // Return to default behavior
    SetBehaviorState(ECreatureBehaviorState::Idle);

    // Trigger adaptation removal event
    OnAdaptationRemoved();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: AI adaptation removed from creature %s"), *GetName());
}

// === Behavior Management Implementation ===

void AAuracronJungleCreature::UpdateCreatureBehavior(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update creature behavior using UE 5.6 behavior system

    // Check for nearby players
    TArray<APawn*> NearbyPlayers = GetNearbyPlayers(DetectionRange);

    if (!NearbyPlayers.IsEmpty())
    {
        // React to closest player
        APawn* ClosestPlayer = GetClosestPlayer(NearbyPlayers);
        if (ClosestPlayer != CurrentTargetPlayer)
        {
            CurrentTargetPlayer = ClosestPlayer;
            ReactToPlayerPresence(ClosestPlayer);
        }
    }
    else
    {
        // No players nearby - return to idle or patrol
        if (CurrentBehaviorState != ECreatureBehaviorState::Idle &&
            CurrentBehaviorState != ECreatureBehaviorState::Patrolling)
        {
            SetBehaviorState(ECreatureBehaviorState::Idle);
        }
        CurrentTargetPlayer = nullptr;
    }

    // Update behavior-specific logic
    switch (CurrentBehaviorState)
    {
        case ECreatureBehaviorState::Patrolling:
            UpdatePatrolBehavior(DeltaTime);
            break;
        case ECreatureBehaviorState::Hunting:
            UpdateHuntingBehavior(DeltaTime);
            break;
        case ECreatureBehaviorState::Fleeing:
            UpdateFleeingBehavior(DeltaTime);
            break;
        case ECreatureBehaviorState::Investigating:
            UpdateInvestigatingBehavior(DeltaTime);
            break;
        default:
            break;
    }
}

void AAuracronJungleCreature::ReactToPlayerPresence(APawn* Player)
{
    if (!Player)
    {
        return;
    }

    // React to player presence using UE 5.6 reaction system
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature %s reacting to player %s"), *GetName(), *Player->GetName());

    // Determine reaction based on creature type and adaptation
    ECreatureBehaviorState NewBehaviorState = DetermineReactionBehavior(Player);
    SetBehaviorState(NewBehaviorState);

    // Update AI blackboard with player information
    if (AAIController* AIController = Cast<AAIController>(GetController()))
    {
        if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
        {
            BlackboardComp->SetValueAsObject(TEXT("TargetPlayer"), Player);
            BlackboardComp->SetValueAsVector(TEXT("PlayerLocation"), Player->GetActorLocation());
            BlackboardComp->SetValueAsFloat(TEXT("PlayerDistance"), FVector::Dist(GetActorLocation(), Player->GetActorLocation()));
        }
    }

    // Trigger player detection event
    OnPlayerDetected(Player);

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature reaction complete - New state: %s"),
        *UEnum::GetValueAsString(NewBehaviorState));
}

void AAuracronJungleCreature::UpdateTerritorialBehavior()
{
    // Update territorial behavior using UE 5.6 territorial system

    // Check if creature is too far from home
    float DistanceFromHome = FVector::Dist(GetActorLocation(), HomeLocation);

    if (DistanceFromHome > TerritorialRadius)
    {
        // Return to territory
        if (AAIController* AIController = Cast<AAIController>(GetController()))
        {
            if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
            {
                BlackboardComp->SetValueAsVector(TEXT("HomeLocation"), HomeLocation);
                BlackboardComp->SetValueAsBool(TEXT("ShouldReturnHome"), true);
            }
        }
    }
    else
    {
        // Patrol territory
        if (AAIController* AIController = Cast<AAIController>(GetController()))
        {
            if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
            {
                BlackboardComp->SetValueAsBool(TEXT("ShouldReturnHome"), false);
                BlackboardComp->SetValueAsBool(TEXT("ShouldPatrol"), true);
            }
        }
    }
}

void AAuracronJungleCreature::UpdateSocialBehavior()
{
    // Update social behavior using UE 5.6 social AI system

    // Find nearby creatures of same type
    TArray<AAuracronJungleCreature*> NearbyCreatures = GetNearbyCreatures(TerritorialRadius);

    if (NearbyCreatures.Num() > 1) // Self + others
    {
        // Update pack behavior
        if (AAIController* AIController = Cast<AAIController>(GetController()))
        {
            if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
            {
                BlackboardComp->SetValueAsInt(TEXT("PackSize"), NearbyCreatures.Num());
                BlackboardComp->SetValueAsBool(TEXT("InPack"), true);

                // Set pack leader (first creature in array)
                if (NearbyCreatures.IsValidIndex(0))
                {
                    BlackboardComp->SetValueAsObject(TEXT("PackLeader"), NearbyCreatures[0]);
                }
            }
        }
    }
    else
    {
        // Solo behavior
        if (AAIController* AIController = Cast<AAIController>(GetController()))
        {
            if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
            {
                BlackboardComp->SetValueAsBool(TEXT("InPack"), false);
                BlackboardComp->SetValueAsObject(TEXT("PackLeader"), nullptr);
            }
        }
    }
}

// === Implementation Methods ===

void AAuracronJungleCreature::InitializeCreatureAI()
{
    if (bIsInitialized)
    {
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Initializing creature AI for %s"), *GetName());

    // Setup creature perception
    SetupCreaturePerception();

    // Configure creature audio
    ConfigureCreatureAudio();

    // Setup creature VFX
    SetupCreatureVFX();

    // Start behavior update timer
    if (GetWorld())
    {
        GetWorld()->GetTimerManager().SetTimer(
            BehaviorUpdateTimer,
            [this]()
            {
                UpdateCreatureBehavior(0.5f);
            },
            0.5f, // Update every 0.5 seconds
            true  // Looping
        );
    }

    // Try to register with adaptive AI system
    if (GetWorld())
    {
        for (TActorIterator<AAuracronAdaptiveJungleAI> ActorItr(GetWorld()); ActorItr; ++ActorItr)
        {
            AAuracronAdaptiveJungleAI* AISystem = *ActorItr;
            if (AISystem && IsValid(AISystem))
            {
                RegisterWithAdaptiveAI(AISystem);
                break;
            }
        }
    }

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature AI initialized for %s"), *GetName());
}

void AAuracronJungleCreature::SetupCreaturePerception()
{
    // Setup creature perception using UE 5.6 AI perception system
    if (AAIController* AIController = Cast<AAIController>(GetController()))
    {
        if (UAIPerceptionComponent* PerceptionComp = AIController->FindComponentByClass<UAIPerceptionComponent>())
        {
            // Basic perception setup - detailed configuration should be done in Blueprint or AI Controller
            UE_LOG(LogTemp, Log, TEXT("AURACRON: AI Perception component found for creature %s"), *GetName());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("AURACRON: No AI Perception component found for creature %s"), *GetName());
        }
    }
}

void AAuracronJungleCreature::ReconfigureForType()
{
    // Reconfigure creature based on current type
    switch (CreatureType)
    {
        case EJungleCreatureType::Predator:
            // Configure for predator behavior
            DetectionRange = 1500.0f;
            MovementSpeed = 600.0f;
            AttackDamage = 50.0f;
            Health = 150.0f;
            break;

        case EJungleCreatureType::Herbivore:
            // Configure for herbivore behavior
            DetectionRange = 800.0f;
            MovementSpeed = 400.0f;
            AttackDamage = 20.0f;
            Health = 100.0f;
            break;

        case EJungleCreatureType::Scavenger:
            // Configure for scavenger behavior
            DetectionRange = 1200.0f;
            MovementSpeed = 500.0f;
            AttackDamage = 30.0f;
            Health = 120.0f;
            break;

        case EJungleCreatureType::Guardian:
            // Configure for guardian behavior
            DetectionRange = 2000.0f;
            MovementSpeed = 300.0f;
            AttackDamage = 80.0f;
            Health = 250.0f;
            break;

        default:
            // Default configuration
            DetectionRange = 1000.0f;
            MovementSpeed = 450.0f;
            AttackDamage = 35.0f;
            Health = 125.0f;
            break;
    }

    // Update movement component if available
    if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
    {
        MovementComp->MaxWalkSpeed = MovementSpeed;
    }

    // Reconfigure perception
    SetupCreaturePerception();

    UE_LOG(LogTemp, Log, TEXT("AURACRON: Creature reconfigured for type %s"), *UEnum::GetValueAsString(CreatureType));
}

// === Behavior Methods Implementation ===

TArray<APawn*> AAuracronJungleCreature::GetNearbyPlayers(float Range)
{
    TArray<APawn*> NearbyPlayers;

    if (UWorld* World = GetWorld())
    {
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (APawn* PlayerPawn = PC->GetPawn())
                {
                    float Distance = FVector::Dist(GetActorLocation(), PlayerPawn->GetActorLocation());
                    if (Distance <= Range)
                    {
                        NearbyPlayers.Add(PlayerPawn);
                    }
                }
            }
        }
    }

    return NearbyPlayers;
}

APawn* AAuracronJungleCreature::GetClosestPlayer(const TArray<APawn*>& Players)
{
    APawn* ClosestPlayer = nullptr;
    float ClosestDistance = FLT_MAX;

    FVector MyLocation = GetActorLocation();

    for (APawn* Player : Players)
    {
        if (Player)
        {
            float Distance = FVector::Dist(MyLocation, Player->GetActorLocation());
            if (Distance < ClosestDistance)
            {
                ClosestDistance = Distance;
                ClosestPlayer = Player;
            }
        }
    }

    return ClosestPlayer;
}

TArray<AAuracronJungleCreature*> AAuracronJungleCreature::GetNearbyCreatures(float Range)
{
    TArray<AAuracronJungleCreature*> NearbyCreatures;

    if (UWorld* World = GetWorld())
    {
        for (TActorIterator<AAuracronJungleCreature> ActorItr(World); ActorItr; ++ActorItr)
        {
            AAuracronJungleCreature* OtherCreature = *ActorItr;
            if (OtherCreature && OtherCreature != this)
            {
                float Distance = FVector::Dist(GetActorLocation(), OtherCreature->GetActorLocation());
                if (Distance <= Range)
                {
                    NearbyCreatures.Add(OtherCreature);
                }
            }
        }
    }

    return NearbyCreatures;
}

void AAuracronJungleCreature::UpdatePatrolBehavior(float DeltaTime)
{
    // Simple patrol behavior - move in a pattern
    if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
    {
        // Basic patrol logic - can be enhanced later
        FVector CurrentLocation = GetActorLocation();
        FVector PatrolDirection = FVector(FMath::Cos(GetWorld()->GetTimeSeconds()), FMath::Sin(GetWorld()->GetTimeSeconds()), 0.0f);
        FVector TargetLocation = CurrentLocation + PatrolDirection * 100.0f;

        FVector Direction = (TargetLocation - CurrentLocation).GetSafeNormal();
        AddMovementInput(Direction, 0.5f);
    }
}

void AAuracronJungleCreature::UpdateHuntingBehavior(float DeltaTime)
{
    // Hunt the closest player
    TArray<APawn*> NearbyPlayers = GetNearbyPlayers(DetectionRange);
    if (NearbyPlayers.Num() > 0)
    {
        APawn* TargetPlayer = GetClosestPlayer(NearbyPlayers);
        if (TargetPlayer)
        {
            FVector Direction = (TargetPlayer->GetActorLocation() - GetActorLocation()).GetSafeNormal();
            AddMovementInput(Direction, 1.0f);
        }
    }
}

void AAuracronJungleCreature::UpdateFleeingBehavior(float DeltaTime)
{
    // Flee from the closest player
    TArray<APawn*> NearbyPlayers = GetNearbyPlayers(DetectionRange);
    if (NearbyPlayers.Num() > 0)
    {
        APawn* ThreatPlayer = GetClosestPlayer(NearbyPlayers);
        if (ThreatPlayer)
        {
            FVector Direction = (GetActorLocation() - ThreatPlayer->GetActorLocation()).GetSafeNormal();
            AddMovementInput(Direction, 1.0f);
        }
    }
}

void AAuracronJungleCreature::UpdateInvestigatingBehavior(float DeltaTime)
{
    // Move towards point of interest
    if (UCharacterMovementComponent* MovementComp = GetCharacterMovement())
    {
        // Basic investigation logic
        FVector InvestigationPoint = GetActorLocation() + FVector(100.0f, 0.0f, 0.0f);
        FVector Direction = (InvestigationPoint - GetActorLocation()).GetSafeNormal();
        AddMovementInput(Direction, 0.3f);
    }
}

ECreatureBehaviorState AAuracronJungleCreature::DetermineReactionBehavior(APawn* Player)
{
    if (!Player)
    {
        return ECreatureBehaviorState::Patrolling;
    }

    float DistanceToPlayer = FVector::Dist(GetActorLocation(), Player->GetActorLocation());

    // Determine behavior based on creature type and distance
    switch (CreatureType)
    {
        case EJungleCreatureType::Predator:
            return (DistanceToPlayer < DetectionRange * 0.5f) ? ECreatureBehaviorState::Hunting : ECreatureBehaviorState::Patrolling;

        case EJungleCreatureType::Herbivore:
            return (DistanceToPlayer < DetectionRange * 0.3f) ? ECreatureBehaviorState::Fleeing : ECreatureBehaviorState::Patrolling;

        case EJungleCreatureType::Scavenger:
            return (DistanceToPlayer < DetectionRange * 0.4f) ? ECreatureBehaviorState::Investigating : ECreatureBehaviorState::Patrolling;

        case EJungleCreatureType::Guardian:
            return (DistanceToPlayer < DetectionRange * 0.6f) ? ECreatureBehaviorState::Hunting : ECreatureBehaviorState::Patrolling;

        default:
            return ECreatureBehaviorState::Patrolling;
    }
}
