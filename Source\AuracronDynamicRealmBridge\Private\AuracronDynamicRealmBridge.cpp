#include "AuracronDynamicRealmBridge.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronRealmManager.h"
#include "AuracronLayerComponent.h"
#include "AuracronPrismalFlow.h"
#include "AuracronDynamicRail.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "HAL/IConsoleManager.h"
#include "Misc/CoreDelegates.h"

DEFINE_LOG_CATEGORY(LogAuracronDynamicRealm);

#define LOCTEXT_NAMESPACE "FAuracronDynamicRealmBridgeModule"

void FAuracronDynamicRealmBridgeModule::StartupModule()
{
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Bridge Module Starting Up - Version %s"), TEXT(AURACRON_DYNAMIC_REALM_VERSION_STRING));
    
    bIsInitialized = false;
    
    // Register dynamic realm systems
    RegisterDynamicRealmSystems();
    
    // Initialize realm managers
    InitializeRealmManagers();
    
    // Register console commands
    RegisterConsoleCommands();
    
    // Register for engine events
    FCoreDelegates::OnPostEngineInit.AddLambda([this]()
    {
        AURACRON_REALM_LOG(Log, TEXT("Engine initialized, finalizing Dynamic Realm Bridge setup"));
        bIsInitialized = true;
    });
    
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Bridge Module Successfully Started"));
}

void FAuracronDynamicRealmBridgeModule::ShutdownModule()
{
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Bridge Module Shutting Down"));
    
    // Unregister console commands
    UnregisterConsoleCommands();
    
    // Shutdown realm managers
    ShutdownRealmManagers();
    
    // Unregister dynamic realm systems
    UnregisterDynamicRealmSystems();
    
    bIsInitialized = false;
    
    AURACRON_REALM_LOG(Warning, TEXT("Auracron Dynamic Realm Bridge Module Successfully Shut Down"));
}

void FAuracronDynamicRealmBridgeModule::RegisterDynamicRealmSystems()
{
    AURACRON_REALM_LOG(Log, TEXT("Registering Dynamic Realm Systems"));
    
    // Register subsystem classes
    // Note: Subsystems are automatically registered by UE5's subsystem framework
    
    // Register component classes
    // Note: Components are automatically registered by UE5's reflection system
    
    // Register actor classes
    // Note: Actors are automatically registered by UE5's reflection system
    
    AURACRON_REALM_LOG(Log, TEXT("Dynamic Realm Systems Registered Successfully"));
}

void FAuracronDynamicRealmBridgeModule::UnregisterDynamicRealmSystems()
{
    AURACRON_REALM_LOG(Log, TEXT("Unregistering Dynamic Realm Systems"));
    
    // Cleanup is handled automatically by UE5's reflection system
    
    AURACRON_REALM_LOG(Log, TEXT("Dynamic Realm Systems Unregistered Successfully"));
}

void FAuracronDynamicRealmBridgeModule::InitializeRealmManagers()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Realm Managers"));
    
    // Realm managers are initialized per-world by the subsystem
    // This function is for module-level initialization
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Managers Initialization Setup Complete"));
}

void FAuracronDynamicRealmBridgeModule::ShutdownRealmManagers()
{
    AURACRON_REALM_LOG(Log, TEXT("Shutting Down Realm Managers"));
    
    // Cleanup is handled by individual world subsystems
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Managers Shutdown Complete"));
}

void FAuracronDynamicRealmBridgeModule::RegisterConsoleCommands()
{
    AURACRON_REALM_LOG(Log, TEXT("Registering Console Commands"));
    
    // Register debug console commands
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("Auracron.Realm.ShowInfo"),
        TEXT("Show information about all realm layers"),
        FConsoleCommandDelegate::CreateRaw(this, &FAuracronDynamicRealmBridgeModule::DebugShowRealmInfo),
        ECVF_Cheat
    ));
    
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("Auracron.Realm.ToggleLayer"),
        TEXT("Toggle visibility of a specific layer (0=Terrestrial, 1=Celestial, 2=Abyssal)"),
        FConsoleCommandWithArgsDelegate::CreateLambda([this](const TArray<FString>& Args)
        {
            if (Args.Num() > 0)
            {
                int32 LayerIndex = FCString::Atoi(*Args[0]);
                DebugToggleLayerVisibility(LayerIndex);
            }
        }),
        ECVF_Cheat
    ));
    
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("Auracron.Realm.TriggerTransition"),
        TEXT("Trigger transition between layers (SourceLayer TargetLayer)"),
        FConsoleCommandWithArgsDelegate::CreateLambda([this](const TArray<FString>& Args)
        {
            if (Args.Num() >= 2)
            {
                int32 SourceLayer = FCString::Atoi(*Args[0]);
                int32 TargetLayer = FCString::Atoi(*Args[1]);
                DebugTriggerRealmTransition(SourceLayer, TargetLayer);
            }
        }),
        ECVF_Cheat
    ));
    
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("Auracron.Realm.SpawnContent"),
        TEXT("Spawn content for a specific layer (0=Terrestrial, 1=Celestial, 2=Abyssal)"),
        FConsoleCommandWithArgsDelegate::CreateLambda([this](const TArray<FString>& Args)
        {
            if (Args.Num() > 0)
            {
                int32 LayerIndex = FCString::Atoi(*Args[0]);
                DebugSpawnRealmContent(LayerIndex);
            }
        }),
        ECVF_Cheat
    ));
    
    ConsoleCommands.Add(IConsoleManager::Get().RegisterConsoleCommand(
        TEXT("Auracron.Realm.AnalyzePerformance"),
        TEXT("Analyze performance metrics for all realm layers"),
        FConsoleCommandDelegate::CreateRaw(this, &FAuracronDynamicRealmBridgeModule::DebugAnalyzeRealmPerformance),
        ECVF_Cheat
    ));
    
    AURACRON_REALM_LOG(Log, TEXT("Console Commands Registered: %d commands"), ConsoleCommands.Num());
}

void FAuracronDynamicRealmBridgeModule::UnregisterConsoleCommands()
{
    AURACRON_REALM_LOG(Log, TEXT("Unregistering Console Commands"));
    
    for (IConsoleCommand* Command : ConsoleCommands)
    {
        if (Command)
        {
            IConsoleManager::Get().UnregisterConsoleObject(Command);
        }
    }
    ConsoleCommands.Empty();
    
    AURACRON_REALM_LOG(Log, TEXT("Console Commands Unregistered"));
}

void FAuracronDynamicRealmBridgeModule::DebugShowRealmInfo()
{
    AURACRON_REALM_LOG(Warning, TEXT("=== AURACRON DYNAMIC REALM DEBUG INFO ==="));
    
    if (GEngine && GEngine->GetCurrentPlayWorld())
    {
        UWorld* World = GEngine->GetCurrentPlayWorld();
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            AURACRON_REALM_LOG(Warning, TEXT("Realm Subsystem Found: %s"), *RealmSubsystem->GetName());
            
            // Show layer information
            for (int32 i = 0; i < 3; i++)
            {
                EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(i + 1);
                bool bIsActive = RealmSubsystem->IsLayerActive(Layer);
                FAuracronRealmLayerData LayerData = RealmSubsystem->GetLayerData(Layer);
                
                FString LayerName;
                switch (Layer)
                {
                    case EAuracronRealmLayer::Terrestrial: LayerName = TEXT("Planície Radiante"); break;
                    case EAuracronRealmLayer::Celestial: LayerName = TEXT("Firmamento Zephyr"); break;
                    case EAuracronRealmLayer::Abyssal: LayerName = TEXT("Abismo Umbrio"); break;
                    default: LayerName = TEXT("Unknown"); break;
                }
                
                AURACRON_REALM_LOG(Warning, TEXT("Layer %s: Active=%s, Height=%.2f, Actors=%d"), 
                    *LayerName, bIsActive ? TEXT("Yes") : TEXT("No"), LayerData.LayerHeight, LayerData.ActiveActorCount);
            }
        }
        else
        {
            AURACRON_REALM_LOG(Error, TEXT("Realm Subsystem Not Found!"));
        }
    }
    else
    {
        AURACRON_REALM_LOG(Error, TEXT("No Valid World Found!"));
    }
    
    AURACRON_REALM_LOG(Warning, TEXT("=== END REALM DEBUG INFO ==="));
}

void FAuracronDynamicRealmBridgeModule::DebugToggleLayerVisibility(int32 LayerIndex)
{
    AURACRON_REALM_LOG(Warning, TEXT("Toggling Layer Visibility: %d"), LayerIndex);
    
    if (GEngine && GEngine->GetCurrentPlayWorld())
    {
        UWorld* World = GEngine->GetCurrentPlayWorld();
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerIndex + 1);
            bool bCurrentlyActive = RealmSubsystem->IsLayerActive(Layer);
            
            if (bCurrentlyActive)
            {
                RealmSubsystem->DeactivateLayer(Layer);
                AURACRON_REALM_LOG(Warning, TEXT("Layer %d Deactivated"), LayerIndex);
            }
            else
            {
                RealmSubsystem->ActivateLayer(Layer);
                AURACRON_REALM_LOG(Warning, TEXT("Layer %d Activated"), LayerIndex);
            }
        }
    }
}

void FAuracronDynamicRealmBridgeModule::DebugTriggerRealmTransition(int32 SourceLayer, int32 TargetLayer)
{
    AURACRON_REALM_LOG(Warning, TEXT("Triggering Realm Transition: %d -> %d"), SourceLayer, TargetLayer);
    
    if (GEngine && GEngine->GetCurrentPlayWorld())
    {
        UWorld* World = GEngine->GetCurrentPlayWorld();
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            // Find a player pawn to test transition
            for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
            {
                if (APlayerController* PC = Iterator->Get())
                {
                    if (APawn* PlayerPawn = PC->GetPawn())
                    {
                        EAuracronRealmLayer Source = static_cast<EAuracronRealmLayer>(SourceLayer + 1);
                        EAuracronRealmLayer Target = static_cast<EAuracronRealmLayer>(TargetLayer + 1);
                        
                        bool bSuccess = RealmSubsystem->RequestLayerTransition(PlayerPawn, Target, ERealmTransitionType::Cinematic);
                        AURACRON_REALM_LOG(Warning, TEXT("Transition Request Result: %s"), bSuccess ? TEXT("Success") : TEXT("Failed"));
                        break;
                    }
                }
            }
        }
    }
}

void FAuracronDynamicRealmBridgeModule::DebugSpawnRealmContent(int32 LayerIndex)
{
    AURACRON_REALM_LOG(Warning, TEXT("Spawning Realm Content for Layer: %d"), LayerIndex);
    
    if (GEngine && GEngine->GetCurrentPlayWorld())
    {
        UWorld* World = GEngine->GetCurrentPlayWorld();
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(LayerIndex + 1);
            
            // Find realm manager for this layer
            for (TActorIterator<AAuracronRealmManager> ActorItr(World); ActorItr; ++ActorItr)
            {
                AAuracronRealmManager* RealmManager = *ActorItr;
                if (RealmManager && RealmManager->ManagedLayer == Layer)
                {
                    RealmManager->GenerateLayerContent();
                    AURACRON_REALM_LOG(Warning, TEXT("Content Generated for Layer %d"), LayerIndex);
                    return;
                }
            }
            
            AURACRON_REALM_LOG(Error, TEXT("No Realm Manager Found for Layer %d"), LayerIndex);
        }
    }
}

void FAuracronDynamicRealmBridgeModule::DebugAnalyzeRealmPerformance()
{
    AURACRON_REALM_LOG(Warning, TEXT("=== AURACRON REALM PERFORMANCE ANALYSIS ==="));
    
    if (GEngine && GEngine->GetCurrentPlayWorld())
    {
        UWorld* World = GEngine->GetCurrentPlayWorld();
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            for (int32 i = 0; i < 3; i++)
            {
                EAuracronRealmLayer Layer = static_cast<EAuracronRealmLayer>(i + 1);
                float PerformanceMetric = RealmSubsystem->GetLayerPerformanceMetric(Layer);
                
                FString LayerName;
                switch (Layer)
                {
                    case EAuracronRealmLayer::Terrestrial: LayerName = TEXT("Planície Radiante"); break;
                    case EAuracronRealmLayer::Celestial: LayerName = TEXT("Firmamento Zephyr"); break;
                    case EAuracronRealmLayer::Abyssal: LayerName = TEXT("Abismo Umbrio"); break;
                    default: LayerName = TEXT("Unknown"); break;
                }
                
                AURACRON_REALM_LOG(Warning, TEXT("Layer %s Performance: %.2f ms"), *LayerName, PerformanceMetric);
            }
            
            // Show memory usage
            FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
            AURACRON_REALM_LOG(Warning, TEXT("Total Memory Usage: %.2f MB"), MemStats.UsedPhysical / (1024.0f * 1024.0f));
            
            // Show active objects
            int32 TotalActors = 0;
            for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
            {
                TotalActors++;
            }
            AURACRON_REALM_LOG(Warning, TEXT("Total Active Actors: %d"), TotalActors);
        }
    }
    
    AURACRON_REALM_LOG(Warning, TEXT("=== END PERFORMANCE ANALYSIS ==="));
}

// Utility function implementations
namespace AuracronDynamicRealmUtils
{
    UAuracronDynamicRealmSubsystem* GetDynamicRealmSubsystem(const UObject* WorldContext)
    {
        if (!WorldContext)
        {
            AURACRON_REALM_LOG(Error, TEXT("GetDynamicRealmSubsystem: Invalid WorldContext"));
            return nullptr;
        }
        
        UWorld* World = WorldContext->GetWorld();
        if (!World)
        {
            AURACRON_REALM_LOG(Error, TEXT("GetDynamicRealmSubsystem: Invalid World"));
            return nullptr;
        }
        
        return World->GetSubsystem<UAuracronDynamicRealmSubsystem>();
    }
    
    float GetLayerHeight(EAuracronRealmLayer Layer)
    {
        switch (Layer)
        {
            case EAuracronRealmLayer::Terrestrial:
                return AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT;
            case EAuracronRealmLayer::Celestial:
                return AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT;
            case EAuracronRealmLayer::Abyssal:
                return AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT;
            default:
                return 0.0f;
        }
    }
    
    EAuracronRealmLayer GetLayerFromPosition(const FVector& WorldPosition)
    {
        float Z = WorldPosition.Z;
        
        // Check for transition zones first
        if (IsInTransitionZone(WorldPosition))
        {
            return EAuracronRealmLayer::Transition;
        }
        
        // Determine layer based on height
        if (Z >= AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT - 500.0f)
        {
            return EAuracronRealmLayer::Celestial;
        }
        else if (Z <= AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT + 500.0f)
        {
            return EAuracronRealmLayer::Abyssal;
        }
        else
        {
            return EAuracronRealmLayer::Terrestrial;
        }
    }
    
    bool IsInTransitionZone(const FVector& WorldPosition)
    {
        float Z = WorldPosition.Z;
        
        // Transition zones are 1000 units above/below layer boundaries
        bool bNearCelestialTransition = FMath::Abs(Z - AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT) < 1000.0f;
        bool bNearAbyssalTransition = FMath::Abs(Z - AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT) < 1000.0f;
        bool bNearTerrestrialTransition = FMath::Abs(Z - AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT) < 1000.0f;
        
        return bNearCelestialTransition || bNearAbyssalTransition || bNearTerrestrialTransition;
    }
    
    float CalculateTransitionDuration(ERealmTransitionType TransitionType, float Distance)
    {
        float BaseDuration = AuracronDynamicRealmConstants::DEFAULT_TRANSITION_DURATION;
        
        switch (TransitionType)
        {
            case ERealmTransitionType::Instant:
                return AuracronDynamicRealmConstants::INSTANT_TRANSITION_DURATION;
            case ERealmTransitionType::Cinematic:
                return AuracronDynamicRealmConstants::CINEMATIC_TRANSITION_DURATION;
            case ERealmTransitionType::Gradual:
            case ERealmTransitionType::Combat:
            case ERealmTransitionType::Stealth:
            default:
                // Scale duration based on distance
                float DistanceMultiplier = FMath::Clamp(Distance / 5000.0f, 0.5f, 3.0f);
                return BaseDuration * DistanceMultiplier;
        }
    }
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FAuracronDynamicRealmBridgeModule, AuracronDynamicRealmBridge)
