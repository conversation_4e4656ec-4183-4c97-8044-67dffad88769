#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronDynamicRail.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class UAuracronLayerComponent;

/**
 * Rail movement data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FRailMovementData
{
    GENERATED_BODY()

    /** Movement speed on this rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    float MovementSpeed;

    /** Acceleration on rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    float Acceleration;

    /** Deceleration when leaving rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    float Deceleration;

    /** Can change direction on rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    bool bCanReverseDirection;

    /** Can exit rail at any point */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    bool bCanExitAnywhere;

    /** Energy cost per second on rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Movement")
    float EnergyCostPerSecond;

    FRailMovementData()
    {
        MovementSpeed = 1000.0f;
        Acceleration = 500.0f;
        Deceleration = 800.0f;
        bCanReverseDirection = true;
        bCanExitAnywhere = true;
        EnergyCostPerSecond = 5.0f;
    }
};

/**
 * Rail visual configuration
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FRailVisualConfig
{
    GENERATED_BODY()

    /** Rail mesh */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    TObjectPtr<UStaticMesh> RailMesh;

    /** Rail material */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    TObjectPtr<UMaterialInterface> RailMaterial;

    /** Particle effect along rail */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    TObjectPtr<UNiagaraSystem> RailEffect;

    /** Movement trail effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    TObjectPtr<UNiagaraSystem> MovementTrailEffect;

    /** Rail activation effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    TObjectPtr<UNiagaraSystem> ActivationEffect;

    /** Rail color */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    FLinearColor RailColor;

    /** Effect intensity */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    float EffectIntensity;

    /** Visibility during day/night */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    bool bVisibleDuringDay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Visuals")
    bool bVisibleDuringNight;

    FRailVisualConfig()
    {
        RailColor = FLinearColor::White;
        EffectIntensity = 1.0f;
        bVisibleDuringDay = true;
        bVisibleDuringNight = true;
    }
};

/**
 * Auracron Dynamic Rail
 * 
 * Dynamic movement rails for rapid traversal:
 * 
 * Solar Trilhos (Golden):
 * - Golden particles with heat distortion
 * - High-speed movement during day
 * - Reduced effectiveness at night
 * 
 * Axis Trilhos (Silver):
 * - Instant vertical movement between layers
 * - Neutral appearance
 * - Always available
 * 
 * Lunar Trilhos (Ethereal Blue):
 * - Ethereal blue-white appearance
 * - Visible only at night
 * - Provides stealth bonus
 * - Enhanced movement speed in darkness
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronDynamicRail : public AActor
{
    GENERATED_BODY()

public:
    AAuracronDynamicRail();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Rail configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Configuration")
    EAuracronRailType RailType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Configuration")
    FRailMovementData MovementData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Configuration")
    FRailVisualConfig VisualConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Configuration")
    bool bIsActive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail Configuration")
    bool bAutoActivate;

    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USplineComponent> RailSpline;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<USplineMeshComponent>> RailMeshComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UNiagaraComponent>> RailEffectComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> RailAudioComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<USphereComponent>> EntryPoints;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<USphereComponent>> ExitPoints;

    // Rail management
    UFUNCTION(BlueprintCallable, Category = "Rail Management")
    void ActivateRail();

    UFUNCTION(BlueprintCallable, Category = "Rail Management")
    void DeactivateRail();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Rail Management")
    bool IsRailActive() const { return bIsActive; }

    UFUNCTION(BlueprintCallable, Category = "Rail Management")
    void SetRailPath(const TArray<FVector>& PathPoints);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Rail Management")
    TArray<FVector> GetRailPath() const;

    // Player interaction
    UFUNCTION(BlueprintCallable, Category = "Player Interaction")
    bool CanPlayerUseRail(APawn* Player) const;

    UFUNCTION(BlueprintCallable, Category = "Player Interaction")
    bool StartPlayerMovement(APawn* Player, bool bForwardDirection = true);

    UFUNCTION(BlueprintCallable, Category = "Player Interaction")
    void StopPlayerMovement(APawn* Player);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Interaction")
    bool IsPlayerOnRail(APawn* Player) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Interaction")
    TArray<APawn*> GetPlayersOnRail() const { return PlayersOnRail; }

    // Movement control
    UFUNCTION(BlueprintCallable, Category = "Movement Control")
    void UpdatePlayerMovement(APawn* Player, float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Movement Control")
    void SetPlayerRailSpeed(APawn* Player, float Speed);

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement Control")
    float GetPlayerRailProgress(APawn* Player) const;

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Movement Control")
    FVector GetPlayerRailPosition(APawn* Player) const;

    // Rail type specific behavior
    UFUNCTION(BlueprintCallable, Category = "Rail Behavior")
    void UpdateSolarRailBehavior(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Rail Behavior")
    void UpdateAxisRailBehavior(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Rail Behavior")
    void UpdateLunarRailBehavior(float DeltaTime);

    // Visual and audio
    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void UpdateRailVisuals(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void UpdateRailVisibility();

    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void PlayRailActivationEffects();

    UFUNCTION(BlueprintCallable, Category = "Presentation")
    void PlayRailDeactivationEffects();

    // Events
    UFUNCTION(BlueprintImplementableEvent, Category = "Rail Events")
    void OnRailActivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Rail Events")
    void OnRailDeactivated();

    UFUNCTION(BlueprintImplementableEvent, Category = "Rail Events")
    void OnPlayerEnteredRail(APawn* Player);

    UFUNCTION(BlueprintImplementableEvent, Category = "Rail Events")
    void OnPlayerExitedRail(APawn* Player);

    UFUNCTION(BlueprintImplementableEvent, Category = "Rail Events")
    void OnPlayerReachedDestination(APawn* Player);

    // Collision events
    UFUNCTION()
    void OnEntryPointBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    UFUNCTION()
    void OnExitPointBeginOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

protected:
    // Player tracking
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TArray<TObjectPtr<APawn>> PlayersOnRail;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TMap<TObjectPtr<APawn>, float> PlayerRailProgress;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TMap<TObjectPtr<APawn>, bool> PlayerRailDirection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Tracking")
    TMap<TObjectPtr<APawn>, float> PlayerRailSpeed;

    // Rail state
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail State")
    float RailLength;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail State")
    bool bIsVisible;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail State")
    float CurrentEffectIntensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rail State")
    float LastActivationTime;

    // Time-based behavior
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time Behavior")
    bool bAffectedByTimeOfDay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time Behavior")
    float DayEffectiveness;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Time Behavior")
    float NightEffectiveness;

    // Audio assets
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> RailActivationSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> RailActiveLoopSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> RailDeactivationSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio Assets")
    TObjectPtr<USoundBase> PlayerMovementSound;

private:
    // Rail type initialization
    void InitializeSolarRail();
    void InitializeAxisRail();
    void InitializeLunarRail();
    
    // Rail construction
    void BuildRailMesh();
    void SetupRailEffects();
    void ConfigureRailAudio();
    void CreateEntryExitPoints();
    
    // Player movement logic
    void ProcessPlayerRailMovement(APawn* Player, float DeltaTime);
    FVector CalculateRailPosition(float Progress) const;
    FRotator CalculateRailRotation(float Progress) const;
    void ApplyRailMovementToPlayer(APawn* Player, const FVector& NewLocation, const FRotator& NewRotation);
    
    // Rail type specific updates
    void UpdateSolarRailEffects(float DeltaTime);
    void UpdateAxisRailEffects(float DeltaTime);
    void UpdateLunarRailEffects(float DeltaTime);
    
    // Time of day effects
    void UpdateTimeOfDayEffects();
    float GetCurrentTimeOfDayMultiplier() const;
    bool IsNightTime() const;
    
    // Visual effects management
    void UpdateRailParticles(float DeltaTime);
    void UpdateRailMaterials();
    void UpdateRailLighting();
    void ApplyHeatDistortion(); // For Solar Rails
    void ApplyEtherealEffect(); // For Lunar Rails
    
    // Performance optimization
    void OptimizeRailRendering(const FVector& ViewerLocation);
    void UpdateRailLOD(float DistanceToViewer);
    void CullDistantEffects(float DistanceToViewer);
    
    // Collision and interaction
    void SetupRailCollision();
    void UpdateRailCollision();
    bool IsValidRailUser(APawn* Player) const;
    
    // Utility functions
    bool ValidateRailConfiguration() const;
    void LogRailStatus() const;
    float GetSplineLength() const;
    FVector GetSplineLocationAtDistance(float Distance) const;
    FVector GetSplineDirectionAtDistance(float Distance) const;

    // Rail type-specific behavior methods
    void UpdateSolarRailBehavior(float DeltaTime);
    void UpdateAxisRailBehavior(float DeltaTime);
    void UpdateLunarRailBehavior(float DeltaTime);

    // Player effect methods
    void ApplyHeatDistortionToPlayer(APawn* Player);
    void ApplyInstantMovementToPlayer(APawn* Player);
    void ApplyStealthEffectToPlayer(APawn* Player);
    void ApplyLunarStealthBonus(APawn* Player, float NightIntensity);
    void ApplyRailEntryEffects(APawn* Player);
    void RemoveRailEffectsFromPlayer(APawn* Player);

    // Rail initialization methods
    void InitializeSolarRail();
    void InitializeAxisRail();
    void InitializeLunarRail();
    void BuildRailMesh();
    void SetupRailEffects();
    void ConfigureRailAudio();
    void CreateEntryExitPoints();
    void CreateRailAccessPoint(const FVector& Location, bool bIsEntryPoint);

    // Effect methods
    void PlayRailActivationEffects();
    void PlayRailDeactivationEffects();
    void UpdateRailVisuals(float DeltaTime);
    void UpdateRailVisibility();
    void ApplyHeatWaveEffects();
    void ApplyNeutralAxisEffects();
    void ApplyEtherealMovementEnhancement(float NightIntensity);
    void ApplyAxisVerticalEffects(APawn* Player);
    void ApplyMovementEffects(APawn* Player, FPlayerRailData* RailData, float DeltaTime);

    // Utility calculation methods
    float GetCurrentGameHour() const;
    float CalculateNightIntensity() const;
    float GetCurrentLunarPhase() const;
    float GetRailTypeSpeedModifier() const;
    bool HasSufficientEnergy(APawn* Player, float RequiredEnergy) const;
    void ConsumePlayerEnergy(APawn* Player, float EnergyAmount);
    void HandleRailCompletion(APawn* Player, FPlayerRailData* RailData);
    void ShowRailInteractionPrompt(APawn* Player);
    void HideRailInteractionPrompt(APawn* Player);

    // Event methods
    void OnPlayerEnteredRail(APawn* Player);
    void OnPlayerExitedRail(APawn* Player);
    void OnRailActivated();
    void OnRailDeactivated();

    // Cached references
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    // Dynamic materials
    UPROPERTY()
    TArray<TObjectPtr<UMaterialInstanceDynamic>> DynamicRailMaterials;

    // Player tracking
    UPROPERTY()
    TMap<TObjectPtr<APawn>, FPlayerRailData> PlayerRailDataMap;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerRailEffects;

    UPROPERTY()
    TMap<TObjectPtr<APawn>, FActiveGameplayEffectHandle> PlayerStealthEffects;

    UPROPERTY()
    TArray<TObjectPtr<APawn>> PotentialUsers;

    // Access points - using existing USphereComponent definitions above

    // Timers
    FTimerHandle EffectUpdateTimer;
    FTimerHandle VisibilityUpdateTimer;
    FTimerHandle PlayerMovementTimer;
    
    // State tracking
    bool bIsInitialized;
    float LastUpdateTime;
    float LastVisibilityCheck;
    int32 ActivePlayerCount;
};
