#include "AuracronVerticalConnector.h"

#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "Components/AudioComponent.h"
#include "Sound/SoundBase.h"
#include "GameFramework/Character.h"

AAuracronVerticalConnectorActor::AAuracronVerticalConnectorActor()
{
    PrimaryActorTick.bCanEverTick = true;

    Root = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));
    SetRootComponent(Root);

    VisualMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("VisualMesh"));
    VisualMesh->SetupAttachment(Root);

    ActivationSphere = CreateDefaultSubobject<USphereComponent>(TEXT("ActivationSphere"));
    ActivationSphere->SetupAttachment(Root);
    ActivationSphere->SetSphereRadius(300.f);
    ActivationSphere->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    ActivationSphere->SetCollisionResponseToAllChannels(ECR_Ignore);
    ActivationSphere->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);

    NiagaraFX = CreateDefaultSubobject<UNiagaraComponent>(TEXT("NiagaraFX"));
    NiagaraFX->SetupAttachment(Root);
    NiagaraFX->bAutoActivate = false;

    AudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("Audio"));
    AudioComponent->SetupAttachment(Root);
    AudioComponent->bAutoActivate = false;
}

void AAuracronVerticalConnectorActor::OnConstruction(const FTransform& Transform)
{
    Super::OnConstruction(Transform);
    UpdateComponentsFromConfig();
}

void AAuracronVerticalConnectorActor::BeginPlay()
{
    Super::BeginPlay();
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::Tick(float DeltaSeconds)
{
    Super::Tick(DeltaSeconds);
}

void AAuracronVerticalConnectorActor::InitializeFromConfig(const FVerticalConnectorConfig& InConfig)
{
    ConnectorConfig = InConfig;
    SetActorLocation(ConnectorConfig.ConnectorLocation);
    ActivationSphere->SetSphereRadius(FMath::Max(50.f, ConnectorConfig.ActivationRadius));
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::SetConnectorState(EVerticalConnectorState NewState)
{
    if (ConnectorState == NewState)
    {
        return;
    }
    ConnectorState = NewState;
    UpdateFXForState();
}

void AAuracronVerticalConnectorActor::ApplyTransitionEffects(ACharacter* Character, bool bStarting)
{
    if (!IsValid(Character))
    {
        return;
    }

    // Basic production-safe hooks: activate/deactivate FX and audio
    if (bStarting)
    {
        if (NiagaraFX && ActivationFX)
        {
            NiagaraFX->SetAsset(ActivationFX);
            NiagaraFX->Activate(true);
        }
        if (AudioComponent && ActivationSound)
        {
            AudioComponent->SetSound(ActivationSound);
            AudioComponent->Play();
        }
    }
    else
    {
        if (NiagaraFX)
        {
            NiagaraFX->Deactivate();
        }
        if (AudioComponent)
        {
            AudioComponent->Stop();
        }
    }
}

void AAuracronVerticalConnectorActor::UpdateComponentsFromConfig()
{
    ActivationSphere->SetSphereRadius(FMath::Max(50.f, ConnectorConfig.ActivationRadius));
}

void AAuracronVerticalConnectorActor::UpdateFXForState()
{
    switch (ConnectorState)
    {
        case EVerticalConnectorState::Active:
            if (NiagaraFX)
            {
                NiagaraFX->Activate(true);
            }
            break;
        case EVerticalConnectorState::InUse:
            if (NiagaraFX)
            {
                NiagaraFX->Activate(true);
            }
            break;
        case EVerticalConnectorState::Inactive:
        case EVerticalConnectorState::Cooldown:
        default:
            if (NiagaraFX)
            {
                NiagaraFX->Deactivate();
            }
            break;
    }
}

void AAuracronVerticalConnector::UpdateConnectorState(float DeltaTime)
{
    // Handle cooldown countdown
    if (ConnectorState == EVerticalConnectorState::Cooldown)
    {
        CooldownRemaining = FMath::Max(0.f, CooldownRemaining - DeltaTime);
        if (CooldownRemaining <= KINDA_SMALL_NUMBER)
        {
            SetConnectorState(EVerticalConnectorState::Active);
        }
    }
}

void AAuracronVerticalConnector::HandleAutoActivation(float DeltaTime)
{
    const FVerticalConnectorConfig& Cfg = ConnectorConfig;
    if (!Cfg.bAutoActivation)
    {
        return;
    }

    AutoActivationTimer += DeltaTime;

    if (!bAutoActive)
    {
        // Wait for next auto activation window
        if (AutoActivationTimer >= FMath::Max(0.1f, Cfg.AutoActivationInterval))
        {
            bAutoActive = true;
            AutoActivationTimer = 0.f;
            AutoActiveRemaining = FMath::Max(0.1f, Cfg.AutoActivationDuration);
            SetConnectorState(EVerticalConnectorState::Active);
        }
    }
    else
    {
        // Active window running
        AutoActiveRemaining -= DeltaTime;
        if (AutoActiveRemaining <= 0.f)
        {
            bAutoActive = false;
            SetConnectorState(EVerticalConnectorState::Inactive);
        }
    }
}

