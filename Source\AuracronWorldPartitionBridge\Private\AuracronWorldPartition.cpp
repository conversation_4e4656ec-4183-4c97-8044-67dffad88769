// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Bridge Implementation
// Bridge 3.1: World Partition - Core Setup

#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionBridge.h"

// World Partition includes
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/WorldSettings.h"
#include "HAL/PlatformMemory.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "DrawDebugHelpers.h"

// =============================================================================
// STREAMING STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronStreamingStatistics::UpdateCalculatedFields()
{
    if (TotalCells > 0)
    {
        StreamingEfficiency = static_cast<float>(LoadedCells) / static_cast<float>(TotalCells);
    }
    else
    {
        StreamingEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION LOGGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionLogger* UAuracronWorldPartitionLogger::Instance = nullptr;

UAuracronWorldPartitionLogger* UAuracronWorldPartitionLogger::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionLogger>();
        Instance->AddToRoot(); // Prevent garbage collection
        
        // Initialize with default enabled categories
        Instance->EnabledCategories.Add(TEXT("WorldPartition"));
        Instance->EnabledCategories.Add(TEXT("Streaming"));
        Instance->EnabledCategories.Add(TEXT("DataLayers"));
    }
    return Instance;
}

void UAuracronWorldPartitionLogger::LogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category)
{
    if (!ShouldLog(Level) || !IsCategoryEnabled(Category))
    {
        return;
    }

    FScopeLock Lock(&LogLock);
    
    FString FormattedMessage = FormatLogMessage(Level, Message, Category);
    LogHistory.Add(FormattedMessage);
    
    // Keep only recent logs (last 1000)
    if (LogHistory.Num() > 1000)
    {
        LogHistory.RemoveAt(0);
    }
    
    // Output to UE log system
    switch (Level)
    {
        case EAuracronWorldPartitionLogLevel::Error:
            UE_LOG(LogTemp, Error, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Warning:
            UE_LOG(LogTemp, Warning, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Log:
            UE_LOG(LogTemp, Log, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::Verbose:
            UE_LOG(LogTemp, Verbose, TEXT("%s"), *FormattedMessage);
            break;
        case EAuracronWorldPartitionLogLevel::VeryVerbose:
            UE_LOG(LogTemp, VeryVerbose, TEXT("%s"), *FormattedMessage);
            break;
        default:
            break;
    }
}

void UAuracronWorldPartitionLogger::LogError(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Error, Message, Category);
}

void UAuracronWorldPartitionLogger::LogWarning(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Warning, Message, Category);
}

void UAuracronWorldPartitionLogger::LogInfo(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Log, Message, Category);
}

void UAuracronWorldPartitionLogger::LogVerbose(const FString& Message, const FString& Category)
{
    LogMessage(EAuracronWorldPartitionLogLevel::Verbose, Message, Category);
}

void UAuracronWorldPartitionLogger::SetLogLevel(EAuracronWorldPartitionLogLevel Level)
{
    CurrentLogLevel = Level;
}

EAuracronWorldPartitionLogLevel UAuracronWorldPartitionLogger::GetLogLevel() const
{
    return CurrentLogLevel;
}

void UAuracronWorldPartitionLogger::EnableCategoryLogging(const FString& Category, bool bEnabled)
{
    if (bEnabled)
    {
        EnabledCategories.Add(Category);
    }
    else
    {
        EnabledCategories.Remove(Category);
    }
}

bool UAuracronWorldPartitionLogger::IsCategoryEnabled(const FString& Category) const
{
    return EnabledCategories.Contains(Category);
}

void UAuracronWorldPartitionLogger::ClearLogs()
{
    FScopeLock Lock(&LogLock);
    LogHistory.Empty();
}

TArray<FString> UAuracronWorldPartitionLogger::GetRecentLogs(int32 MaxCount) const
{
    FScopeLock Lock(&LogLock);
    
    TArray<FString> RecentLogs;
    int32 StartIndex = FMath::Max(0, LogHistory.Num() - MaxCount);
    
    for (int32 i = StartIndex; i < LogHistory.Num(); i++)
    {
        RecentLogs.Add(LogHistory[i]);
    }
    
    return RecentLogs;
}

void UAuracronWorldPartitionLogger::SaveLogsToFile(const FString& FilePath) const
{
    FScopeLock Lock(&LogLock);
    
    FString LogContent;
    for (const FString& LogEntry : LogHistory)
    {
        LogContent += LogEntry + TEXT("\n");
    }
    
    FFileHelper::SaveStringToFile(LogContent, *FilePath);
}

bool UAuracronWorldPartitionLogger::ShouldLog(EAuracronWorldPartitionLogLevel Level) const
{
    return Level <= CurrentLogLevel;
}

FString UAuracronWorldPartitionLogger::FormatLogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category) const
{
    FString LevelString = LogLevelToString(Level);
    FString TimeString = FDateTime::Now().ToString(TEXT("%H:%M:%S"));
    
    return FString::Printf(TEXT("[%s][%s][%s] %s"), *TimeString, *LevelString, *Category, *Message);
}

FString UAuracronWorldPartitionLogger::LogLevelToString(EAuracronWorldPartitionLogLevel Level) const
{
    switch (Level)
    {
        case EAuracronWorldPartitionLogLevel::Error: return TEXT("ERROR");
        case EAuracronWorldPartitionLogLevel::Warning: return TEXT("WARN");
        case EAuracronWorldPartitionLogLevel::Log: return TEXT("INFO");
        case EAuracronWorldPartitionLogLevel::Verbose: return TEXT("VERB");
        case EAuracronWorldPartitionLogLevel::VeryVerbose: return TEXT("VVERB");
        default: return TEXT("NONE");
    }
}

// =============================================================================
// WORLD PARTITION MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionManager* UAuracronWorldPartitionManager::GetInstance()
{
    if (GEngine)
    {
        return GEngine->GetEngineSubsystem<UAuracronWorldPartitionManager>();
    }
    return nullptr;
}

void UAuracronWorldPartitionManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    
    Logger = UAuracronWorldPartitionLogger::GetInstance();
    CurrentState = EAuracronWorldPartitionState::Uninitialized;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition Manager initialized"));
}

void UAuracronWorldPartitionManager::Deinitialize()
{
    if (bIsInitialized)
    {
        ShutdownWorldPartition();
    }
    
    Super::Deinitialize();
    AURACRON_WP_LOG_INFO(TEXT("World Partition Manager deinitialized"));
}

void UAuracronWorldPartitionManager::InitializeWorldPartition(UWorld* World, const FAuracronWorldPartitionConfiguration& InConfiguration)
{
    if (!World)
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot initialize World Partition: World is null"));
        return;
    }

    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("World Partition already initialized"));
        return;
    }

    CurrentState = EAuracronWorldPartitionState::Initializing;
    Configuration = InConfiguration;
    ManagedWorld = World;

    // Validate configuration
    ValidateConfiguration();

    // Setup world partition
    if (!SetupWorldPartition(World))
    {
        CurrentState = EAuracronWorldPartitionState::Error;
        AURACRON_WP_LOG_ERROR(TEXT("Failed to setup World Partition"));
        return;
    }

    // Initialize statistics
    Statistics = FAuracronStreamingStatistics();
    
    bIsInitialized = true;
    CurrentState = EAuracronWorldPartitionState::Ready;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition initialized successfully for world: %s"), *World->GetName());
}

void UAuracronWorldPartitionManager::ShutdownWorldPartition()
{
    if (!bIsInitialized)
    {
        return;
    }

    CurrentState = EAuracronWorldPartitionState::Shutdown;
    
    // Clear cell tracking
    CellInfoMap.Empty();
    LoadedCells.Empty();
    StreamingCells.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    WorldPartition.Reset();
    
    bIsInitialized = false;
    CurrentState = EAuracronWorldPartitionState::Uninitialized;
    
    AURACRON_WP_LOG_INFO(TEXT("World Partition shutdown completed"));
}

bool UAuracronWorldPartitionManager::IsInitialized() const
{
    return bIsInitialized;
}

EAuracronWorldPartitionState UAuracronWorldPartitionManager::GetState() const
{
    return CurrentState;
}

bool UAuracronWorldPartitionManager::SetupWorldPartition(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    // Get or create world partition
    UWorldPartition* WP = World->GetWorldPartition();
    if (!WP)
    {
        AURACRON_WP_LOG_WARNING(TEXT("World does not have World Partition enabled"));
        return false;
    }

    WorldPartition = WP;
    
    // Verify world partition is initialized
    if (!WP->IsInitialized())
    {
        AURACRON_WP_LOG_ERROR(TEXT("World Partition is not initialized"));
        return false;
    }

    AURACRON_WP_LOG_INFO(TEXT("World Partition setup completed"));
    return true;
}

UWorldPartition* UAuracronWorldPartitionManager::GetWorldPartition() const
{
    return WorldPartition.Get();
}

UDataLayerSubsystem* UAuracronWorldPartitionManager::GetDataLayerSubsystem() const
{
    if (UWorld* World = ManagedWorld.Get())
    {
        return World->GetSubsystem<UDataLayerSubsystem>();
    }
    return nullptr;
}

UWorldPartitionSubsystem* UAuracronWorldPartitionManager::GetWorldPartitionSubsystem() const
{
    if (UWorld* World = ManagedWorld.Get())
    {
        return World->GetSubsystem<UWorldPartitionSubsystem>();
    }
    return nullptr;
}

void UAuracronWorldPartitionManager::ValidateConfiguration()
{
    // Validate streaming distances
    if (Configuration.StreamingDistance <= 0.0f)
    {
        Configuration.StreamingDistance = 10000.0f;
        AURACRON_WP_LOG_WARNING(TEXT("Invalid streaming distance, set to default: %.1f"), Configuration.StreamingDistance);
    }

    if (Configuration.UnloadingDistance <= Configuration.StreamingDistance)
    {
        Configuration.UnloadingDistance = Configuration.StreamingDistance * 1.5f;
        AURACRON_WP_LOG_WARNING(TEXT("Unloading distance adjusted to: %.1f"), Configuration.UnloadingDistance);
    }

    // Validate grid sizes
    Configuration.DefaultGridSize = FMath::Clamp(Configuration.DefaultGridSize, Configuration.MinGridSize, Configuration.MaxGridSize);
    
    // Validate concurrent requests
    if (Configuration.MaxConcurrentStreamingRequests <= 0)
    {
        Configuration.MaxConcurrentStreamingRequests = 8;
        AURACRON_WP_LOG_WARNING(TEXT("Invalid max concurrent requests, set to default: %d"), Configuration.MaxConcurrentStreamingRequests);
    }
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetAllCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> AllCells;
    
    if (!bIsInitialized || !ManagedWorld.IsValid())
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get all cells: Manager not initialized or world invalid"));
        return AllCells;
    }
    
    UWorld* World = ManagedWorld.Get();
    UWorldPartition* WP = World->GetWorldPartition();
    if (!WP)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get all cells: World Partition not available"));
        return AllCells;
    }
    
    // Get runtime hash to access cells
    if (UWorldPartitionRuntimeHash* RuntimeHash = WP->GetRuntimeHash())
    {
        // Get streaming grid bounds
        FBox WorldBounds = WP->GetWorldBounds();
        if (!WorldBounds.IsValid)
        {
            // Use default bounds if world bounds are invalid
            WorldBounds = FBox(FVector(-100000, -100000, -10000), FVector(100000, 100000, 10000));
        }
        
        // Calculate grid dimensions based on configuration
        float CellSize = Configuration.DefaultGridSize > 0 ? Configuration.DefaultGridSize : 25600.0f;
        
        FVector GridMin = WorldBounds.Min;
        FVector GridMax = WorldBounds.Max;
        
        int32 GridSizeX = FMath::CeilToInt((GridMax.X - GridMin.X) / CellSize);
        int32 GridSizeY = FMath::CeilToInt((GridMax.Y - GridMin.Y) / CellSize);
        
        // Generate cell info for each grid position
        for (int32 X = 0; X < GridSizeX; X++)
        {
            for (int32 Y = 0; Y < GridSizeY; Y++)
            {
                FIntVector Coordinates(X, Y, 0);
                FAuracronCellInfo CellInfo = CreateCellInfo(Coordinates);
                
                // Calculate cell bounds
                FVector CellMin = GridMin + FVector(X * CellSize, Y * CellSize, GridMin.Z);
                FVector CellMax = CellMin + FVector(CellSize, CellSize, GridMax.Z - GridMin.Z);
                CellInfo.CellBounds = FBox(CellMin, CellMax);
                
                // Check if cell is currently loaded
                CellInfo.StreamingState = LoadedCells.Contains(CellInfo.CellId) ? 
                    EAuracronStreamingState::Loaded : EAuracronStreamingState::Unloaded;
                
                AllCells.Add(CellInfo);
            }
        }
    }
    
    // Also add any cells from our tracking map that might not be in the grid
    CellInfoMap.GenerateValueArray(AllCells);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Retrieved %d cells"), AllCells.Num());
    return AllCells;
}

FAuracronCellInfo UAuracronWorldPartitionManager::GetCellInfo(const FString& CellId) const
{
    FScopeLock Lock(&ManagerLock);

    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get cell info: Manager not initialized"));
        return FAuracronCellInfo();
    }
    
    if (CellId.IsEmpty())
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get cell info: CellId is empty"));
        return FAuracronCellInfo();
    }
    
    // First check our cached cell info
    if (const FAuracronCellInfo* FoundInfo = CellInfoMap.Find(CellId))
    {
        return *FoundInfo;
    }
    
    // Try to generate cell info from CellId if not cached
    FAuracronCellInfo CellInfo;
    CellInfo.CellId = CellId;
    
    // Parse coordinates from CellId (assuming format like "Cell_X_Y" or "X_Y")
    FString ParsedId = CellId;
    ParsedId.RemoveFromStart(TEXT("Cell_"));
    
    TArray<FString> Coordinates;
    ParsedId.ParseIntoArray(Coordinates, TEXT("_"));
    
    if (Coordinates.Num() >= 2)
    {
        int32 X = FCString::Atoi(*Coordinates[0]);
        int32 Y = FCString::Atoi(*Coordinates[1]);
        int32 Z = Coordinates.Num() > 2 ? FCString::Atoi(*Coordinates[2]) : 0;
        
        CellInfo.CellCoordinates = FIntVector(X, Y, Z);
        
        // Calculate cell bounds based on coordinates
        float CellSize = Configuration.DefaultGridSize > 0 ? Configuration.DefaultGridSize : 25600.0f;
        FVector CellMin = FVector(X * CellSize, Y * CellSize, Z * CellSize);
        FVector CellMax = CellMin + FVector(CellSize, CellSize, CellSize);
        CellInfo.CellBounds = FBox(CellMin, CellMax);
        
        // Check current streaming state
        CellInfo.StreamingState = LoadedCells.Contains(CellId) ? 
            EAuracronStreamingState::Loaded : EAuracronStreamingState::Unloaded;
        
        // Set other properties
        CellInfo.CellType = EAuracronCellType::Static;
        CellInfo.LastAccessTime = FDateTime::Now();
        CellInfo.LoadingTime = 0.0f;
        
        // Cache the generated info
        const_cast<UAuracronWorldPartitionManager*>(this)->CellInfoMap.Add(CellId, CellInfo);
        
        AURACRON_WP_LOG_VERBOSE(TEXT("Generated cell info for %s at coordinates (%d, %d, %d)"), 
            *CellId, X, Y, Z);
    }
    else
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot parse coordinates from CellId: %s"), *CellId);
    }
    
    return CellInfo;
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetCellsInRadius(const FVector& Location, float Radius) const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> CellsInRadius;
    
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get cells in radius: Manager not initialized"));
        return CellsInRadius;
    }
    
    if (Radius <= 0.0f)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get cells in radius: Invalid radius %f"), Radius);
        return CellsInRadius;
    }
    
    float CellSize = Configuration.DefaultGridSize > 0 ? Configuration.DefaultGridSize : 25600.0f;
    float RadiusSquared = Radius * Radius;
    
    // Calculate the range of cells to check
    int32 CellRadius = FMath::CeilToInt(Radius / CellSize) + 1; // Add 1 for safety margin
    
    // Get the center cell coordinates
    int32 CenterX = FMath::FloorToInt(Location.X / CellSize);
    int32 CenterY = FMath::FloorToInt(Location.Y / CellSize);
    int32 CenterZ = FMath::FloorToInt(Location.Z / CellSize);
    
    // Check cells in a square around the center, then filter by actual distance
    for (int32 X = CenterX - CellRadius; X <= CenterX + CellRadius; X++)
    {
        for (int32 Y = CenterY - CellRadius; Y <= CenterY + CellRadius; Y++)
        {
            for (int32 Z = CenterZ - 1; Z <= CenterZ + 1; Z++) // Limited Z range for performance
            {
                // Calculate cell center
                FVector CellCenter = FVector(
                    (X + 0.5f) * CellSize,
                    (Y + 0.5f) * CellSize,
                    (Z + 0.5f) * CellSize
                );
                
                // Check if cell center is within radius
                float DistanceSquared = FVector::DistSquared(Location, CellCenter);
                if (DistanceSquared <= RadiusSquared)
                {
                    // Generate cell ID and get cell info
                    FString CellId = GenerateCellId(FIntVector(X, Y, Z));
                    FAuracronCellInfo CellInfo = GetCellInfo(CellId);
                    
                    if (!CellInfo.CellId.IsEmpty())
                    {
                        // Calculate actual distance for sorting
                        CellInfo.DistanceFromQuery = FMath::Sqrt(DistanceSquared);
                        CellsInRadius.Add(CellInfo);
                    }
                }
            }
        }
    }
    
    // Sort by distance (closest first)
    CellsInRadius.Sort([](const FAuracronCellInfo& A, const FAuracronCellInfo& B) {
        return A.DistanceFromQuery < B.DistanceFromQuery;
    });
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Found %d cells within radius %f of location %s"), 
        CellsInRadius.Num(), Radius, *Location.ToString());

    return CellsInRadius;
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetLoadedCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> LoadedCellInfos;

    for (const FString& CellId : LoadedCells)
    {
        const FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
        if (CellInfo)
        {
            LoadedCellInfos.Add(*CellInfo);
        }
    }

    return LoadedCellInfos;
}

TArray<FAuracronCellInfo> UAuracronWorldPartitionManager::GetStreamingCells() const
{
    FScopeLock Lock(&ManagerLock);

    TArray<FAuracronCellInfo> StreamingCellInfos;

    for (const FString& CellId : StreamingCells)
    {
        const FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
        if (CellInfo)
        {
            StreamingCellInfos.Add(*CellInfo);
        }
    }

    return StreamingCellInfos;
}

void UAuracronWorldPartitionManager::RequestCellLoading(const FString& CellId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell loading: Manager not initialized"));
        return;
    }

    FScopeLock Lock(&ManagerLock);

    if (LoadedCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell already loaded: %s"), *CellId);
        return;
    }

    if (StreamingCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell already streaming: %s"), *CellId);
        return;
    }

    // Add to streaming set
    StreamingCells.Add(CellId);

    // Update cell info
    FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Loading;
        CellInfo->LastAccessTime = FDateTime::Now();
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Requested cell loading: %s"), *CellId);

    // In a real implementation, this would trigger actual streaming
    // For now, we simulate immediate loading
    FDateTime LoadStartTime = FDateTime::Now();

    // Simulate loading time
    float SimulatedLoadTime = FMath::RandRange(0.1f, 2.0f);

    // Move from streaming to loaded
    StreamingCells.Remove(CellId);
    LoadedCells.Add(CellId);

    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Loaded;
        CellInfo->LoadingTime = SimulatedLoadTime;
    }

    OnCellLoadedInternal(CellId, SimulatedLoadTime);
}

void UAuracronWorldPartitionManager::RequestCellUnloading(const FString& CellId)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot request cell unloading: Manager not initialized"));
        return;
    }

    FScopeLock Lock(&ManagerLock);

    if (!LoadedCells.Contains(CellId))
    {
        AURACRON_WP_LOG_VERBOSE(TEXT("Cell not loaded: %s"), *CellId);
        return;
    }

    // Remove from loaded set
    LoadedCells.Remove(CellId);

    // Update cell info
    FAuracronCellInfo* CellInfo = CellInfoMap.Find(CellId);
    if (CellInfo)
    {
        CellInfo->StreamingState = EAuracronStreamingState::Unloaded;
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Requested cell unloading: %s"), *CellId);

    // Simulate unloading time
    float SimulatedUnloadTime = FMath::RandRange(0.05f, 0.5f);

    OnCellUnloadedInternal(CellId, SimulatedUnloadTime);
}

void UAuracronWorldPartitionManager::SetConfiguration(const FAuracronWorldPartitionConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Configuration updated"));
}

FAuracronWorldPartitionConfiguration UAuracronWorldPartitionManager::GetConfiguration() const
{
    return Configuration;
}

void UAuracronWorldPartitionManager::SetStreamingDistance(float Distance)
{
    Configuration.StreamingDistance = FMath::Max(0.0f, Distance);

    // Adjust unloading distance if necessary
    if (Configuration.UnloadingDistance <= Configuration.StreamingDistance)
    {
        Configuration.UnloadingDistance = Configuration.StreamingDistance * 1.5f;
    }

    AURACRON_WP_LOG_INFO(TEXT("Streaming distance set to: %.1f"), Configuration.StreamingDistance);
}

float UAuracronWorldPartitionManager::GetStreamingDistance() const
{
    return Configuration.StreamingDistance;
}

FAuracronStreamingStatistics UAuracronWorldPartitionManager::GetStreamingStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronStreamingStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronStreamingStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Statistics reset"));
}

float UAuracronWorldPartitionManager::GetMemoryUsage() const
{
    if (!bIsInitialized)
    {
        return 0.0f;
    }
    
    float TotalMemoryMB = 0.0f;
    
    // Calculate memory usage from various sources
    
    // 1. Cell info map memory
    TotalMemoryMB += (CellInfoMap.Num() * sizeof(FAuracronCellInfo)) / (1024.0f * 1024.0f);
    
    // 2. Loaded cells tracking
    TotalMemoryMB += (LoadedCells.Num() * sizeof(FString)) / (1024.0f * 1024.0f);
    
    // 3. Streaming cells tracking
    TotalMemoryMB += (StreamingCells.Num() * sizeof(FString)) / (1024.0f * 1024.0f);
    
    // 4. World Partition subsystem memory (if available)
    if (ManagedWorld.IsValid())
    {
        UWorld* World = ManagedWorld.Get();
        if (UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
        {
            // Estimate memory from world partition subsystem
            // This is an approximation as UE doesn't expose exact memory usage
            TotalMemoryMB += 50.0f; // Base subsystem overhead
        }
        
        // 5. Data Layer subsystem memory
        if (UDataLayerSubsystem* DataLayerSubsystem = World->GetSubsystem<UDataLayerSubsystem>())
        {
            TotalMemoryMB += 10.0f; // Base data layer overhead
        }
    }
    
    // 6. Configuration and other overhead
    TotalMemoryMB += sizeof(FAuracronWorldPartitionConfiguration) / (1024.0f * 1024.0f);
    TotalMemoryMB += sizeof(FAuracronStreamingStatistics) / (1024.0f * 1024.0f);
    
    // 7. Platform-specific memory usage (if available)
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    if (MemStats.UsedPhysical > 0)
    {
        // Add a small percentage as our estimated contribution to total memory
        float EstimatedContribution = (LoadedCells.Num() * 0.1f); // 0.1MB per loaded cell estimate
        TotalMemoryMB += EstimatedContribution;
    }
    
    // Update statistics with calculated memory usage
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.TotalMemoryUsageMB = TotalMemoryMB;
    }
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Estimated memory usage: %.2f MB"), TotalMemoryMB);
    
    return TotalMemoryMB;
}

int32 UAuracronWorldPartitionManager::GetLoadedCellCount() const
{
    FScopeLock Lock(&ManagerLock);
    return LoadedCells.Num();
}

int32 UAuracronWorldPartitionManager::GetTotalCellCount() const
{
    FScopeLock Lock(&ManagerLock);
    return CellInfoMap.Num();
}

void UAuracronWorldPartitionManager::OnCellLoadedInternal(const FString& CellId, float LoadingTime)
{
    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.LoadedCells++;
        Statistics.StreamingRequests++;
        Statistics.AverageLoadingTime = (Statistics.AverageLoadingTime + LoadingTime) / 2.0f;
    }

    // Broadcast event
    OnCellLoaded.Broadcast(CellId, LoadingTime);

    AURACRON_WP_LOG_VERBOSE(TEXT("Cell loaded: %s (%.3fs)"), *CellId, LoadingTime);
}

void UAuracronWorldPartitionManager::OnCellUnloadedInternal(const FString& CellId, float UnloadingTime)
{
    // Update statistics
    {
        FScopeLock Lock(&StatisticsLock);
        Statistics.LoadedCells = FMath::Max(0, Statistics.LoadedCells - 1);
    }

    // Broadcast event
    OnCellUnloaded.Broadcast(CellId, UnloadingTime);

    AURACRON_WP_LOG_VERBOSE(TEXT("Cell unloaded: %s (%.3fs)"), *CellId, UnloadingTime);
}

FString UAuracronWorldPartitionManager::GenerateCellId(const FIntVector& Coordinates) const
{
    // Generate a consistent cell ID based on coordinates
    // Format: "Cell_X_Y_Z" for 3D or "Cell_X_Y" for 2D grids
    
    if (Configuration.bUse2DGrid)
    {
        return FString::Printf(TEXT("Cell_%d_%d"), Coordinates.X, Coordinates.Y);
    }
    else
    {
        return FString::Printf(TEXT("Cell_%d_%d_%d"), Coordinates.X, Coordinates.Y, Coordinates.Z);
    }
}

FAuracronCellInfo UAuracronWorldPartitionManager::CreateCellInfo(const FIntVector& Coordinates) const
{
    FAuracronCellInfo CellInfo;
    
    // Generate cell ID
    CellInfo.CellId = GenerateCellId(Coordinates);
    CellInfo.CellCoordinates = Coordinates;
    
    // Calculate cell bounds based on coordinates and configuration
    float CellSize = Configuration.DefaultGridSize > 0 ? Configuration.DefaultGridSize : 25600.0f;
    
    FVector CellMin = FVector(
        Coordinates.X * CellSize,
        Coordinates.Y * CellSize,
        Coordinates.Z * CellSize
    );
    
    FVector CellMax = CellMin + FVector(CellSize, CellSize, CellSize);
    CellInfo.CellBounds = FBox(CellMin, CellMax);
    
    // Set default properties
    CellInfo.CellType = EAuracronCellType::Static;
    CellInfo.StreamingState = EAuracronStreamingState::Unloaded;
    CellInfo.LastAccessTime = FDateTime::Now();
    CellInfo.LoadingTime = 0.0f;
    CellInfo.DistanceFromQuery = 0.0f;
    
    // Check if this cell is currently loaded
    if (LoadedCells.Contains(CellInfo.CellId))
    {
        CellInfo.StreamingState = EAuracronStreamingState::Loaded;
    }
    else if (StreamingCells.Contains(CellInfo.CellId))
    {
        CellInfo.StreamingState = EAuracronStreamingState::Loading;
    }
    
    // Determine cell type based on coordinates (example logic)
    if (FMath::Abs(Coordinates.X) > 10 || FMath::Abs(Coordinates.Y) > 10)
    {
        CellInfo.CellType = EAuracronCellType::Dynamic; // Far cells are dynamic
    }
    
    // Set priority based on distance from origin
    float DistanceFromOrigin = FVector(Coordinates.X, Coordinates.Y, Coordinates.Z).Size();
    CellInfo.Priority = FMath::Max(0, 100 - FMath::FloorToInt(DistanceFromOrigin));
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Created cell info for %s at coordinates (%d, %d, %d)"), 
        *CellInfo.CellId, Coordinates.X, Coordinates.Y, Coordinates.Z);
    
    return CellInfo;
}
