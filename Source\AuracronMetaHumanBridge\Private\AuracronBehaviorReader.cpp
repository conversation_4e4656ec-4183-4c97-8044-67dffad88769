#include "AuracronBehaviorReader.h"
#include "AuracronDNAReaderWriter.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"

DEFINE_LOG_CATEGORY(LogAuracronBehaviorReader);

// ========================================
// FAuracronBehaviorReader Implementation
// ========================================

FAuracronBehaviorReader::FAuracronBehaviorReader()
    : NativeReader(nullptr)
    , bIsValid(false)
    , bBehaviorDataCached(false)
    , bControlRigDataCached(false)
{
}

FAuracronBehaviorReader::~FAuracronBehaviorReader()
{
    Reset();
}

bool FAuracronBehaviorReader::InitializeFromReader(const FAuracronDNAReader& Reader)
{
    FScopeLock Lock(&AccessMutex);

    if (!Reader.IsValid())
    {
        LastError = TEXT("Invalid DNA Reader provided");
        LogError(LastError);
        return false;
    }

    Reset();

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        // Get reference to the native reader using UE5.6 smart pointer system
        NativeReader = Reader.GetNativeReader();
        bIsValid = (NativeReader != nullptr);

        if (bIsValid)
        {
            UE_LOG(LogAuracronBehaviorReader, Log, TEXT("Successfully initialized behavior reader from DNA reader"));
        }
        else
        {
            LastError = TEXT("Failed to get native reader reference");
            LogError(LastError);
        }

        return bIsValid;
    }
    catch (const std::exception& e)
    {
        LastError = FString::Printf(TEXT("Exception initializing behavior reader: %s"), UTF8_TO_TCHAR(e.what()));
        LogError(LastError);
        return false;
    }
#else
    LastError = TEXT("MetaHuman DNA Calibration not available in this build");
    LogError(LastError);
    return false;
#endif
}

bool FAuracronBehaviorReader::IsValid() const
{
    FScopeLock Lock(&AccessMutex);
    return bIsValid && NativeReader != nullptr;
}

void FAuracronBehaviorReader::Reset()
{
    FScopeLock Lock(&AccessMutex);

    // Note: We don't own the NativeReader, just reference it
    NativeReader = nullptr;
    bIsValid = false;
    LastError.Empty();
    ValidationWarnings.Empty();
    ClearCache();
}

FMetaHumanBehaviorData FAuracronBehaviorReader::GetBehaviorData() const
{
    FScopeLock Lock(&AccessMutex);

    if (bBehaviorDataCached)
    {
        return CachedBehaviorData;
    }

    FMetaHumanBehaviorData BehaviorData;

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return BehaviorData;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto* Reader = static_cast<dna::BinaryStreamReader*>(NativeReader);

        // Get control count and names using UE5.6 optimized batch operations
        int32 ControlCount = Reader->getGUIControlCount();
        BehaviorData.ControlNames.Reserve(ControlCount);
        BehaviorData.ControlValues.Reserve(ControlCount);

        for (int32 i = 0; i < ControlCount; ++i)
        {
            FString ControlName = UTF8_TO_TCHAR(Reader->getGUIControlName(i));
            BehaviorData.ControlNames.Add(ControlName);

            // Get default control values using UE5.6 DNA APIs
            float DefaultValue = 0.0f;
            if (Reader->getGUIControlCount() > i)
            {
                // Access actual control values from the DNA behavior layer
                try
                {
                    // Get the control's default value from the DNA data
                    DefaultValue = Reader->getGUIControlDefaultValue(i);
                    
                    // Validate the value is within expected range
                    float MinValue = Reader->getGUIControlMinValue(i);
                    float MaxValue = Reader->getGUIControlMaxValue(i);
                    DefaultValue = FMath::Clamp(DefaultValue, MinValue, MaxValue);
                    
                    UE_LOG(LogAuracronBehaviorReader, VeryVerbose, TEXT("Control %d: Default=%.3f, Range=[%.3f, %.3f]"), 
                           i, DefaultValue, MinValue, MaxValue);
                }
                catch (const std::exception& e)
                {
                    UE_LOG(LogAuracronBehaviorReader, Warning, TEXT("Failed to get control value for index %d: %s"), 
                           i, UTF8_TO_TCHAR(e.what()));
                    DefaultValue = 0.0f; // Safe fallback
                }
            }
            BehaviorData.ControlValues.Add(DefaultValue);
        }

        // Get animated map count and names using UE5.6 batch processing
        int32 AnimatedMapCount = Reader->getAnimatedMapCount();
        BehaviorData.AnimatedMapNames.Reserve(AnimatedMapCount);
        BehaviorData.AnimatedMapIndices.Reserve(AnimatedMapCount);

        for (int32 i = 0; i < AnimatedMapCount; ++i)
        {
            FString MapName = UTF8_TO_TCHAR(Reader->getAnimatedMapName(i));
            BehaviorData.AnimatedMapNames.Add(MapName);
            BehaviorData.AnimatedMapIndices.Add(i);
        }

        // Get blend shape channel information using UE5.6 APIs
        int32 BlendShapeChannelCount = Reader->getBlendShapeChannelCount();
        BehaviorData.BlendShapeChannelNames.Reserve(BlendShapeChannelCount);
        BehaviorData.BlendShapeChannelIndices.Reserve(BlendShapeChannelCount);

        for (int32 i = 0; i < BlendShapeChannelCount; ++i)
        {
            FString ChannelName = UTF8_TO_TCHAR(Reader->getBlendShapeChannelName(i));
            BehaviorData.BlendShapeChannelNames.Add(ChannelName);
            BehaviorData.BlendShapeChannelIndices.Add(i);
        }

        // Cache the result for performance using UE5.6 caching system
        CachedBehaviorData = BehaviorData;
        bBehaviorDataCached = true;

        UE_LOG(LogAuracronBehaviorReader, Log, TEXT("Retrieved behavior data: %d controls, %d animated maps, %d blend shape channels"), 
               ControlCount, AnimatedMapCount, BlendShapeChannelCount);
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting behavior data: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return BehaviorData;
}

FMetaHumanControlRigData FAuracronBehaviorReader::GetControlRigData() const
{
    FScopeLock Lock(&AccessMutex);

    if (bControlRigDataCached)
    {
        return CachedControlRigData;
    }

    FMetaHumanControlRigData ControlRigData;

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return ControlRigData;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto* Reader = static_cast<dna::BinaryStreamReader*>(NativeReader);

        // Get control rig information using UE5.6 Control Rig APIs
        ControlRigData.ControlCount = Reader->getGUIControlCount();
        ControlRigData.ControlNames.Reserve(ControlRigData.ControlCount);
        ControlRigData.ControlIndices.Reserve(ControlRigData.ControlCount);
        ControlRigData.DefaultValues.Reserve(ControlRigData.ControlCount);

        for (int32 i = 0; i < ControlRigData.ControlCount; ++i)
        {
            FString ControlName = UTF8_TO_TCHAR(Reader->getGUIControlName(i));
            ControlRigData.ControlNames.Add(ControlName);
            ControlRigData.ControlIndices.Add(i);

            // Get default control values using UE5.6 Control Rig system
            float DefaultValue = 0.0f;
            try
            {
                // Access actual control rig default values from the DNA behavior layer
                DefaultValue = Reader->getGUIControlDefaultValue(i);
                
                // Validate the value is within expected range for Control Rig
                float MinValue = Reader->getGUIControlMinValue(i);
                float MaxValue = Reader->getGUIControlMaxValue(i);
                DefaultValue = FMath::Clamp(DefaultValue, MinValue, MaxValue);
                
                UE_LOG(LogAuracronBehaviorReader, VeryVerbose, TEXT("Control Rig %s: Default=%.3f, Range=[%.3f, %.3f]"), 
                       *ControlName, DefaultValue, MinValue, MaxValue);
            }
            catch (const std::exception& e)
            {
                UE_LOG(LogAuracronBehaviorReader, Warning, TEXT("Failed to get control rig default value for %s: %s"), 
                       *ControlName, UTF8_TO_TCHAR(e.what()));
                DefaultValue = 0.0f; // Safe fallback for Control Rig
            }
            ControlRigData.DefaultValues.Add(DefaultValue);
        }

        // Get joint animation data using UE5.6 animation system
        int32 JointCount = Reader->getJointCount();
        ControlRigData.JointNames.Reserve(JointCount);
        ControlRigData.JointIndices.Reserve(JointCount);

        for (int32 i = 0; i < JointCount; ++i)
        {
            FString JointName = UTF8_TO_TCHAR(Reader->getJointName(i));
            ControlRigData.JointNames.Add(JointName);
            ControlRigData.JointIndices.Add(i);
        }

        // Cache the result using UE5.6 caching system
        CachedControlRigData = ControlRigData;
        bControlRigDataCached = true;

        UE_LOG(LogAuracronBehaviorReader, Log, TEXT("Retrieved control rig data: %d controls, %d joints"), 
               ControlRigData.ControlCount, JointCount);
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting control rig data: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return ControlRigData;
}

int32 FAuracronBehaviorReader::GetControlCount() const
{
    FScopeLock Lock(&AccessMutex);

    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return 0;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto* Reader = static_cast<dna::BinaryStreamReader*>(NativeReader);
        return Reader->getGUIControlCount();
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting control count: %s"), UTF8_TO_TCHAR(e.what())));
        return 0;
    }
#else
    return 0;
#endif
}

FString FAuracronBehaviorReader::GetControlName(int32 ControlIndex) const
{
    FScopeLock Lock(&AccessMutex);

    if (!ValidateControlIndex(ControlIndex))
    {
        return TEXT("");
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto* Reader = static_cast<dna::BinaryStreamReader*>(NativeReader);
        return UTF8_TO_TCHAR(Reader->getGUIControlName(ControlIndex));
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting control name: %s"), UTF8_TO_TCHAR(e.what())));
        return TEXT("");
    }
#else
    return TEXT("");
#endif
}

TArray<float> FAuracronBehaviorReader::GetControlValues(int32 ControlIndex) const
{
    FScopeLock Lock(&AccessMutex);

    TArray<float> Values;

    if (!ValidateControlIndex(ControlIndex))
    {
        return Values;
    }

#ifdef WITH_METAHUMAN_DNA_CALIBRATION
    try
    {
        auto* Reader = static_cast<dna::BinaryStreamReader*>(NativeReader);
        
        // Access actual control values from the DNA using UE5.6 optimized batch operations
        if (Reader && Reader->getControlCount() > ControlIndex)
        {
            // Get the control's default value
            float DefaultValue = Reader->getControlDefaultValue(ControlIndex);
            Values.Add(DefaultValue);
            
            // If there are animation frames, get values for each frame
            uint32 AnimationCount = Reader->getAnimatedMapCount();
            for (uint32 AnimIndex = 0; AnimIndex < AnimationCount; ++AnimIndex)
            {
                // Get control value for this animation frame
                float AnimValue = Reader->getControlValue(ControlIndex, AnimIndex);
                Values.Add(AnimValue);
            }
        }
        else
        {
            // Fallback to default value if control index is invalid
            Values.Add(0.0f);
        }
        
        UE_LOG(LogAuracronBehaviorReader, VeryVerbose, TEXT("Retrieved %d values for control %d"), Values.Num(), ControlIndex);
    }
    catch (const std::exception& e)
    {
        LogError(FString::Printf(TEXT("Exception getting control values: %s"), UTF8_TO_TCHAR(e.what())));
    }
#endif

    return Values;
}

bool FAuracronBehaviorReader::ValidateControlIndex(int32 ControlIndex) const
{
    if (!IsValid())
    {
        LogWarning(TEXT("No valid DNA data loaded"));
        return false;
    }

    if (ControlIndex < 0 || ControlIndex >= GetControlCount())
    {
        LogWarning(FString::Printf(TEXT("Invalid control index: %d"), ControlIndex));
        return false;
    }

    return true;
}

void FAuracronBehaviorReader::LogError(const FString& ErrorMessage) const
{
    UE_LOG(LogAuracronBehaviorReader, Error, TEXT("FAuracronBehaviorReader: %s"), *ErrorMessage);
}

void FAuracronBehaviorReader::LogWarning(const FString& WarningMessage) const
{
    UE_LOG(LogAuracronBehaviorReader, Warning, TEXT("FAuracronBehaviorReader: %s"), *WarningMessage);
}

void FAuracronBehaviorReader::ClearCache() const
{
    bBehaviorDataCached = false;
    bControlRigDataCached = false;
    CachedBehaviorData = FMetaHumanBehaviorData();
    CachedControlRigData = FMetaHumanControlRigData();
}

FString FAuracronBehaviorReader::GetLastError() const
{
    FScopeLock Lock(&AccessMutex);
    return LastError;
}

TArray<FString> FAuracronBehaviorReader::GetValidationWarnings() const
{
    FScopeLock Lock(&AccessMutex);
    return ValidationWarnings;
}
