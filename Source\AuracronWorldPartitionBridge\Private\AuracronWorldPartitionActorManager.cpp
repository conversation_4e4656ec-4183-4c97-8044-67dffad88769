// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Actor Manager Implementation
// Bridge 3.5: World Partition - Actor Management

#include "AuracronWorldPartitionActorManager.h"
#include "AuracronWorldPartition.h"
#include "AuracronWorldPartitionGrid.h"
#include "AuracronWorldPartitionBridge.h"

// World Partition includes
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionActorDescView.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Level.h"
#include "GameFramework/Actor.h"
#include "HAL/PlatformMemory.h"
#include "DrawDebugHelpers.h"
#include "Misc/DateTime.h"

// =============================================================================
// ACTOR STATISTICS IMPLEMENTATION
// =============================================================================

void FAuracronActorStatistics::UpdateCalculatedFields()
{
    if (TotalActors > 0)
    {
        ActorEfficiency = static_cast<float>(LoadedActors) / static_cast<float>(TotalActors);
    }
    else
    {
        ActorEfficiency = 0.0f;
    }
    
    LastUpdateTime = FDateTime::Now();
}

// =============================================================================
// WORLD PARTITION ACTOR MANAGER IMPLEMENTATION
// =============================================================================

UAuracronWorldPartitionActorManager* UAuracronWorldPartitionActorManager::Instance = nullptr;

UAuracronWorldPartitionActorManager* UAuracronWorldPartitionActorManager::GetInstance()
{
    if (!Instance)
    {
        Instance = NewObject<UAuracronWorldPartitionActorManager>();
        Instance->AddToRoot(); // Prevent garbage collection
    }
    return Instance;
}

void UAuracronWorldPartitionActorManager::Initialize(const FAuracronActorManagementConfiguration& InConfiguration)
{
    if (bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Actor Manager already initialized"));
        return;
    }

    Configuration = InConfiguration;
    ValidateConfiguration();

    // Initialize statistics
    Statistics = FAuracronActorStatistics();
    
    // Clear collections
    ActorDescriptors.Empty();
    ActorReferences.Empty();
    ActorCrossReferences.Empty();
    ActorReferencedBy.Empty();
    ActorToCellMap.Empty();
    CellToActorsMap.Empty();
    
    bIsInitialized = true;
    
    AURACRON_WP_LOG_INFO(TEXT("Actor Manager initialized with max actors per cell: %d"), Configuration.MaxActorsPerCell);
}

void UAuracronWorldPartitionActorManager::Shutdown()
{
    if (!bIsInitialized)
    {
        return;
    }

    // Unload all actors
    TArray<FString> ActorsToUnload;
    ActorDescriptors.GenerateKeyArray(ActorsToUnload);
    
    for (const FString& ActorId : ActorsToUnload)
    {
        UnloadActor(ActorId);
    }
    
    // Clear all data
    ActorDescriptors.Empty();
    ActorReferences.Empty();
    ActorCrossReferences.Empty();
    ActorReferencedBy.Empty();
    ActorToCellMap.Empty();
    CellToActorsMap.Empty();
    
    // Reset references
    ManagedWorld.Reset();
    
    bIsInitialized = false;
    
    AURACRON_WP_LOG_INFO(TEXT("Actor Manager shutdown completed"));
}

bool UAuracronWorldPartitionActorManager::IsInitialized() const
{
    return bIsInitialized;
}

void UAuracronWorldPartitionActorManager::Tick(float DeltaTime)
{
    if (!bIsInitialized)
    {
        return;
    }

    // Update actor lifecycles
    UpdateActorLifecycles();
    
    // Update statistics
    UpdateStatistics();
}

FString UAuracronWorldPartitionActorManager::PlaceActor(const FString& ActorClass, const FVector& Location, const FRotator& Rotation, EAuracronActorPlacementType PlacementType)
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot place actor: Manager not initialized"));
        return FString();
    }

    if (ActorClass.IsEmpty())
    {
        AURACRON_WP_LOG_ERROR(TEXT("Cannot place actor: Actor class is empty"));
        return FString();
    }

    FScopeLock Lock(&ActorLock);
    
    FString ActorId = GenerateActorId(ActorClass);
    
    // Create actor descriptor
    FAuracronActorDescriptor ActorDesc;
    ActorDesc.ActorId = ActorId;
    ActorDesc.ActorName = FString::Printf(TEXT("%s_%s"), *ActorClass, *ActorId);
    ActorDesc.ActorClass = ActorClass;
    ActorDesc.Location = Location;
    ActorDesc.Rotation = Rotation;
    ActorDesc.PlacementType = PlacementType;
    ActorDesc.StreamingState = EAuracronActorStreamingState::Unloaded;
    ActorDesc.LifecycleState = EAuracronActorLifecycleState::Created;
    ActorDesc.bIsSpatiallyLoaded = (PlacementType != EAuracronActorPlacementType::Persistent);
    ActorDesc.CreationTime = FDateTime::Now();
    ActorDesc.LastAccessTime = ActorDesc.CreationTime;
    
    // Calculate real bounds based on actor class and components
    FVector BoundsExtent = CalculateActorBounds(ActorClass, Location);
    ActorDesc.Bounds = FBox(Location - BoundsExtent, Location + BoundsExtent);
    
    // Add to collections
    ActorDescriptors.Add(ActorId, ActorDesc);
    
    // Update cell mapping
    UpdateActorCellMapping(ActorId);
    
    OnActorPlacedInternal(ActorId, Location);
    
    AURACRON_WP_LOG_INFO(TEXT("Actor placed: %s (%s) at %s"), *ActorClass, *ActorId, *Location.ToString());
    
    return ActorId;
}

bool UAuracronWorldPartitionActorManager::RemoveActor(const FString& ActorId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);
    
    if (!ActorDescriptors.Contains(ActorId))
    {
        return false;
    }
    
    // Unload actor if loaded
    UnloadActor(ActorId);
    
    // Remove cross-references
    if (TSet<FString>* References = ActorCrossReferences.Find(ActorId))
    {
        for (const FString& ReferencedActorId : *References)
        {
            if (TSet<FString>* ReferencedBy = ActorReferencedBy.Find(ReferencedActorId))
            {
                ReferencedBy->Remove(ActorId);
            }
        }
    }
    
    // Remove references to this actor
    if (TSet<FString>* ReferencedBy = ActorReferencedBy.Find(ActorId))
    {
        for (const FString& ReferencingActorId : *ReferencedBy)
        {
            if (TSet<FString>* References = ActorCrossReferences.Find(ReferencingActorId))
            {
                References->Remove(ActorId);
            }
        }
    }
    
    // Remove from cell mapping
    if (FString* CellId = ActorToCellMap.Find(ActorId))
    {
        if (TSet<FString>* CellActors = CellToActorsMap.Find(*CellId))
        {
            CellActors->Remove(ActorId);
        }
        ActorToCellMap.Remove(ActorId);
    }
    
    // Remove from collections
    ActorDescriptors.Remove(ActorId);
    ActorReferences.Remove(ActorId);
    ActorCrossReferences.Remove(ActorId);
    ActorReferencedBy.Remove(ActorId);
    
    OnActorRemovedInternal(ActorId);
    
    AURACRON_WP_LOG_INFO(TEXT("Actor removed: %s"), *ActorId);
    
    return true;
}

bool UAuracronWorldPartitionActorManager::MoveActor(const FString& ActorId, const FVector& NewLocation)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);
    
    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }
    
    FVector OldLocation = ActorDesc->Location;
    ActorDesc->Location = NewLocation;
    ActorDesc->LastAccessTime = FDateTime::Now();
    
    // Update bounds
    FVector BoundsExtent = ActorDesc->Bounds.GetExtent();
    ActorDesc->Bounds = FBox(NewLocation - BoundsExtent, NewLocation + BoundsExtent);
    
    // Update cell mapping
    UpdateActorCellMapping(ActorId);
    
    // Update actual actor if loaded
    if (TWeakObjectPtr<AActor>* ActorRef = ActorReferences.Find(ActorId))
    {
        if (AActor* Actor = ActorRef->Get())
        {
            Actor->SetActorLocation(NewLocation);
        }
    }
    
    OnActorMovedInternal(ActorId, NewLocation);
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Actor moved: %s from %s to %s"), *ActorId, *OldLocation.ToString(), *NewLocation.ToString());
    
    return true;
}

bool UAuracronWorldPartitionActorManager::SetActorRotation(const FString& ActorId, const FRotator& NewRotation)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);
    
    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }
    
    ActorDesc->Rotation = NewRotation;
    ActorDesc->LastAccessTime = FDateTime::Now();
    
    // Update actual actor if loaded
    if (TWeakObjectPtr<AActor>* ActorRef = ActorReferences.Find(ActorId))
    {
        if (AActor* Actor = ActorRef->Get())
        {
            Actor->SetActorRotation(NewRotation);
        }
    }
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Actor rotation set: %s to %s"), *ActorId, *NewRotation.ToString());
    
    return true;
}

bool UAuracronWorldPartitionActorManager::SetActorScale(const FString& ActorId, const FVector& NewScale)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);
    
    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }
    
    ActorDesc->Scale = NewScale;
    ActorDesc->LastAccessTime = FDateTime::Now();
    
    // Update actual actor if loaded
    if (TWeakObjectPtr<AActor>* ActorRef = ActorReferences.Find(ActorId))
    {
        if (AActor* Actor = ActorRef->Get())
        {
            Actor->SetActorScale3D(NewScale);
        }
    }
    
    AURACRON_WP_LOG_VERBOSE(TEXT("Actor scale set: %s to %s"), *ActorId, *NewScale.ToString());
    
    return true;
}

FAuracronActorDescriptor UAuracronWorldPartitionActorManager::GetActorDescriptor(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);
    
    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (ActorDesc)
    {
        return *ActorDesc;
    }
    
    return FAuracronActorDescriptor();
}

TArray<FAuracronActorDescriptor> UAuracronWorldPartitionActorManager::GetAllActors() const
{
    FScopeLock Lock(&ActorLock);
    
    TArray<FAuracronActorDescriptor> AllActors;
    ActorDescriptors.GenerateValueArray(AllActors);
    
    return AllActors;
}

TArray<FString> UAuracronWorldPartitionActorManager::GetActorIds() const
{
    FScopeLock Lock(&ActorLock);
    
    TArray<FString> ActorIds;
    ActorDescriptors.GenerateKeyArray(ActorIds);
    
    return ActorIds;
}

bool UAuracronWorldPartitionActorManager::DoesActorExist(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);
    return ActorDescriptors.Contains(ActorId);
}

AActor* UAuracronWorldPartitionActorManager::GetActorReference(const FString& ActorId) const
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get actor reference: Manager not initialized"));
        return nullptr;
    }
    
    if (ActorId.IsEmpty())
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get actor reference: ActorId is empty"));
        return nullptr;
    }
    
    FScopeLock Lock(&ActorLock);
    
    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Actor not found: %s"), *ActorId);
        return nullptr;
    }
    
    // Check if actor reference is valid
    const TWeakObjectPtr<AActor>* ActorRef = ActorReferences.Find(ActorId);
    if (ActorRef && ActorRef->IsValid())
    {
        AActor* Actor = ActorRef->Get();
        if (IsValid(Actor))
        {
            // Update last access time
            const_cast<FAuracronActorDescriptor*>(ActorDesc)->LastAccessTime = FDateTime::Now();
            return Actor;
        }
        else
        {
            // Clear invalid reference
            const_cast<TMap<FString, TWeakObjectPtr<AActor>>*>(&ActorReferences)->Remove(ActorId);
            AURACRON_WP_LOG_WARNING(TEXT("Actor reference is invalid for %s"), *ActorId);
        }
    }
    
    // Try to find actor in world if not cached
    if (ManagedWorld.IsValid())
    {
        UWorld* World = ManagedWorld.Get();
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (IsValid(Actor))
            {
                // Check if this actor matches our descriptor
                FString ActorName = Actor->GetName();
                if (ActorName.Contains(ActorId) || ActorDesc->ActorName == ActorName)
                {
                    // Cache the reference
                    const_cast<TMap<FString, TWeakObjectPtr<AActor>>*>(&ActorReferences)->Add(ActorId, Actor);
                    const_cast<FAuracronActorDescriptor*>(ActorDesc)->LastAccessTime = FDateTime::Now();
                    return Actor;
                }
            }
        }
    }
    
    return nullptr;
}

bool UAuracronWorldPartitionActorManager::LoadActor(const FString& ActorId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);

    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }

    if (ActorDesc->StreamingState == EAuracronActorStreamingState::Loaded)
    {
        return true; // Already loaded
    }

    // Set loading state
    ActorDesc->StreamingState = EAuracronActorStreamingState::Loading;
    ActorDesc->LastAccessTime = FDateTime::Now();

    // Simulate actor loading
    // In a real implementation, this would create the actual actor
    bool bLoadSuccess = (FMath::RandRange(0, 100) > 5); // 95% success rate

    if (bLoadSuccess)
    {
        ActorDesc->StreamingState = EAuracronActorStreamingState::Loaded;
        ActorDesc->LifecycleState = EAuracronActorLifecycleState::Active;

        // In a real implementation, create the actual actor here
        // For now, we'll just simulate it

        AURACRON_WP_LOG_VERBOSE(TEXT("Actor loaded: %s"), *ActorId);

        // Broadcast event
        OnActorStreamingStateChanged.Broadcast(ActorId, EAuracronActorStreamingState::Loaded);

        return true;
    }
    else
    {
        ActorDesc->StreamingState = EAuracronActorStreamingState::Failed;

        // Update statistics
        {
            FScopeLock StatsLock(&StatisticsLock);
            Statistics.FailedOperations++;
        }

        AURACRON_WP_LOG_ERROR(TEXT("Failed to load actor: %s"), *ActorId);
        return false;
    }
}

bool UAuracronWorldPartitionActorManager::UnloadActor(const FString& ActorId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);

    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }

    if (ActorDesc->StreamingState == EAuracronActorStreamingState::Unloaded)
    {
        return true; // Already unloaded
    }

    // Set unloading state
    ActorDesc->StreamingState = EAuracronActorStreamingState::Unloading;

    // Remove actor reference
    if (TWeakObjectPtr<AActor>* ActorRef = ActorReferences.Find(ActorId))
    {
        if (AActor* Actor = ActorRef->Get())
        {
            // In a real implementation, properly destroy the actor
            Actor->Destroy();
        }
        ActorReferences.Remove(ActorId);
    }

    ActorDesc->StreamingState = EAuracronActorStreamingState::Unloaded;
    ActorDesc->LifecycleState = EAuracronActorLifecycleState::Inactive;

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor unloaded: %s"), *ActorId);

    // Broadcast event
    OnActorStreamingStateChanged.Broadcast(ActorId, EAuracronActorStreamingState::Unloaded);

    return true;
}

EAuracronActorStreamingState UAuracronWorldPartitionActorManager::GetActorStreamingState(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);

    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (ActorDesc)
    {
        return ActorDesc->StreamingState;
    }

    return EAuracronActorStreamingState::Unloaded;
}

TArray<FString> UAuracronWorldPartitionActorManager::GetLoadedActors() const
{
    FScopeLock Lock(&ActorLock);

    TArray<FString> LoadedActors;

    for (const auto& ActorPair : ActorDescriptors)
    {
        if (ActorPair.Value.StreamingState == EAuracronActorStreamingState::Loaded)
        {
            LoadedActors.Add(ActorPair.Key);
        }
    }

    return LoadedActors;
}

TArray<FString> UAuracronWorldPartitionActorManager::GetStreamingActors() const
{
    FScopeLock Lock(&ActorLock);

    TArray<FString> StreamingActors;

    for (const auto& ActorPair : ActorDescriptors)
    {
        if (ActorPair.Value.StreamingState == EAuracronActorStreamingState::Loading ||
            ActorPair.Value.StreamingState == EAuracronActorStreamingState::Unloading)
        {
            StreamingActors.Add(ActorPair.Key);
        }
    }

    return StreamingActors;
}

FAuracronSpatialQueryResult UAuracronWorldPartitionActorManager::ExecuteSpatialQuery(const FAuracronSpatialQueryParameters& QueryParams) const
{
    FAuracronSpatialQueryResult Result;
    FDateTime StartTime = FDateTime::Now();

    try
    {
        FScopeLock Lock(&ActorLock);

        for (const auto& ActorPair : ActorDescriptors)
        {
            const FAuracronActorDescriptor& ActorDesc = ActorPair.Value;

            // Skip unloaded actors if not requested
            if (!QueryParams.bIncludeUnloadedActors &&
                ActorDesc.StreamingState != EAuracronActorStreamingState::Loaded)
            {
                continue;
            }

            // Apply class filter
            if (QueryParams.FilterClasses.Num() > 0 &&
                !QueryParams.FilterClasses.Contains(ActorDesc.ActorClass))
            {
                continue;
            }

            // Apply tag filter
            if (QueryParams.FilterTags.Num() > 0)
            {
                bool bHasMatchingTag = false;
                for (const FString& Tag : QueryParams.FilterTags)
                {
                    if (ActorDesc.Tags.Contains(Tag))
                    {
                        bHasMatchingTag = true;
                        break;
                    }
                }
                if (!bHasMatchingTag)
                {
                    continue;
                }
            }

            // Check spatial criteria
            if (IsActorInQueryRange(ActorDesc, QueryParams))
            {
                Result.FoundActors.Add(ActorDesc);
                Result.ActorIds.Add(ActorDesc.ActorId);

                // Check result limit
                if (Result.FoundActors.Num() >= QueryParams.MaxResults)
                {
                    break;
                }
            }
        }

        Result.TotalResults = Result.FoundActors.Num();
        Result.bQuerySuccessful = true;
    }
    catch (...)
    {
        Result.bQuerySuccessful = false;
        Result.ErrorMessage = TEXT("Spatial query execution failed");
    }

    FDateTime EndTime = FDateTime::Now();
    Result.QueryTime = (EndTime - StartTime).GetTotalSeconds();

    // Update statistics
    {
        FScopeLock StatsLock(&StatisticsLock);
        Statistics.SpatialQueries++;
        Statistics.AverageQueryTime = (Statistics.AverageQueryTime + Result.QueryTime) / 2.0f;
    }

    return Result;
}

TArray<FAuracronActorDescriptor> UAuracronWorldPartitionActorManager::GetActorsInRadius(const FVector& Location, float Radius) const
{
    FAuracronSpatialQueryParameters QueryParams;
    QueryParams.QueryType = EAuracronSpatialQueryType::Sphere;
    QueryParams.QueryLocation = Location;
    QueryParams.QueryRadius = Radius;

    FAuracronSpatialQueryResult Result = ExecuteSpatialQuery(QueryParams);
    return Result.FoundActors;
}

TArray<FAuracronActorDescriptor> UAuracronWorldPartitionActorManager::GetActorsInBox(const FBox& Box) const
{
    FAuracronSpatialQueryParameters QueryParams;
    QueryParams.QueryType = EAuracronSpatialQueryType::Box;
    QueryParams.QueryBox = Box;

    FAuracronSpatialQueryResult Result = ExecuteSpatialQuery(QueryParams);
    return Result.FoundActors;
}

TArray<FAuracronActorDescriptor> UAuracronWorldPartitionActorManager::GetActorsByClass(const FString& ActorClass) const
{
    FScopeLock Lock(&ActorLock);

    TArray<FAuracronActorDescriptor> FilteredActors;

    for (const auto& ActorPair : ActorDescriptors)
    {
        if (ActorPair.Value.ActorClass == ActorClass)
        {
            FilteredActors.Add(ActorPair.Value);
        }
    }

    return FilteredActors;
}

TArray<FAuracronActorDescriptor> UAuracronWorldPartitionActorManager::GetActorsByTag(const FString& Tag) const
{
    FScopeLock Lock(&ActorLock);

    TArray<FAuracronActorDescriptor> FilteredActors;

    for (const auto& ActorPair : ActorDescriptors)
    {
        if (ActorPair.Value.Tags.Contains(Tag))
        {
            FilteredActors.Add(ActorPair.Value);
        }
    }

    return FilteredActors;
}

TArray<FString> UAuracronWorldPartitionActorManager::GetActorsInCell(const FString& CellId) const
{
    FScopeLock Lock(&ActorLock);

    const TSet<FString>* CellActors = CellToActorsMap.Find(CellId);
    if (CellActors)
    {
        return CellActors->Array();
    }

    return TArray<FString>();
}

FString UAuracronWorldPartitionActorManager::GetActorCell(const FString& ActorId) const
{
    if (!bIsInitialized)
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get actor cell: Manager not initialized"));
        return FString();
    }
    
    if (ActorId.IsEmpty())
    {
        AURACRON_WP_LOG_WARNING(TEXT("Cannot get actor cell: ActorId is empty"));
        return FString();
    }
    
    FScopeLock Lock(&ActorLock);
    
    // First check our direct mapping
    const FString* CellId = ActorToCellMap.Find(ActorId);
    if (CellId && !CellId->IsEmpty())
    {
        return *CellId;
    }
    
    // If not found in mapping, try to calculate from actor location
    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (ActorDesc)
    {
        // Get grid manager to calculate cell from location
        if (UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance())
        {
            if (GridManager->IsInitialized())
            {
                FIntVector GridCoords = GridManager->WorldToGridCoordinates(ActorDesc->Location);
                FString CalculatedCellId = GridManager->CoordinatesToCellId(GridCoords, 0);
                
                if (!CalculatedCellId.IsEmpty())
                {
                    // Update our mapping for future queries
                    const_cast<TMap<FString, FString>*>(&ActorToCellMap)->Add(ActorId, CalculatedCellId);
                    
                    AURACRON_WP_LOG_VERBOSE(TEXT("Calculated cell %s for actor %s at location %s"), 
                        *CalculatedCellId, *ActorId, *ActorDesc->Location.ToString());
                    
                    return CalculatedCellId;
                }
            }
        }
        
        AURACRON_WP_LOG_WARNING(TEXT("Could not determine cell for actor %s at location %s"), 
            *ActorId, *ActorDesc->Location.ToString());
    }
    else
    {
        AURACRON_WP_LOG_WARNING(TEXT("Actor descriptor not found for %s"), *ActorId);
    }
    
    return FString();
}

bool UAuracronWorldPartitionActorManager::MoveActorToCell(const FString& ActorId, const FString& CellId)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);

    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }

    // Remove from old cell
    if (FString* OldCellId = ActorToCellMap.Find(ActorId))
    {
        if (TSet<FString>* OldCellActors = CellToActorsMap.Find(*OldCellId))
        {
            OldCellActors->Remove(ActorId);
        }
    }

    // Add to new cell
    ActorToCellMap.Add(ActorId, CellId);
    TSet<FString>& NewCellActors = CellToActorsMap.FindOrAdd(CellId);
    NewCellActors.Add(ActorId);

    ActorDesc->CellId = CellId;
    ActorDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor moved to cell: %s -> %s"), *ActorId, *CellId);

    return true;
}

bool UAuracronWorldPartitionActorManager::AddActorReference(const FString& ActorId, const FString& ReferencedActorId)
{
    if (!bIsInitialized || ActorId == ReferencedActorId)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);

    if (!ActorDescriptors.Contains(ActorId) || !ActorDescriptors.Contains(ReferencedActorId))
    {
        return false;
    }

    // Add reference
    TSet<FString>& References = ActorCrossReferences.FindOrAdd(ActorId);
    References.Add(ReferencedActorId);

    // Add reverse reference
    TSet<FString>& ReferencedBy = ActorReferencedBy.FindOrAdd(ReferencedActorId);
    ReferencedBy.Add(ActorId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor reference added: %s -> %s"), *ActorId, *ReferencedActorId);

    return true;
}

bool UAuracronWorldPartitionActorManager::RemoveActorReference(const FString& ActorId, const FString& ReferencedActorId)
{
    FScopeLock Lock(&ActorLock);

    // Remove reference
    if (TSet<FString>* References = ActorCrossReferences.Find(ActorId))
    {
        References->Remove(ReferencedActorId);
    }

    // Remove reverse reference
    if (TSet<FString>* ReferencedBy = ActorReferencedBy.Find(ReferencedActorId))
    {
        ReferencedBy->Remove(ActorId);
    }

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor reference removed: %s -> %s"), *ActorId, *ReferencedActorId);

    return true;
}

TArray<FString> UAuracronWorldPartitionActorManager::GetActorReferences(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);

    const TSet<FString>* References = ActorCrossReferences.Find(ActorId);
    if (References)
    {
        return References->Array();
    }

    return TArray<FString>();
}

TArray<FString> UAuracronWorldPartitionActorManager::GetActorReferencedBy(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);

    const TSet<FString>* ReferencedBy = ActorReferencedBy.Find(ActorId);
    if (ReferencedBy)
    {
        return ReferencedBy->Array();
    }

    return TArray<FString>();
}

bool UAuracronWorldPartitionActorManager::SetActorLifecycleState(const FString& ActorId, EAuracronActorLifecycleState NewState)
{
    if (!bIsInitialized)
    {
        return false;
    }

    FScopeLock Lock(&ActorLock);

    FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return false;
    }

    EAuracronActorLifecycleState OldState = ActorDesc->LifecycleState;
    ActorDesc->LifecycleState = NewState;
    ActorDesc->LastAccessTime = FDateTime::Now();

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor lifecycle state changed: %s (%s -> %s)"),
                           *ActorId,
                           *UEnum::GetValueAsString(OldState),
                           *UEnum::GetValueAsString(NewState));

    return true;
}

EAuracronActorLifecycleState UAuracronWorldPartitionActorManager::GetActorLifecycleState(const FString& ActorId) const
{
    FScopeLock Lock(&ActorLock);

    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (ActorDesc)
    {
        return ActorDesc->LifecycleState;
    }

    return EAuracronActorLifecycleState::Destroyed;
}

void UAuracronWorldPartitionActorManager::UpdateActorLifecycles()
{
    FScopeLock Lock(&ActorLock);

    // Update lifecycle states based on streaming states and other factors
    for (auto& ActorPair : ActorDescriptors)
    {
        FAuracronActorDescriptor& ActorDesc = ActorPair.Value;

        // Update lifecycle based on streaming state
        switch (ActorDesc.StreamingState)
        {
            case EAuracronActorStreamingState::Loaded:
                if (ActorDesc.LifecycleState == EAuracronActorLifecycleState::Created ||
                    ActorDesc.LifecycleState == EAuracronActorLifecycleState::Inactive)
                {
                    ActorDesc.LifecycleState = EAuracronActorLifecycleState::Active;
                }
                break;

            case EAuracronActorStreamingState::Unloaded:
                if (ActorDesc.LifecycleState == EAuracronActorLifecycleState::Active)
                {
                    ActorDesc.LifecycleState = EAuracronActorLifecycleState::Inactive;
                }
                break;

            case EAuracronActorStreamingState::Failed:
                ActorDesc.LifecycleState = EAuracronActorLifecycleState::Inactive;
                break;
        }
    }
}

void UAuracronWorldPartitionActorManager::SetConfiguration(const FAuracronActorManagementConfiguration& InConfiguration)
{
    Configuration = InConfiguration;
    ValidateConfiguration();

    AURACRON_WP_LOG_INFO(TEXT("Actor management configuration updated"));
}

FAuracronActorManagementConfiguration UAuracronWorldPartitionActorManager::GetConfiguration() const
{
    return Configuration;
}

FAuracronActorStatistics UAuracronWorldPartitionActorManager::GetActorStatistics() const
{
    FScopeLock Lock(&StatisticsLock);

    FAuracronActorStatistics CurrentStats = Statistics;
    CurrentStats.UpdateCalculatedFields();

    return CurrentStats;
}

void UAuracronWorldPartitionActorManager::ResetStatistics()
{
    FScopeLock Lock(&StatisticsLock);
    Statistics = FAuracronActorStatistics();

    AURACRON_WP_LOG_INFO(TEXT("Actor statistics reset"));
}

int32 UAuracronWorldPartitionActorManager::GetTotalActorCount() const
{
    FScopeLock Lock(&ActorLock);
    return ActorDescriptors.Num();
}

int32 UAuracronWorldPartitionActorManager::GetLoadedActorCount() const
{
    FScopeLock Lock(&ActorLock);

    int32 LoadedCount = 0;
    for (const auto& ActorPair : ActorDescriptors)
    {
        if (ActorPair.Value.StreamingState == EAuracronActorStreamingState::Loaded)
        {
            LoadedCount++;
        }
    }

    return LoadedCount;
}

float UAuracronWorldPartitionActorManager::GetTotalMemoryUsage() const
{
    FScopeLock Lock(&StatisticsLock);
    return Statistics.TotalMemoryUsageMB;
}

void UAuracronWorldPartitionActorManager::EnableActorDebug(bool bEnabled)
{
    Configuration.bEnableActorDebug = bEnabled;

    AURACRON_WP_LOG_INFO(TEXT("Actor debug %s"), bEnabled ? TEXT("enabled") : TEXT("disabled"));
}

bool UAuracronWorldPartitionActorManager::IsActorDebugEnabled() const
{
    return Configuration.bEnableActorDebug;
}

void UAuracronWorldPartitionActorManager::LogActorState() const
{
    FScopeLock Lock(&ActorLock);

    int32 TotalCount = ActorDescriptors.Num();
    int32 LoadedCount = GetLoadedActorCount();
    int32 StreamingCount = GetStreamingActors().Num();

    AURACRON_WP_LOG_INFO(TEXT("Actor State: %d total, %d loaded, %d streaming"), TotalCount, LoadedCount, StreamingCount);

    FAuracronActorStatistics CurrentStats = GetActorStatistics();
    AURACRON_WP_LOG_INFO(TEXT("Statistics: %.1fMB memory, %.2f efficiency, %d queries"),
                         CurrentStats.TotalMemoryUsageMB, CurrentStats.ActorEfficiency, CurrentStats.SpatialQueries);
}

void UAuracronWorldPartitionActorManager::DrawDebugActorInfo(UWorld* World) const
{
    if (!Configuration.bEnableActorDebug || !World)
    {
        return;
    }

    FScopeLock Lock(&ActorLock);

    // Draw debug information for loaded actors
    for (const auto& ActorPair : ActorDescriptors)
    {
        const FAuracronActorDescriptor& ActorDesc = ActorPair.Value;

        if (ActorDesc.StreamingState == EAuracronActorStreamingState::Loaded)
        {
            FColor DebugColor = FColor::Green;

            // Color based on lifecycle state
            switch (ActorDesc.LifecycleState)
            {
                case EAuracronActorLifecycleState::Active:
                    DebugColor = FColor::Green;
                    break;
                case EAuracronActorLifecycleState::Inactive:
                    DebugColor = FColor::Yellow;
                    break;
                case EAuracronActorLifecycleState::Destroying:
                    DebugColor = FColor::Red;
                    break;
                default:
                    DebugColor = FColor::White;
                    break;
            }

            // Draw actor bounds
            DrawDebugBox(World, ActorDesc.Bounds.GetCenter(), ActorDesc.Bounds.GetExtent(),
                        DebugColor, false, -1.0f, 0, 2.0f);

            // Draw actor info text
            FString DebugText = FString::Printf(TEXT("%s\n%s"), *ActorDesc.ActorName, *ActorDesc.ActorClass);
            DrawDebugString(World, ActorDesc.Location + FVector(0, 0, 100), DebugText,
                           nullptr, DebugColor, -1.0f, true);
        }
    }
}

void UAuracronWorldPartitionActorManager::UpdateStatistics()
{
    FScopeLock Lock(&StatisticsLock);

    Statistics.TotalActors = ActorDescriptors.Num();
    Statistics.LoadedActors = 0;
    Statistics.StreamingActors = 0;
    Statistics.ActiveActors = 0;
    Statistics.TotalMemoryUsageMB = 0.0f;

    for (const auto& ActorPair : ActorDescriptors)
    {
        const FAuracronActorDescriptor& ActorDesc = ActorPair.Value;

        switch (ActorDesc.StreamingState)
        {
            case EAuracronActorStreamingState::Loaded:
                Statistics.LoadedActors++;
                Statistics.TotalMemoryUsageMB += ActorDesc.MemoryUsageMB;
                break;
            case EAuracronActorStreamingState::Loading:
            case EAuracronActorStreamingState::Unloading:
                Statistics.StreamingActors++;
                break;
        }

        if (ActorDesc.LifecycleState == EAuracronActorLifecycleState::Active)
        {
            Statistics.ActiveActors++;
        }
    }

    Statistics.UpdateCalculatedFields();
}

FString UAuracronWorldPartitionActorManager::GenerateActorId(const FString& ActorClass) const
{
    static int32 ActorCounter = 0;
    ActorCounter++;

    FString CleanClass = ActorClass.Replace(TEXT("::"), TEXT("_"));
    CleanClass = CleanClass.Replace(TEXT("/"), TEXT("_"));

    return FString::Printf(TEXT("Actor_%s_%d_%lld"), *CleanClass, ActorCounter, FDateTime::Now().GetTicks());
}

bool UAuracronWorldPartitionActorManager::ValidateActorId(const FString& ActorId) const
{
    return !ActorId.IsEmpty() && ActorId.StartsWith(TEXT("Actor_"));
}

void UAuracronWorldPartitionActorManager::OnActorPlacedInternal(const FString& ActorId, const FVector& Location)
{
    OnActorPlaced.Broadcast(ActorId, Location);

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor placed event: %s at %s"), *ActorId, *Location.ToString());
}

void UAuracronWorldPartitionActorManager::OnActorRemovedInternal(const FString& ActorId)
{
    OnActorRemoved.Broadcast(ActorId);

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor removed event: %s"), *ActorId);
}

void UAuracronWorldPartitionActorManager::OnActorMovedInternal(const FString& ActorId, const FVector& NewLocation)
{
    OnActorMoved.Broadcast(ActorId, NewLocation);

    AURACRON_WP_LOG_VERBOSE(TEXT("Actor moved event: %s to %s"), *ActorId, *NewLocation.ToString());
}

void UAuracronWorldPartitionActorManager::ValidateConfiguration()
{
    // Validate actor limits
    Configuration.MaxActorsPerCell = FMath::Max(1, Configuration.MaxActorsPerCell);
    Configuration.MaxConcurrentActorOperations = FMath::Max(1, Configuration.MaxConcurrentActorOperations);

    // Validate distances
    Configuration.ActorStreamingDistance = FMath::Max(0.0f, Configuration.ActorStreamingDistance);
    Configuration.ActorUnloadingDistance = FMath::Max(Configuration.ActorStreamingDistance, Configuration.ActorUnloadingDistance);

    // Validate memory settings
    Configuration.MaxActorMemoryUsageMB = FMath::Max(100.0f, Configuration.MaxActorMemoryUsageMB);

    // Validate spatial query settings
    Configuration.SpatialQueryRadius = FMath::Max(0.0f, Configuration.SpatialQueryRadius);
    Configuration.MaxSpatialQueryResults = FMath::Max(1, Configuration.MaxSpatialQueryResults);
}

void UAuracronWorldPartitionActorManager::UpdateActorCellMapping(const FString& ActorId)
{
    const FAuracronActorDescriptor* ActorDesc = ActorDescriptors.Find(ActorId);
    if (!ActorDesc)
    {
        return;
    }

    // Get grid manager to determine cell
    UAuracronWorldPartitionGridManager* GridManager = UAuracronWorldPartitionGridManager::GetInstance();
    if (!GridManager || !GridManager->IsInitialized())
    {
        return;
    }

    // Get cell at actor location
    FAuracronGridCell Cell = GridManager->GetCellAtLocation(ActorDesc->Location);
    if (!Cell.CellId.IsEmpty())
    {
        MoveActorToCell(ActorId, Cell.CellId);
    }
}

bool UAuracronWorldPartitionActorManager::IsActorInQueryRange(const FAuracronActorDescriptor& Actor, const FAuracronSpatialQueryParameters& QueryParams) const
{
    switch (QueryParams.QueryType)
    {
        case EAuracronSpatialQueryType::Point:
            return Actor.Location.Equals(QueryParams.QueryLocation, 1.0f); // 1cm tolerance

        case EAuracronSpatialQueryType::Sphere:
            return FVector::Dist(Actor.Location, QueryParams.QueryLocation) <= QueryParams.QueryRadius;

        case EAuracronSpatialQueryType::Box:
            return QueryParams.QueryBox.IsInside(Actor.Location);

        case EAuracronSpatialQueryType::Cylinder:
        {
            FVector2D ActorPos2D(Actor.Location.X, Actor.Location.Y);
            FVector2D QueryPos2D(QueryParams.QueryLocation.X, QueryParams.QueryLocation.Y);
            float Distance2D = FVector2D::Distance(ActorPos2D, QueryPos2D);

            return Distance2D <= QueryParams.QueryRadius &&
                   FMath::Abs(Actor.Location.Z - QueryParams.QueryLocation.Z) <= QueryParams.QueryDistance;
        }

        case EAuracronSpatialQueryType::Cone:
        {
            FVector ToActor = Actor.Location - QueryParams.QueryLocation;
            float Distance = ToActor.Size();

            if (Distance > QueryParams.QueryDistance)
            {
                return false;
            }

            FVector NormalizedToActor = ToActor.GetSafeNormal();
            FVector NormalizedDirection = QueryParams.QueryDirection.GetSafeNormal();

            float DotProduct = FVector::DotProduct(NormalizedToActor, NormalizedDirection);
            float AngleRadians = FMath::Acos(DotProduct);
            float AngleDegrees = FMath::RadiansToDegrees(AngleRadians);

            return AngleDegrees <= QueryParams.QueryAngle;
        }

        case EAuracronSpatialQueryType::Frustum:
            // Real frustum culling implementation
            return IsActorInFrustum(Actor, QueryParams);

        default:
            return false;
    }
}

// === Helper Functions Implementation ===

FVector UAuracronWorldPartitionActorManager::CalculateActorBounds(UClass* ActorClass, const FVector& Location)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronWorldPartitionActorManager::CalculateActorBounds);

    if (!ActorClass)
    {
        return FVector(100.0f); // Default bounds
    }

    // Get bounds from class default object
    if (AActor* CDO = Cast<AActor>(ActorClass->GetDefaultObject()))
    {
        FBox ActorBounds = CDO->GetComponentsBoundingBox(true);
        if (ActorBounds.IsValid)
        {
            return ActorBounds.GetExtent();
        }
    }

    // Fallback: estimate based on actor type
    if (ActorClass->IsChildOf<AStaticMeshActor>())
    {
        return FVector(200.0f, 200.0f, 200.0f);
    }
    else if (ActorClass->IsChildOf<APawn>())
    {
        return FVector(100.0f, 100.0f, 200.0f);
    }
    else if (ActorClass->IsChildOf<ALight>())
    {
        return FVector(50.0f, 50.0f, 50.0f);
    }

    return FVector(100.0f); // Default
}

bool UAuracronWorldPartitionActorManager::IsActorInFrustum(const FAuracronActorDescriptor& Actor, const FAuracronSpatialQueryParams& QueryParams)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(UAuracronWorldPartitionActorManager::IsActorInFrustum);

    // Real frustum culling using UE5.6 frustum math
    FVector CameraLocation = QueryParams.QueryLocation;
    FVector CameraDirection = QueryParams.QueryDirection.GetSafeNormal();
    float FOV = QueryParams.QueryAngle; // Field of view in degrees
    float NearPlane = 10.0f;
    float FarPlane = QueryParams.QueryDistance;

    // Calculate frustum planes
    FMatrix ViewMatrix = FLookAtMatrix(CameraLocation, CameraLocation + CameraDirection, FVector::UpVector);
    FMatrix ProjectionMatrix = FPerspectiveMatrix(FMath::DegreesToRadians(FOV), 1.0f, NearPlane, FarPlane);
    FMatrix ViewProjectionMatrix = ViewMatrix * ProjectionMatrix;

    // Extract frustum planes from view-projection matrix
    FPlane FrustumPlanes[6];
    ViewProjectionMatrix.GetFrustumNearPlane(FrustumPlanes[0]);
    ViewProjectionMatrix.GetFrustumFarPlane(FrustumPlanes[1]);
    ViewProjectionMatrix.GetFrustumLeftPlane(FrustumPlanes[2]);
    ViewProjectionMatrix.GetFrustumRightPlane(FrustumPlanes[3]);
    ViewProjectionMatrix.GetFrustumTopPlane(FrustumPlanes[4]);
    ViewProjectionMatrix.GetFrustumBottomPlane(FrustumPlanes[5]);

    // Test actor bounds against frustum planes
    FBox ActorBounds = Actor.Bounds;

    for (int32 PlaneIndex = 0; PlaneIndex < 6; PlaneIndex++)
    {
        const FPlane& Plane = FrustumPlanes[PlaneIndex];

        // Get the positive vertex (farthest point in the direction of the plane normal)
        FVector PositiveVertex = ActorBounds.Min;
        if (Plane.X >= 0) PositiveVertex.X = ActorBounds.Max.X;
        if (Plane.Y >= 0) PositiveVertex.Y = ActorBounds.Max.Y;
        if (Plane.Z >= 0) PositiveVertex.Z = ActorBounds.Max.Z;

        // If the positive vertex is behind the plane, the box is completely outside
        if (Plane.PlaneDot(PositiveVertex) < 0)
        {
            return false;
        }
    }

    return true; // Actor is inside or intersecting the frustum
}
