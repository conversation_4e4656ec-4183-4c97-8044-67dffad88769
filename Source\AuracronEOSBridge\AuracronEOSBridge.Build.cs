﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Epic Online Services Bridge Build Configuration
using UnrealBuildTool;
public class AuracronEOSBridge : ModuleRules
{
    public AuracronEOSBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine","OnlineSubsystemUtils","EOSShared","GameplayAbilities",
                "GameplayTags",
                "GameplayTasks",
                "ModularGameplay",
                "NetCore",
                "ReplicationGraph",
                "Sockets",
                "Networking",
                "EnhancedInput",
                "InputCore",
                "UMG",
                "Slate",
                "SlateCore","DeveloperSettings",
                "EngineSettings"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "Json",
                "SessionServices",
                "PlatformCrypto",
                "RSA",
                "Localization"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "BlueprintGraph",
                    "CommonUIEditor",
                    "EditorStyle",
                    "EditorWidgets",
                    "Kismet",
                    "KismetCompiler",
                    "PropertyEditor",
                    "ToolMenus",
                    "UMGEditor",
                    "UnrealEd"
                }
            );
        }
        DynamicallyLoadedModuleNames.AddRange(
            new string[]
            {}
        );
        // UE 5.6 specific features
        PublicDefinitions.Add("AURACRON_UE56_FEATURES=1");
        PublicDefinitions.Add("WITH_EOS=1");
        PublicDefinitions.Add("WITH_EOS_SDK=1");
        PublicDefinitions.Add("WITH_ONLINE_SUBSYSTEM_EOS=1");
        PublicDefinitions.Add("WITH_EOS_VOICE=1");
        PublicDefinitions.Add("WITH_EOS_SESSIONS=1");
        PublicDefinitions.Add("WITH_EOS_ACHIEVEMENTS=1");
        PublicDefinitions.Add("WITH_EOS_LEADERBOARDS=1");
        PublicDefinitions.Add("WITH_EOS_STATS=1");
        PublicDefinitions.Add("WITH_EOS_FRIENDS=1");
        PublicDefinitions.Add("WITH_EOS_PRESENCE=1");
        // EOS features
        PublicDefinitions.Add("AURACRON_EOS_AUTHENTICATION=1");
        PublicDefinitions.Add("AURACRON_EOS_MATCHMAKING=1");
        PublicDefinitions.Add("AURACRON_EOS_LOBBIES=1");
        PublicDefinitions.Add("AURACRON_EOS_SESSIONS=1");
        PublicDefinitions.Add("AURACRON_EOS_VOICE_CHAT=1");
        PublicDefinitions.Add("AURACRON_EOS_ACHIEVEMENTS=1");
        PublicDefinitions.Add("AURACRON_EOS_LEADERBOARDS=1");
        PublicDefinitions.Add("AURACRON_EOS_PLAYER_DATA=1");
        PublicDefinitions.Add("AURACRON_EOS_FRIENDS=1");
        PublicDefinitions.Add("AURACRON_EOS_PRESENCE=1");
        PublicDefinitions.Add("AURACRON_EOS_METRICS=1");
        PublicDefinitions.Add("AURACRON_EOS_ANTI_CHEAT=1");
        PublicDefinitions.Add("AURACRON_EOS_SANCTIONS=1");
        PublicDefinitions.Add("AURACRON_EOS_REPORTS=1");
        // Cross-platform features
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM_PLAY=1");
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM_PROGRESSION=1");
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM_FRIENDS=1");
        PublicDefinitions.Add("AURACRON_CROSS_PLATFORM_VOICE=1");
        // Platform-specific configurations
        if (Target.Platform == UnrealTargetPlatform.Android || Target.Platform == UnrealTargetPlatform.IOS)
        {
            PublicDefinitions.Add("AURACRON_MOBILE_EOS=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_MOBILE_EOS=0");
        }
        // Development vs Shipping
        if (Target.Configuration == UnrealTargetConfiguration.Development || 
            Target.Configuration == UnrealTargetConfiguration.DebugGame)
        {
            PublicDefinitions.Add("AURACRON_EOS_DEBUG=1");
            PublicDefinitions.Add("AURACRON_EOS_LOGGING=1");
        }
        else
        {
            PublicDefinitions.Add("AURACRON_EOS_DEBUG=0");
            PublicDefinitions.Add("AURACRON_EOS_LOGGING=0");
        }
        // Security and compliance
        PublicDefinitions.Add("AURACRON_EOS_ENCRYPTION=1");
        PublicDefinitions.Add("AURACRON_EOS_GDPR=1");
        PublicDefinitions.Add("AURACRON_EOS_COPPA=1");
    }
}



