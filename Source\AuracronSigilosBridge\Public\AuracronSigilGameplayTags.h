/**
 * Auracron Sigil System - Native GameplayTags
 * 
 * Defines all native GameplayTags for the Sigil System using UE 5.6 APIs
 * Includes tags for all 3 Sigil types, 16 subtypes, and 150 archetype combinations
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

#pragma once

#include "CoreMinimal.h"
#include "GameplayTagContainer.h"
#include "NativeGameplayTags.h"
#include "AuracronSigilosBridge.h"

/**
 * Native GameplayTags for Auracron Sigil System
 * Using UE 5.6 UE_DECLARE_GAMEPLAY_TAG_EXTERN and UE_DEFINE_GAMEPLAY_TAG macros
 */
namespace AuracronSigilTags
{
    // === Root Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Fusion);

    // === State Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State_Inactive);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State_Active);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State_Cooldown);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State_Charging);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_State_Fusion20);

    // === Aegis Sigil Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis_Primordial);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis_Cristalino);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis_Temporal);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis_Espectral);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Aegis_Absoluto);

    // === Ruin Sigil Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin_Flamejante);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin_Gelido);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin_Sombrio);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin_Corrosivo);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Ruin_Aniquilador);

    // === Vesper Sigil Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Curativo);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Energetico);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Velocidade);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Visao);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Teleporte);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Type_Vesper_Temporal);

    // === Fusion 2.0 Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Fusion_Active);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Fusion_Cooldown);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Fusion_Synergy);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Fusion_Resonance);

    // === Archetype Category Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Guardian);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Destroyer);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Assassin);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Healer);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Controller);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Archetype_Hybrid);

    // === Effect Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Shield);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Damage);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Heal);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Buff);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Debuff);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_Teleport);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Effect_TimeManipulation);

    // === Input Tags ===
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Input_Aegis);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Input_Ruin);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Input_Vesper);
    UE_DECLARE_GAMEPLAY_TAG_EXTERN(Sigil_Input_Fusion20);

    // === Utility Functions ===
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetAegisSigilTag(EAuracronAegisSigilType AegisType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetRuinSigilTag(EAuracronRuinSigilType RuinType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetVesperSigilTag(EAuracronVesperSigilType VesperType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GenerateArchetypeTag(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType);
    AURACRONSIGILOSBRIDGE_API FGameplayTagContainer GetAllSigilTags();

    // === Utility Functions ===
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetAegisSigilTag(EAuracronAegisSigilType AegisType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetRuinSigilTag(EAuracronRuinSigilType RuinType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GetVesperSigilTag(EAuracronVesperSigilType VesperType);
    AURACRONSIGILOSBRIDGE_API FGameplayTag GenerateArchetypeTag(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType);
    AURACRONSIGILOSBRIDGE_API FGameplayTagContainer GetAllSigilTags();

}

// Note: Utility function implementations are in AuracronSigilGameplayTags.cpp
// to avoid redefinition issues with the compiler
