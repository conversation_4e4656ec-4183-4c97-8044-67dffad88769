#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "GameplayTagContainer.h"
#include "AuracronHarmonyEngineBridge.h"
#include "HarmonyEngineSettings.generated.h"

/**
 * Developer Settings for Harmony Engine
 * Configure all aspects of the anti-toxicity and community healing system
 */
UCLASS(Config=Game, DefaultConfig, meta=(DisplayName="Harmony Engine Settings"))
class AURACRONHARMONYENGINEBRIDGE_API UHarmonyEngineSettings : public UDeveloperSettings
{
    GENERATED_BODY()

public:
    UHarmonyEngineSettings();

    // Core System Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Core Settings")
    bool bEnableHarmonyEngine;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Core Settings")
    bool bEnableRealTimeMonitoring;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Core Settings")
    bool bEnablePredictiveIntervention;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Core Settings")
    bool bEnableCommunityHealing;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Core Settings")
    bool bEnableMachineLearning;

    // Behavior Detection Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Behavior Detection", meta=(ClampMin="0.0", ClampMax="1.0"))
    float ToxicityDetectionThreshold;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Behavior Detection", meta=(ClampMin="0.0", ClampMax="1.0"))
    float PositivityDetectionThreshold;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Behavior Detection", meta=(ClampMin="0.0", ClampMax="1.0"))
    float FrustrationDetectionThreshold;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Behavior Detection", meta=(ClampMin="1.0"))
    float BehaviorAnalysisInterval;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Behavior Detection")
    bool bEnableAutomaticBehaviorDetection;

    // Intervention Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Intervention Settings", meta=(ClampMin="0"))
    int32 MaxConcurrentInterventions;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Intervention Settings", meta=(ClampMin="30.0"))
    float InterventionCooldownTime;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Intervention Settings", meta=(ClampMin="60.0"))
    float InterventionTimeout;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Intervention Settings")
    bool bEnableAutomaticEscalation;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Intervention Settings", meta=(ClampMin="0.0", ClampMax="1.0"))
    float EscalationThreshold;

    // Community Healing Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Community Healing", meta=(ClampMin="0"))
    int32 MaxConcurrentHealingSessions;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Community Healing", meta=(ClampMin="300.0"))
    float MaxHealingSessionDuration;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Community Healing", meta=(ClampMin="0.0", ClampMax="1.0"))
    float MinHealerSkillLevel;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Community Healing", meta=(ClampMin="1"))
    int32 MaxHealersPerSession;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Community Healing")
    bool bEnableAutomaticHealerMatching;

    // Rewards System Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Rewards System", meta=(ClampMin="0"))
    int32 MaxRewardsPerSession;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Rewards System", meta=(ClampMin="0.0"))
    float RewardCooldownTime;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Rewards System")
    bool bEnableProgressiveRewards;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Rewards System")
    bool bEnableSpecialEvents;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Rewards System", meta=(ClampMin="1.0"))
    float RewardAmplificationFactor;

    // Emotional Intelligence Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Emotional Intelligence", meta=(ClampMin="1.0"))
    float EmotionalMonitoringInterval;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Emotional Intelligence", meta=(ClampMin="60.0"))
    float EscalationDetectionWindow;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Emotional Intelligence", meta=(ClampMin="0"))
    int32 MaxSupportMessagesPerSession;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Emotional Intelligence")
    bool bEnablePredictiveEmotionalAnalysis;

    // Machine Learning Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Machine Learning", meta=(ClampMin="10"))
    int32 MinTrainingDataPoints;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Machine Learning", meta=(ClampMin="60.0"))
    float MLModelUpdateInterval;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Machine Learning", meta=(ClampMin="0.0", ClampMax="1.0"))
    float MLModelAccuracyThreshold;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Machine Learning")
    bool bEnableMLModelTraining;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Machine Learning", meta=(ClampMin="100"))
    int32 MaxTrainingDatasetSize;

    // Tier Requirements
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tier System")
    int32 BronzeToSilverRequirement;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tier System")
    int32 SilverToGoldRequirement;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tier System")
    int32 GoldToPlatinumRequirement;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tier System")
    int32 PlatinumToDiamondRequirement;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Tier System")
    int32 DiamondToLegendaryRequirement;

    // Logging and Debug Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debug Settings")
    bool bEnableVerboseLogging;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debug Settings")
    bool bEnableBehaviorDataLogging;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debug Settings")
    bool bEnableInterventionLogging;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debug Settings")
    bool bSaveDebugDataToDisk;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Debug Settings")
    FString DebugDataSavePath;

    // Performance Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta=(ClampMin="0.1"))
    float TickInterval;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta=(ClampMin="1"))
    int32 MaxPlayersPerAnalysisBatch;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance")
    bool bEnablePerformanceOptimizations;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Performance", meta=(ClampMin="100"))
    int32 MaxBehaviorHistoryEntries;

    // Network Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Network Settings")
    bool bReplicateBehaviorData;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Network Settings")
    bool bReplicateInterventions;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Network Settings")
    bool bReplicateRewards;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Network Settings", meta=(ClampMin="0.1"))
    float NetworkUpdateFrequency;

    // Integration Settings
    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Integration")
    bool bIntegrateWithGameplayAbilitySystem;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Integration")
    bool bIntegrateWithChatSystem;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Integration")
    bool bIntegrateWithUISystem;

    UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Integration")
    bool bIntegrateWithAudioSystem;

    // Validation and utility functions
    UFUNCTION(BlueprintCallable, Category = "Harmony Engine Settings")
    bool ValidateSettings();

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine Settings")
    void ResetToDefaults();

    UFUNCTION(BlueprintCallable, Category = "Harmony Engine Settings")
    void ApplySettings();

    // Static access
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Harmony Engine Settings")
    static UHarmonyEngineSettings* GetHarmonyEngineSettings();

protected:
    virtual void PostInitProperties() override;
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

private:
    void InitializeDefaultValues();
    void ValidateAndClampValues();
    void LogConfigurationStatus();
};
