﻿// AuracronQABridge.h
// AURACRON Champion Quality Assurance Bridge for UE5.6
// Exposes UE5.6 automation testing and data validation APIs to Python

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Misc/EngineVersionComparison.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Animation/AnimSequence.h"
#include "Sound/SoundBase.h"
#include "Engine/Texture2D.h"
#include "Engine/DataTable.h"
#include "Internationalization/Text.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Dom/JsonObject.h"
#include "Engine/ObjectLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Editor/EditorEngine.h"
#include "Editor/UnrealEdEngine.h"
#include "UnrealEdGlobals.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "Toolkits/AssetEditorManager.h"
#include "Framework/Application/SlateApplication.h"
#include "Widgets/SWindow.h"
#include "Engine/GameViewportClient.h"
#include "Slate/SceneViewport.h"
#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "Modules/ModuleManager.h"
#include "HAL/PlatformApplicationMisc.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "Trace/Trace.h"
#include "HAL/MemoryBase.h"
#include "HAL/PlatformMemory.h"
#include "GenericPlatform/GenericPlatformMemoryStats.h"

#include "AuracronQABridge.generated.h"

// Forward declarations
class UDataValidationManager;
class UEditorValidatorBase;
class UAutomationTestSettings;

/**
 * QA Test Types for AURACRON champion validation
 */
UENUM(BlueprintType)
enum class EAuracronQATestType : uint8
{
    VisualValidation     UMETA(DisplayName = "Visual Validation"),
    GameplayTesting      UMETA(DisplayName = "Gameplay Testing"),
    PerformanceTesting   UMETA(DisplayName = "Performance Testing"),
    BalanceVerification  UMETA(DisplayName = "Balance Verification"),
    AssetValidation      UMETA(DisplayName = "Asset Validation"),
    IntegrationTesting   UMETA(DisplayName = "Integration Testing")
};

/**
 * QA Test Results for automation testing
 */
UENUM(BlueprintType)
enum class EAuracronQATestResult : uint8
{
    Passed      UMETA(DisplayName = "Passed"),
    Failed      UMETA(DisplayName = "Failed"),
    Warning     UMETA(DisplayName = "Warning"),
    Skipped     UMETA(DisplayName = "Skipped"),
    Error       UMETA(DisplayName = "Error")
};

/**
 * QA Test Severity Levels
 */
UENUM(BlueprintType)
enum class EAuracronQASeverity : uint8
{
    Critical    UMETA(DisplayName = "Critical"),
    High        UMETA(DisplayName = "High"),
    Medium      UMETA(DisplayName = "Medium"),
    Low         UMETA(DisplayName = "Low"),
    Info        UMETA(DisplayName = "Info")
};

/**
 * QA Test Case Definition Structure
 */
USTRUCT(BlueprintType)
struct AURACRONQABRIDGE_API FAuracronQATestCase
{
    GENERATED_BODY()

    /** Unique identifier for the test case */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    FString TestID;

    /** Human-readable test name */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    FString TestName;

    /** Test description */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    FString Description;

    /** Test type classification */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    EAuracronQATestType TestType = EAuracronQATestType::AssetValidation;

    /** Test severity level */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    EAuracronQASeverity Severity = EAuracronQASeverity::Medium;

    /** Expected execution time in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    float ExpectedDuration = 1.0f;

    /** Test timeout in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    float TimeoutDuration = 30.0f;

    /** Test parameters as JSON string */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    FString Parameters;

    /** Test prerequisites */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    TArray<FString> Prerequisites;

    /** Test tags for filtering */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    TArray<FString> Tags;

    /** Whether test is enabled */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    bool bEnabled = true;

    /** Whether test runs in editor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    bool bRunInEditor = true;

    /** Whether test runs in game */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Case")
    bool bRunInGame = false;
};

/**
 * QA Test Execution Result Structure
 */
USTRUCT(BlueprintType)
struct AURACRONQABRIDGE_API FAuracronQATestExecution
{
    GENERATED_BODY()

    /** Test case that was executed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    FAuracronQATestCase TestCase;

    /** Test execution result */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    EAuracronQATestResult Result = EAuracronQATestResult::Skipped;

    /** Test execution message */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    FString Message;

    /** Test execution start time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    FDateTime StartTime;

    /** Test execution end time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    FDateTime EndTime;

    /** Actual execution duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    float ActualDuration = 0.0f;

    /** Test output logs */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    TArray<FString> OutputLogs;

    /** Test artifacts (screenshots, files, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    TArray<FString> Artifacts;

    /** Performance metrics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    TMap<FString, float> PerformanceMetrics;

    /** Memory usage statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Test Execution")
    TMap<FString, int64> MemoryStats;
};

/**
 * Performance Profiling Data Structure
 */
USTRUCT(BlueprintType)
struct AURACRONQABRIDGE_API FAuracronQAPerformanceData
{
    GENERATED_BODY()

    /** Frame rate statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageFrameRate = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MinFrameRate = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float MaxFrameRate = 0.0f;

    /** Memory usage statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int64 TotalMemoryUsed = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int64 PeakMemoryUsed = 0;

    /** CPU usage statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageCPUUsage = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float PeakCPUUsage = 0.0f;

    /** GPU usage statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AverageGPUUsage = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float PeakGPUUsage = 0.0f;

    /** Draw call statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 AverageDrawCalls = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 PeakDrawCalls = 0;

    /** Loading time statistics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LevelLoadTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float AssetLoadTime = 0.0f;
};

/**
 * Main AURACRON QA Bridge Class
 * Provides comprehensive quality assurance testing capabilities for AURACRON champions
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|QA", meta = (DisplayName = "AURACRON QA Bridge"))
class AURACRONQABRIDGE_API UAuracronQABridge : public UObject
{
    GENERATED_BODY()

public:
    UAuracronQABridge();

    // === Core Testing Framework ===

    /**
     * Initialize automation testing framework
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Framework", CallInEditor)
    bool InitializeAutomationTesting();

    /**
     * Execute single test case
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Execution", CallInEditor)
    FAuracronQATestExecution ExecuteTestCase(const FAuracronQATestCase& TestCase);

    /**
     * Execute multiple test cases
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Execution", CallInEditor)
    TArray<FAuracronQATestExecution> ExecuteTestSuite(const TArray<FAuracronQATestCase>& TestCases);

    /**
     * Execute all tests with specific tag
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Execution", CallInEditor)
    TArray<FAuracronQATestExecution> ExecuteTestsByTag(const FString& Tag);

    /**
     * Execute all tests of specific type
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Execution", CallInEditor)
    TArray<FAuracronQATestExecution> ExecuteTestsByType(EAuracronQATestType TestType);

    // === Asset Validation ===

    /**
     * Validate single asset
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Validation", CallInEditor)
    FAuracronQATestExecution ValidateAsset(UObject* Asset);

    /**
     * Validate multiple assets
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Validation", CallInEditor)
    TArray<FAuracronQATestExecution> ValidateAssets(const TArray<UObject*>& Assets);

    /**
     * Validate assets by path
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Validation", CallInEditor)
    TArray<FAuracronQATestExecution> ValidateAssetsByPath(const TArray<FString>& AssetPaths);

    // === Screenshot Comparison ===

    /**
     * Capture screenshot for visual validation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Visual", CallInEditor)
    FString CaptureScreenshot(const FString& ScreenshotName, int32 Width = 1920, int32 Height = 1080);

    /**
     * Compare screenshots for visual regression testing
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Visual", CallInEditor)
    FAuracronQATestExecution CompareScreenshots(const FString& ReferenceImage, const FString& CurrentImage, float Tolerance = 0.95f);

    /**
     * Capture champion render for visual validation
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Visual", CallInEditor)
    FString CaptureChampionRender(AActor* ChampionActor, const FString& ChampionName);

    // === Performance Profiling ===

    /**
     * Start performance profiling
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Performance", CallInEditor)
    bool StartPerformanceProfiling();

    /**
     * Stop performance profiling and get results
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Performance", CallInEditor)
    FAuracronQAPerformanceData StopPerformanceProfiling();

    /**
     * Profile champion performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Performance", CallInEditor)
    FAuracronQATestExecution ProfileChampionPerformance(AActor* ChampionActor, float TestDuration = 10.0f);

    // === Memory Tracking ===

    /**
     * Start memory tracking
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Memory", CallInEditor)
    bool StartMemoryTracking();

    /**
     * Stop memory tracking and get results
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Memory", CallInEditor)
    FAuracronQATestExecution StopMemoryTracking();

    // === Asset Loading Validation ===

    /**
     * Validate asset loading performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Asset Loading", CallInEditor)
    FAuracronQATestExecution ValidateAssetLoadingPerformance(const TArray<FString>& AssetPaths);

    /**
     * Test asset streaming performance
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Asset Loading", CallInEditor)
    FAuracronQATestExecution TestAssetStreamingPerformance(const FString& LevelPath);

    // === Utility Functions ===

    /**
     * Export QA results to JSON
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Utility", CallInEditor)
    bool ExportQAResultsToJSON(const TArray<FAuracronQATestExecution>& TestResults, const FString& OutputPath);

    /**
     * Import QA test cases from JSON
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Utility", CallInEditor)
    TArray<FAuracronQATestCase> ImportQATestCasesFromJSON(const FString& InputPath);

    /**
     * Get QA system configuration
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Utility", CallInEditor)
    FString GetQASystemConfiguration();

public:
    // === Configuration Properties ===

    /** Default test timeout in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float DefaultTestTimeout = 30.0f;

    /** Screenshot comparison tolerance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float DefaultScreenshotTolerance = 0.95f;

    /** Performance profiling sample rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float ProfilingSampleRate = 1.0f;

    /** Enable detailed logging */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableDetailedLogging = true;

    /** Enable performance profiling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnablePerformanceProfiling = true;

    /** Enable memory tracking */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bEnableMemoryTracking = true;

    /** QA output directory */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FString QAOutputDirectory = TEXT("QA/Results");

    /** Screenshot output directory */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FString ScreenshotDirectory = TEXT("QA/Screenshots");

private:
    // === Internal State ===
    
    /** Automation framework initialized */
    bool bAutomationInitialized = false;
    
    /** Performance profiling active */
    bool bPerformanceProfilingActive = false;
    
    /** Memory tracking active */
    bool bMemoryTrackingActive = false;
    
    /** Performance profiling start time */
    FDateTime ProfilingStartTime;
    
    /** Memory tracking start stats */
    FGenericPlatformMemoryStats MemoryTrackingStartStats;
    
    /** Current test execution */
    FAuracronQATestExecution* CurrentTestExecution = nullptr;

public:
    // === Python Integration ===
    
    /**
     * Initialize Python bindings
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Python", CallInEditor)
    bool InitializePythonBindings();
    
    /**
     * Execute Python test script
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Python", CallInEditor)
    FAuracronQATestExecution ExecutePythonTestScript(const FString& ScriptPath);
    
    /**
     * Get QA data for Python
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON QA|Python", CallInEditor)
    FString GetQADataForPython() const;
};

