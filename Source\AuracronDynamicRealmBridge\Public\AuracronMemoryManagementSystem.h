/**
 * AuracronMemoryManagementSystem.h
 * 
 * Sistema avançado de gerenciamento de memória para otimização automática
 * baseada nas capacidades do dispositivo e demandas do jogo.
 * 
 * Implementa:
 * - Orçamento dinâmico de memória por categoria
 * - Garbage collection inteligente
 * - Streaming preditivo de assets
 * - Monitoramento contínuo de uso
 * - Otimização automática baseada em padrões
 * 
 * Usa UE 5.6 APIs modernas para gerenciamento eficiente de recursos.
 */

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/Engine.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "UObject/GarbageCollection.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "AuracronMemoryManagementSystem.generated.h"

// Forward declarations
class UAuracronHardwareDetectionSystem;

/**
 * Categorias de memória
 */
UENUM(BlueprintType)
enum class EMemoryCategory : uint8
{
    None                UMETA(DisplayName = "None"),
    Textures            UMETA(DisplayName = "Textures"),
    Audio               UMETA(DisplayName = "Audio"),
    Meshes              UMETA(DisplayName = "Meshes"),
    Particles           UMETA(DisplayName = "Particles"),
    Animation           UMETA(DisplayName = "Animation"),
    Blueprints          UMETA(DisplayName = "Blueprints"),
    Materials           UMETA(DisplayName = "Materials"),
    Levels              UMETA(DisplayName = "Levels"),
    Gameplay            UMETA(DisplayName = "Gameplay"),
    UI                  UMETA(DisplayName = "UI"),
    Networking          UMETA(DisplayName = "Networking"),
    Streaming           UMETA(DisplayName = "Streaming"),
    Cache               UMETA(DisplayName = "Cache"),
    System              UMETA(DisplayName = "System")
};

/**
 * Prioridades de memória
 */
UENUM(BlueprintType)
enum class EMemoryPriority : uint8
{
    Critical            UMETA(DisplayName = "Critical"),
    High                UMETA(DisplayName = "High"),
    Normal              UMETA(DisplayName = "Normal"),
    Low                 UMETA(DisplayName = "Low"),
    Disposable          UMETA(DisplayName = "Disposable")
};

/**
 * Estados de gerenciamento de memória
 */
UENUM(BlueprintType)
enum class EMemoryManagementState : uint8
{
    Optimal             UMETA(DisplayName = "Optimal"),
    Good                UMETA(DisplayName = "Good"),
    Warning             UMETA(DisplayName = "Warning"),
    Critical            UMETA(DisplayName = "Critical"),
    Emergency           UMETA(DisplayName = "Emergency")
};

/**
 * Orçamento de memória por categoria
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FMemoryBudget
{
    GENERATED_BODY()

    /** Categoria de memória */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    EMemoryCategory Category;

    /** Orçamento máximo em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float MaxBudgetMB;

    /** Orçamento atual em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float CurrentBudgetMB;

    /** Uso atual em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float CurrentUsageMB;

    /** Prioridade da categoria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    EMemoryPriority Priority;

    /** Permite crescimento dinâmico */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    bool bAllowDynamicGrowth;

    /** Multiplicador de crescimento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float GrowthMultiplier;

    /** Threshold de warning (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float WarningThreshold;

    /** Threshold crítico (0.0 - 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Budget")
    float CriticalThreshold;

    FMemoryBudget()
    {
        Category = EMemoryCategory::None;
        MaxBudgetMB = 512.0f;
        CurrentBudgetMB = 512.0f;
        CurrentUsageMB = 0.0f;
        Priority = EMemoryPriority::Normal;
        bAllowDynamicGrowth = true;
        GrowthMultiplier = 1.5f;
        WarningThreshold = 0.8f;
        CriticalThreshold = 0.95f;
    }
};

/**
 * Configuração de garbage collection
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FGarbageCollectionConfig
{
    GENERATED_BODY()

    /** Habilitar GC automático */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    bool bEnableAutomaticGC;

    /** Intervalo de GC em segundos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    float GCIntervalSeconds;

    /** Threshold de memória para GC forçado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    float ForceGCThresholdMB;

    /** Usar GC incremental */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    bool bUseIncrementalGC;

    /** Tempo máximo de GC por frame em ms */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    float MaxGCTimePerFrameMS;

    /** Habilitar GC durante loading */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    bool bEnableGCDuringLoading;

    /** Priorizar GC de categorias específicas */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "GC Config")
    TArray<EMemoryCategory> PriorityCategories;

    FGarbageCollectionConfig()
    {
        bEnableAutomaticGC = true;
        GCIntervalSeconds = 30.0f;
        ForceGCThresholdMB = 1024.0f;
        bUseIncrementalGC = true;
        MaxGCTimePerFrameMS = 5.0f;
        bEnableGCDuringLoading = false;
    }
};

/**
 * Configuração de streaming de assets
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAssetStreamingConfig
{
    GENERATED_BODY()

    /** Habilitar streaming preditivo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    bool bEnablePredictiveStreaming;

    /** Distância de pré-carregamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    float PreloadDistance;

    /** Distância de descarregamento */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    float UnloadDistance;

    /** Orçamento de streaming em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    float StreamingBudgetMB;

    /** Prioridade de streaming por categoria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    TMap<EMemoryCategory, int32> StreamingPriorities;

    /** Usar compressão de assets */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    bool bUseAssetCompression;

    /** Nível de compressão (0-9) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming Config")
    int32 CompressionLevel;

    FAssetStreamingConfig()
    {
        bEnablePredictiveStreaming = true;
        PreloadDistance = 5000.0f;
        UnloadDistance = 10000.0f;
        StreamingBudgetMB = 1024.0f;
        bUseAssetCompression = true;
        CompressionLevel = 6;
    }
};

/**
 * Métricas de memória
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FMemoryMetrics
{
    GENERATED_BODY()

    /** Memória total do sistema em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    float TotalSystemMemoryMB;

    /** Memória disponível em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    float AvailableMemoryMB;

    /** Memória usada pelo jogo em MB */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    float GameMemoryUsageMB;

    /** Memória usada por categoria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    TMap<EMemoryCategory, float> CategoryUsageMB;

    /** Número de objetos carregados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    int32 LoadedObjectCount;

    /** Número de assets em streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    int32 StreamingAssetCount;

    /** Taxa de hit do cache */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    float CacheHitRate;

    /** Fragmentação de memória */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    float MemoryFragmentation;

    /** Timestamp da última atualização */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory Metrics")
    FDateTime LastUpdateTime;

    FMemoryMetrics()
    {
        TotalSystemMemoryMB = 0.0f;
        AvailableMemoryMB = 0.0f;
        GameMemoryUsageMB = 0.0f;
        LoadedObjectCount = 0;
        StreamingAssetCount = 0;
        CacheHitRate = 0.0f;
        MemoryFragmentation = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }
};

/**
 * Sistema de gerenciamento de memória
 */
UCLASS(BlueprintType)
class AURACRONDYNAMICREALMBRIDGE_API UAuracronMemoryManagementSystem : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    // USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    virtual void Tick(float DeltaTime) override;
    virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

    // === Core Memory Management ===
    
    /** Inicializar sistema de gerenciamento de memória */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void InitializeMemoryManagement();

    /** Atualizar sistema de memória */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void UpdateMemoryManagement(float DeltaTime);

    /** Finalizar sistema de memória */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void ShutdownMemoryManagement();

    // === Budget Management ===
    
    /** Configurar orçamento de categoria */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void SetCategoryBudget(EMemoryCategory Category, float BudgetMB);

    /** Obter orçamento de categoria */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    FMemoryBudget GetCategoryBudget(EMemoryCategory Category) const;

    /** Obter todos os orçamentos */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    TMap<EMemoryCategory, FMemoryBudget> GetAllBudgets() const;

    /** Ajustar orçamentos automaticamente */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void AutoAdjustBudgets();

    // === Garbage Collection ===
    
    /** Forçar garbage collection */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void ForceGarbageCollection();

    /** Configurar garbage collection */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void ConfigureGarbageCollection(const FGarbageCollectionConfig& Config);

    /** Executar GC incremental */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void PerformIncrementalGC();

    /** Limpar categoria específica */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void CleanupCategory(EMemoryCategory Category);

    // === Asset Streaming ===
    
    /** Configurar streaming de assets */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void ConfigureAssetStreaming(const FAssetStreamingConfig& Config);

    /** Pré-carregar assets críticos */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void PreloadCriticalAssets();

    /** Descarregar assets não utilizados */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void UnloadUnusedAssets();

    /** Otimizar streaming baseado em localização */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void OptimizeStreamingForLocation(const FVector& Location);

    // === Monitoring ===
    
    /** Obter métricas atuais de memória */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    FMemoryMetrics GetCurrentMemoryMetrics() const;

    /** Obter estado do gerenciamento de memória */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    EMemoryManagementState GetMemoryManagementState() const;

    /** Verificar se categoria está em warning */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    bool IsCategoryInWarning(EMemoryCategory Category) const;

    /** Verificar se categoria está crítica */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Memory Management")
    bool IsCategoryCritical(EMemoryCategory Category) const;

    // === Optimization ===
    
    /** Otimizar uso de memória */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void OptimizeMemoryUsage();

    /** Aplicar configurações baseadas em hardware */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void ApplyHardwareBasedConfiguration();

    /** Reduzir uso de memória de emergência */
    UFUNCTION(BlueprintCallable, Category = "Memory Management")
    void EmergencyMemoryReduction();

    // === Events ===
    
    /** Evento quando orçamento é excedido */
    UFUNCTION(BlueprintImplementableEvent, Category = "Memory Management")
    void OnBudgetExceeded(EMemoryCategory Category, float UsageMB, float BudgetMB);

    /** Evento quando estado de memória muda */
    UFUNCTION(BlueprintImplementableEvent, Category = "Memory Management")
    void OnMemoryStateChanged(EMemoryManagementState OldState, EMemoryManagementState NewState);

    /** Evento quando GC é executado */
    UFUNCTION(BlueprintImplementableEvent, Category = "Memory Management")
    void OnGarbageCollectionPerformed(float MemoryFreedMB, float DurationMS);

    /** Evento quando otimização é aplicada */
    UFUNCTION(BlueprintImplementableEvent, Category = "Memory Management")
    void OnMemoryOptimizationApplied(float MemorySavedMB);

protected:
    // === Configuration ===
    
    /** Orçamentos por categoria */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    TMap<EMemoryCategory, FMemoryBudget> CategoryBudgets;

    /** Configuração de garbage collection */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FGarbageCollectionConfig GCConfig;

    /** Configuração de streaming */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FAssetStreamingConfig StreamingConfig;

    /** Métricas atuais */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    FMemoryMetrics CurrentMetrics;

    /** Estado atual do sistema */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    EMemoryManagementState CurrentState;

    /** Sistema inicializado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bSystemInitialized;

private:
    // === Internal Implementation ===
    void InitializeDefaultBudgets();
    void UpdateMemoryMetrics();
    void CheckBudgetViolations();
    void ProcessAutomaticGC();
    void UpdateStreamingSystem();
    void AnalyzeMemoryPatterns();
    
    // === Budget Implementation ===
    void CalculateCategoryUsage();
    void AdjustBudgetForCategory(EMemoryCategory Category);
    void RebalanceBudgets();
    
    // === GC Implementation ===
    void ScheduleGarbageCollection();
    void PerformCategorySpecificGC(EMemoryCategory Category);
    void OptimizeGCTiming();
    
    // === Streaming Implementation ===
    void UpdateAssetStreaming();
    void PredictAssetNeeds();
    void ManageStreamingBudget();
    
    // === Optimization Implementation ===
    void ApplyMemoryOptimizations();
    void ReduceQualityForMemory();
    void CompressAssets();
    void ClearCaches();
    
    // === Utility Methods ===
    EMemoryManagementState DetermineMemoryState();
    float CalculateMemoryPressure();
    bool ShouldPerformGC();
    void LogMemoryStatistics();
    
    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UAuracronHardwareDetectionSystem> CachedHardwareSystem;
    
    UPROPERTY()
    TObjectPtr<UAssetManager> CachedAssetManager;
    
    // === Timers ===
    FTimerHandle MemoryUpdateTimer;
    FTimerHandle GCTimer;
    FTimerHandle StreamingUpdateTimer;
    FTimerHandle OptimizationTimer;
    
    // === Performance Tracking ===
    float LastGCTime;
    float TotalMemoryFreed;
    int32 GCExecutionCount;
    TArray<float> MemoryUsageHistory;
    
    // === State Tracking ===
    float LastUpdateTime;
    float LastOptimizationTime;
    bool bEmergencyModeActive;
};
