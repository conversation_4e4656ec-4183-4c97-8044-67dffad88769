/**
 * Auracron Sigil System - Native GameplayTags Implementation
 * 
 * Implements all native GameplayTags for the Sigil System using UE 5.6 APIs
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

#include "AuracronSigilGameplayTags.h"
#include "GameplayTagsManager.h"
#include "Engine/Engine.h"

namespace AuracronSigilTags
{
    // === Root Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil, "Sigil", "Root tag for all Auracron Sigil system tags");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State, "Sigil.State", "Tags for Sigil states");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type, "Sigil.Type", "Tags for Sigil types");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype, "Sigil.Archetype", "Tags for Fusion 2.0 Archetypes");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Fusion, "Sigil.Fusion", "Tags for Fusion system");

    // === State Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State_Inactive, "Sigil.State.Inactive", "Sigil is not active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State_Active, "Sigil.State.Active", "Sigil is currently active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State_Cooldown, "Sigil.State.Cooldown", "Sigil is on cooldown");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State_Charging, "Sigil.State.Charging", "Sigil is charging up");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_State_Fusion20, "Sigil.State.Fusion20", "Fusion 2.0 is active");

    // === Aegis Sigil Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis, "Sigil.Type.Aegis", "Aegis (Defense) Sigil type");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis_Primordial, "Sigil.Type.Aegis.Primordial", "Aegis Primordial - Basic shield, absorbs damage");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis_Cristalino, "Sigil.Type.Aegis.Cristalino", "Aegis Cristalino - Shield that reflects damage");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis_Temporal, "Sigil.Type.Aegis.Temporal", "Aegis Temporal - Shield that slows projectiles");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis_Espectral, "Sigil.Type.Aegis.Espectral", "Aegis Espectral - Shield that absorbs magical effects");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Aegis_Absoluto, "Sigil.Type.Aegis.Absoluto", "Aegis Absoluto - Shield that grants temporary invulnerability");

    // === Ruin Sigil Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin, "Sigil.Type.Ruin", "Ruin (Damage) Sigil type");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin_Flamejante, "Sigil.Type.Ruin.Flamejante", "Ruin Flamejante - Continuous fire damage");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin_Gelido, "Sigil.Type.Ruin.Gelido", "Ruin Gélido - Ice damage that slows");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin_Sombrio, "Sigil.Type.Ruin.Sombrio", "Ruin Sombrio - Shadow damage that reduces vision");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin_Corrosivo, "Sigil.Type.Ruin.Corrosivo", "Ruin Corrosivo - Corrosive damage that reduces armor");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Ruin_Aniquilador, "Sigil.Type.Ruin.Aniquilador", "Ruin Aniquilador - Massive instant damage");

    // === Vesper Sigil Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper, "Sigil.Type.Vesper", "Vesper (Support) Sigil type");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Curativo, "Sigil.Type.Vesper.Curativo", "Vesper Curativo - HP regeneration");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Energetico, "Sigil.Type.Vesper.Energetico", "Vesper Energético - MP regeneration");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Velocidade, "Sigil.Type.Vesper.Velocidade", "Vesper Velocidade - Movement speed increase");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Visao, "Sigil.Type.Vesper.Visao", "Vesper Visão - Vision range increase");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Teleporte, "Sigil.Type.Vesper.Teleporte", "Vesper Teleporte - Short range teleportation");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Type_Vesper_Temporal, "Sigil.Type.Vesper.Temporal", "Vesper Temporal - Time manipulation (slow/haste)");

    // === Fusion 2.0 Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Fusion_Active, "Sigil.Fusion.Active", "Fusion 2.0 is currently active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Fusion_Cooldown, "Sigil.Fusion.Cooldown", "Fusion 2.0 is on cooldown");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Fusion_Synergy, "Sigil.Fusion.Synergy", "Sigils have synergy bonus");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Fusion_Resonance, "Sigil.Fusion.Resonance", "Sigils have elemental resonance");

    // === Archetype Category Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Guardian, "Sigil.Archetype.Guardian", "Guardian archetype - Tank focused");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Destroyer, "Sigil.Archetype.Destroyer", "Destroyer archetype - Damage focused");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Assassin, "Sigil.Archetype.Assassin", "Assassin archetype - Burst damage");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Healer, "Sigil.Archetype.Healer", "Healer archetype - Support focused");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Controller, "Sigil.Archetype.Controller", "Controller archetype - Utility focused");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Archetype_Hybrid, "Sigil.Archetype.Hybrid", "Hybrid archetype - Balanced");

    // === Effect Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Shield, "Sigil.Effect.Shield", "Shield effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Damage, "Sigil.Effect.Damage", "Damage effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Heal, "Sigil.Effect.Heal", "Healing effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Buff, "Sigil.Effect.Buff", "Buff effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Debuff, "Sigil.Effect.Debuff", "Debuff effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_Teleport, "Sigil.Effect.Teleport", "Teleport effect active");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Effect_TimeManipulation, "Sigil.Effect.TimeManipulation", "Time manipulation effect active");

    // === Input Tags ===
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Input_Aegis, "Sigil.Input.Aegis", "Input for Aegis Sigil activation");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Input_Ruin, "Sigil.Input.Ruin", "Input for Ruin Sigil activation");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Input_Vesper, "Sigil.Input.Vesper", "Input for Vesper Sigil activation");
    UE_DEFINE_GAMEPLAY_TAG_COMMENT(Sigil_Input_Fusion20, "Sigil.Input.Fusion20", "Input for Fusion 2.0 activation");

    // === Utility Functions Implementation ===
    // Note: These functions are now implemented outside the namespace to avoid redefinition issues

} // End namespace AuracronSigilTags

// Utility functions implementation outside namespace
namespace AuracronSigilTags
{
    FGameplayTag GetAegisSigilTag(EAuracronAegisSigilType AegisType)
    {
        switch (AegisType)
        {
            case EAuracronAegisSigilType::Primordial:
                return Sigil_Type_Aegis_Primordial;
            case EAuracronAegisSigilType::Cristalino:
                return Sigil_Type_Aegis_Cristalino;
            case EAuracronAegisSigilType::Temporal:
                return Sigil_Type_Aegis_Temporal;
            case EAuracronAegisSigilType::Espectral:
                return Sigil_Type_Aegis_Espectral;
            case EAuracronAegisSigilType::Absoluto:
                return Sigil_Type_Aegis_Absoluto;
            default:
                return Sigil_Type_Aegis;
        }
    }

    FGameplayTag GetRuinSigilTag(EAuracronRuinSigilType RuinType)
    {
        switch (RuinType)
        {
            case EAuracronRuinSigilType::Flamejante:
                return Sigil_Type_Ruin_Flamejante;
            case EAuracronRuinSigilType::Gelido:
                return Sigil_Type_Ruin_Gelido;
            case EAuracronRuinSigilType::Sombrio:
                return Sigil_Type_Ruin_Sombrio;
            case EAuracronRuinSigilType::Corrosivo:
                return Sigil_Type_Ruin_Corrosivo;
            case EAuracronRuinSigilType::Aniquilador:
                return Sigil_Type_Ruin_Aniquilador;
            default:
                return Sigil_Type_Ruin;
        }
    }

    FGameplayTag GetVesperSigilTag(EAuracronVesperSigilType VesperType)
    {
        switch (VesperType)
        {
            case EAuracronVesperSigilType::Curativo:
                return Sigil_Type_Vesper_Curativo;
            case EAuracronVesperSigilType::Energetico:
                return Sigil_Type_Vesper_Energetico;
            case EAuracronVesperSigilType::Velocidade:
                return Sigil_Type_Vesper_Velocidade;
            case EAuracronVesperSigilType::Visao:
                return Sigil_Type_Vesper_Visao;
            case EAuracronVesperSigilType::Teleporte:
                return Sigil_Type_Vesper_Teleporte;
            case EAuracronVesperSigilType::Temporal:
                return Sigil_Type_Vesper_Temporal;
            default:
                return Sigil_Type_Vesper;
        }
    }

    FGameplayTag GenerateArchetypeTag(EAuracronAegisSigilType AegisType, EAuracronRuinSigilType RuinType, EAuracronVesperSigilType VesperType)
    {
        // Generate unique archetype tag based on combination
        FString ArchetypeTagString = FString::Printf(TEXT("Sigil.Archetype.%d_%d_%d"), 
            static_cast<int32>(AegisType), 
            static_cast<int32>(RuinType), 
            static_cast<int32>(VesperType));
        
        return FGameplayTag::RequestGameplayTag(FName(*ArchetypeTagString));
    }

    FGameplayTagContainer GetAllSigilTags()
    {
        FGameplayTagContainer AllTags;
        
        // Add all Sigil-related tags
        AllTags.AddTag(Sigil);
        AllTags.AddTag(Sigil_State);
        AllTags.AddTag(Sigil_Type);
        AllTags.AddTag(Sigil_Archetype);
        AllTags.AddTag(Sigil_Fusion);
        
        // Add state tags
        AllTags.AddTag(Sigil_State_Inactive);
        AllTags.AddTag(Sigil_State_Active);
        AllTags.AddTag(Sigil_State_Cooldown);
        AllTags.AddTag(Sigil_State_Charging);
        AllTags.AddTag(Sigil_State_Fusion20);
        
        // Add type tags
        AllTags.AddTag(Sigil_Type_Aegis);
        AllTags.AddTag(Sigil_Type_Ruin);
        AllTags.AddTag(Sigil_Type_Vesper);
        
        return AllTags;
    }
}
