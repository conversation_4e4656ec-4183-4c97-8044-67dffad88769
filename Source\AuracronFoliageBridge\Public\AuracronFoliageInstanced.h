// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - Foliage Instanced Static Mesh System Header
// Bridge 4.2: Foliage - Instanced Static Mesh System

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "AuracronFoliage.h"

// Instanced mesh includes for UE5.6
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"

// Rendering includes
#include "Engine/Engine.h"
#include "RenderingThread.h"
#include "RenderResource.h"
#include "StaticMeshResources.h"
// Forward declaration for InstancedStaticMesh compatibility
class UInstancedStaticMeshComponent;

// Performance includes
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Async/AsyncWork.h"
#include "Async/TaskGraphInterfaces.h"

// Memory management
#include "HAL/PlatformMemory.h"
#include "Containers/ResourceArray.h"

#include "AuracronFoliageInstanced.generated.h"

// Forward declarations
class UAuracronFoliageInstancedManager;
class UAuracronInstancedMeshBatch;

// =============================================================================
// INSTANCED MESH TYPES AND ENUMS
// =============================================================================

// Instance rendering modes
UENUM(BlueprintType)
enum class EAuracronInstanceRenderingMode : uint8
{
    Standard                UMETA(DisplayName = "Standard"),
    Hierarchical            UMETA(DisplayName = "Hierarchical"),
    Clustered               UMETA(DisplayName = "Clustered"),
    GPU                     UMETA(DisplayName = "GPU Driven"),
    Nanite                  UMETA(DisplayName = "Nanite")
};

// Instance culling modes
UENUM(BlueprintType)
enum class EAuracronInstanceCullingMode : uint8
{
    None                    UMETA(DisplayName = "None"),
    Frustum                 UMETA(DisplayName = "Frustum"),
    Distance                UMETA(DisplayName = "Distance"),
    Occlusion               UMETA(DisplayName = "Occlusion"),
    Combined                UMETA(DisplayName = "Combined"),
    GPU                     UMETA(DisplayName = "GPU Culling")
};

// Instance LOD modes
UENUM(BlueprintType)
enum class EAuracronInstanceLODMode : uint8
{
    PerInstance             UMETA(DisplayName = "Per Instance"),
    PerCluster              UMETA(DisplayName = "Per Cluster"),
    PerBatch                UMETA(DisplayName = "Per Batch"),
    Adaptive                UMETA(DisplayName = "Adaptive"),
    Performance             UMETA(DisplayName = "Performance Based")
};

// Instance update modes
UENUM(BlueprintType)
enum class EAuracronInstanceUpdateMode : uint8
{
    Immediate               UMETA(DisplayName = "Immediate"),
    Deferred                UMETA(DisplayName = "Deferred"),
    Batched                 UMETA(DisplayName = "Batched"),
    Async                   UMETA(DisplayName = "Async"),
    GPU                     UMETA(DisplayName = "GPU Update")
};

// =============================================================================
// INSTANCED MESH CONFIGURATION
// =============================================================================

/**
 * Instanced Mesh Configuration
 * Configuration settings for instanced static mesh system
 */
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronInstancedMeshConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    bool bEnableInstancedRendering = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    bool bEnableHierarchicalInstancing = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    bool bEnableGPUCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    bool bEnableNaniteSupport = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    EAuracronInstanceRenderingMode DefaultRenderingMode = EAuracronInstanceRenderingMode::Hierarchical;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    EAuracronInstanceCullingMode DefaultCullingMode = EAuracronInstanceCullingMode::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    EAuracronInstanceLODMode DefaultLODMode = EAuracronInstanceLODMode::Adaptive;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instanced Mesh")
    EAuracronInstanceUpdateMode DefaultUpdateMode = EAuracronInstanceUpdateMode::Batched;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxInstancesPerComponent = 50000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 MaxInstancesPerBatch = 1000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    int32 ClusterSize = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float CullingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance0 = 1500.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance1 = 4000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance2 = 8000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float LODDistance3 = 12000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableInstanceDataCompression = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableInstancePooling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    bool bEnableAsyncLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    int32 InstancePoolSize = 10000;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Memory")
    float MemoryBudgetMB = 512.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableFrustumCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableOcclusionCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableDistanceCulling = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableBatchOptimization = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    bool bEnableInstanceMerging = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Optimization")
    float BatchOptimizationInterval = 5.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableInstanceDebugDraw = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowInstanceBounds = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowClusterBounds = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowCullingInfo = false;

    FAuracronInstancedMeshConfiguration()
    {
        bEnableInstancedRendering = true;
        bEnableHierarchicalInstancing = true;
        bEnableGPUCulling = true;
        bEnableNaniteSupport = true;
        DefaultRenderingMode = EAuracronInstanceRenderingMode::Hierarchical;
        DefaultCullingMode = EAuracronInstanceCullingMode::Combined;
        DefaultLODMode = EAuracronInstanceLODMode::Adaptive;
        DefaultUpdateMode = EAuracronInstanceUpdateMode::Batched;
        MaxInstancesPerComponent = 50000;
        MaxInstancesPerBatch = 1000;
        ClusterSize = 100;
        CullingDistance = 15000.0f;
        LODDistance0 = 1500.0f;
        LODDistance1 = 4000.0f;
        LODDistance2 = 8000.0f;
        LODDistance3 = 12000.0f;
        bEnableInstanceDataCompression = true;
        bEnableInstancePooling = true;
        bEnableAsyncLoading = true;
        InstancePoolSize = 10000;
        MemoryBudgetMB = 512.0f;
        bEnableFrustumCulling = true;
        bEnableOcclusionCulling = true;
        bEnableDistanceCulling = true;
        bEnableBatchOptimization = true;
        bEnableInstanceMerging = true;
        BatchOptimizationInterval = 5.0f;
        bEnableInstanceDebugDraw = false;
        bShowInstanceBounds = false;
        bShowClusterBounds = false;
        bShowCullingInfo = false;
    }
};

// =============================================================================
// INSTANCE BATCH DATA
// =============================================================================

/**
 * Instance Batch Data
 * Data structure for batched instance rendering
 */
USTRUCT(BlueprintType)
struct FAuracronInstanceBatchData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    FString BatchId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    TSoftObjectPtr<UStaticMesh> StaticMesh;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    TSoftObjectPtr<UMaterialInterface> Material;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    TArray<FTransform> InstanceTransforms;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    TArray<FLinearColor> InstanceColors;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    TArray<float> InstanceCustomData;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    EAuracronInstanceRenderingMode RenderingMode = EAuracronInstanceRenderingMode::Hierarchical;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    EAuracronInstanceCullingMode CullingMode = EAuracronInstanceCullingMode::Combined;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bIsVisible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bCastShadows = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    bool bReceiveDecals = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    int32 LODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    FBox BoundingBox;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    FDateTime CreationTime;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Batch")
    FDateTime LastUpdateTime;

    FAuracronInstanceBatchData()
    {
        RenderingMode = EAuracronInstanceRenderingMode::Hierarchical;
        CullingMode = EAuracronInstanceCullingMode::Combined;
        bIsVisible = true;
        bCastShadows = true;
        bReceiveDecals = true;
        LODLevel = 0;
        MemoryUsageMB = 0.0f;
        CreationTime = FDateTime::Now();
        LastUpdateTime = FDateTime::Now();
    }
};

// =============================================================================
// INSTANCE CLUSTER DATA
// =============================================================================

/**
 * Instance Cluster Data
 * Data structure for hierarchical instance clustering
 */
USTRUCT(BlueprintType)
struct FAuracronInstanceClusterData
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    FString ClusterId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    FString ParentBatchId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    TArray<int32> InstanceIndices;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    FBox ClusterBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    FVector ClusterCenter;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    float ClusterRadius = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    int32 LODLevel = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    bool bIsVisible = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    bool bIsCulled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    float DistanceToViewer = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cluster")
    float ScreenSize = 0.0f;

    FAuracronInstanceClusterData()
    {
        ClusterRadius = 0.0f;
        LODLevel = 0;
        bIsVisible = true;
        bIsCulled = false;
        DistanceToViewer = 0.0f;
        ScreenSize = 0.0f;
    }
};

// =============================================================================
// FOLIAGE INSTANCED MANAGER
// =============================================================================

/**
 * Foliage Instanced Manager
 * Manager for instanced static mesh rendering system
 */
UCLASS(BlueprintType, Blueprintable)
class UAuracronFoliageInstancedManager : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    static UAuracronFoliageInstancedManager* GetInstance();

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void Initialize(const FAuracronInstancedMeshConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void Shutdown();

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void Tick(float DeltaTime);

    // Batch management
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    FString CreateInstanceBatch(UStaticMesh* StaticMesh, UMaterialInterface* Material = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool DestroyInstanceBatch(const FString& BatchId);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    FAuracronInstanceBatchData GetInstanceBatch(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    TArray<FAuracronInstanceBatchData> GetAllInstanceBatches() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool UpdateInstanceBatch(const FAuracronInstanceBatchData& BatchData);

    // Instance management
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 AddInstance(const FString& BatchId, const FTransform& Transform, const FLinearColor& Color = FLinearColor::White);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool RemoveInstance(const FString& BatchId, int32 InstanceIndex);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool UpdateInstance(const FString& BatchId, int32 InstanceIndex, const FTransform& Transform, const FLinearColor& Color = FLinearColor::White);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    TArray<FTransform> GetInstanceTransforms(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 GetInstanceCount(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void ClearInstances(const FString& BatchId);

    // Batch operations
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 AddInstances(const FString& BatchId, const TArray<FTransform>& Transforms, const TArray<FLinearColor>& Colors);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager", meta = (CallInEditor = "true"))
    int32 AddInstancesSimple(const FString& BatchId, const TArray<FTransform>& Transforms);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool RemoveInstances(const FString& BatchId, const TArray<int32>& InstanceIndices);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool UpdateInstances(const FString& BatchId, const TArray<int32>& InstanceIndices, const TArray<FTransform>& Transforms, const TArray<FLinearColor>& Colors);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager", meta = (CallInEditor = "true"))
    bool UpdateInstancesSimple(const FString& BatchId, const TArray<int32>& InstanceIndices, const TArray<FTransform>& Transforms);

    // Clustering
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void BuildClusters(const FString& BatchId);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void RebuildClusters(const FString& BatchId);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    TArray<FAuracronInstanceClusterData> GetClusters(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void SetClusterLOD(const FString& BatchId, const FString& ClusterId, int32 LODLevel);

    // Culling and LOD
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void UpdateCulling(const FVector& ViewerLocation, const FVector& ViewerDirection);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void UpdateLOD(const FVector& ViewerLocation);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void SetBatchVisibility(const FString& BatchId, bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void SetBatchLOD(const FString& BatchId, int32 LODLevel);

    // Rendering optimization
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void OptimizeBatches();

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void MergeBatches(const TArray<FString>& BatchIds, const FString& NewBatchId);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void SplitBatch(const FString& BatchId, int32 MaxInstancesPerBatch);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void CompactBatches();

    // GPU operations
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void EnableGPUCulling(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool IsGPUCullingEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void UpdateGPUInstances(const FString& BatchId);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void FlushGPUCommands();

    // Memory management
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    float GetMemoryUsageMB() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    float GetBatchMemoryUsageMB(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void OptimizeMemoryUsage();

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void CompressInstanceData(const FString& BatchId);

    // Statistics
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 GetTotalInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 GetVisibleInstanceCount() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    int32 GetBatchCount() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    TMap<FString, int32> GetRenderingStatistics() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void SetConfiguration(const FAuracronInstancedMeshConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    FAuracronInstancedMeshConfiguration GetConfiguration() const;

    // Debug visualization
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    void DrawDebugVisualization(UWorld* World) const;

    // Component access
    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    UInstancedStaticMeshComponent* GetInstancedComponent(const FString& BatchId) const;

    UFUNCTION(BlueprintCallable, Category = "Instanced Manager")
    UHierarchicalInstancedStaticMeshComponent* GetHierarchicalComponent(const FString& BatchId) const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBatchCreated, FString, BatchId, FAuracronInstanceBatchData, BatchData);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBatchDestroyed, FString, BatchId);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInstanceAdded, FString, BatchId, int32, InstanceIndex);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInstanceRemoved, FString, BatchId, int32, InstanceIndex);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnBatchOptimized, FString, BatchId);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBatchCreated OnBatchCreated;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBatchDestroyed OnBatchDestroyed;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnInstanceAdded OnInstanceAdded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnInstanceRemoved OnInstanceRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnBatchOptimized OnBatchOptimized;

private:
    static UAuracronFoliageInstancedManager* Instance;

    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    FAuracronInstancedMeshConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    // Batch data
    TMap<FString, FAuracronInstanceBatchData> InstanceBatches;
    TMap<FString, TArray<FAuracronInstanceClusterData>> BatchClusters;
    TMap<FString, TWeakObjectPtr<UInstancedStaticMeshComponent>> InstancedComponents;
    TMap<FString, TWeakObjectPtr<UHierarchicalInstancedStaticMeshComponent>> HierarchicalComponents;

    // Performance tracking
    int32 TotalInstanceCount = 0;
    int32 VisibleInstanceCount = 0;
    float LastCullingUpdateTime = 0.0f;
    float LastLODUpdateTime = 0.0f;
    float LastOptimizationTime = 0.0f;

    // Memory tracking
    float TotalMemoryUsageMB = 0.0f;
    TMap<FString, float> BatchMemoryUsage;

    // Thread safety
    mutable FCriticalSection InstancedLock;

    // Internal functions
    void ValidateConfiguration();
    FString GenerateBatchId() const;
    UInstancedStaticMeshComponent* CreateInstancedComponent(const FString& BatchId, UStaticMesh* StaticMesh, UMaterialInterface* Material);
    UHierarchicalInstancedStaticMeshComponent* CreateHierarchicalComponent(const FString& BatchId, UStaticMesh* StaticMesh, UMaterialInterface* Material);
    void UpdateBatchBounds(const FString& BatchId);
    void UpdateBatchMemoryUsage(const FString& BatchId);
    void PerformCullingUpdate(const FVector& ViewerLocation, const FVector& ViewerDirection);
    void PerformLODUpdate(const FVector& ViewerLocation);
    void BuildClusterHierarchy(const FString& BatchId);
    void OptimizeBatch(const FString& BatchId);
    void CleanupInvalidBatches();
    bool ShouldUseClustering(int32 InstanceCount) const;
    float CalculateScreenSize(const FVector& BoundsCenter, float BoundsRadius, const FVector& ViewerLocation) const;
};
