/**
 * Auracron Sigil System Build Configuration
 * 
 * Advanced sigil fusion system with 150 archetype combinations
 * Integrates with UE 5.6 Gameplay Ability System and Enhanced Input
 * 
 * Features:
 * - 3 Sigil Types: <PERSON><PERSON><PERSON> (Defense), <PERSON><PERSON> (Damage), <PERSON><PERSON><PERSON> (Support)
 * - 150 Unique Archetype Combinations (5x5x6)
 * - Fusion 2.0 Engine with dynamic combinations
 * - Native GameplayTags integration
 * - Enhanced Input System integration
 * - Performance optimized for production
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

using UnrealBuildTool;

public class AuracronSigilSystem : ModuleRules
{
    public AuracronSigilSystem(ReadOnlyTargetRules Target) : base(Target)
    {
        // Use the latest C++ standard for UE 5.6
        CppStandard = CppStandardVersion.Cpp20;
        
        // Enable modern UE 5.6 features
        bEnableExceptions = false;
        bUseRTTI = false;
        CppCompileWarningSettings.UndefinedIdentifierWarningLevel = WarningLevel.Warning;
        
        // PCH usage for faster compilation
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        
        // Core dependencies for UE 5.6
        PublicDependencyModuleNames.AddRange(new string[]
        {
            "Core",
            "CoreUObject",
            "Engine",
            "UnrealEd",
            "ToolMenus",
            "EditorStyle",
            "EditorWidgets",
            "PropertyEditor",
            "SlateCore",
            "Slate",
            "UMG",
            "InputCore",
            "EnhancedInput",           // UE 5.6 Enhanced Input System
            "GameplayAbilities",      // Gameplay Ability System
            "GameplayTags",           // Native GameplayTags
            "GameplayTasks",          // Gameplay Tasks
            "NetCore",                // Networking core
            "ReplicationGraph",       // Advanced replication
            "Niagara",               // VFX system
            "AudioMixer",            // Audio system
            "CinematicCamera",       // Camera effects
            "LevelSequence",         // Sequencer integration
            "MovieScene",            // Movie scene framework
            "TimeManagement",        // Time dilation effects
            "PhysicsCore",           // Physics integration
            "Chaos",                 // Chaos physics
            "GeometryCollectionEngine", // Destruction system
            "FieldSystemEngine",     // Field system for effects
            "ProceduralMeshComponent", // Procedural geometry
            "RenderCore",            // Rendering system
            "RHI",                   // Render Hardware Interface
            "ApplicationCore"        // Application framework
        });

        // Private dependencies for internal systems
        PrivateDependencyModuleNames.AddRange(new string[]
        {
            "Json",                  // JSON parsing
            "JsonUtilities",         // JSON utilities
            "HTTP",                  // HTTP requests
            "ImageWrapper",          // Image processing
            "AssetRegistry",         // Asset management
            "DeveloperSettings",     // Settings framework
            "EngineSettings",        // Engine settings
            "UnrealEd",             // Editor integration
            "ToolMenus",            // Tool menus
            "ContentBrowser",       // Content browser
            "AssetTools",           // Asset tools
            "EditorSubsystem",      // Editor subsystems
            "SubobjectEditor",      // Subobject editing
            "KismetCompiler",       // Blueprint compilation
            "BlueprintGraph",       // Blueprint graph
            "KismetDeveloper",      // Kismet development
            "GameplayDebugger",     // Gameplay debugger
            "AIModule",             // AI integration
            "NavigationSystem",     // Navigation mesh
            "Landscape",            // Landscape system
            "Foliage",              // Foliage system
            "Water",                // Water system
            "GeometryCache",        // Geometry caching
            "HairStrandsCore",      // Hair/fur system
            "ClothingSystemRuntimeInterface", // Clothing system
            "AnimGraphRuntime",     // Animation system
            "AnimationCore",        // Animation core
            "ControlRig",           // Control rig system
            "IKRig",                // IK rig system
            "MetasoundEngine",      // Metasound audio
            "WaveTable",            // Wave table synthesis
            "Synthesis",            // Audio synthesis
            "AudioExtensions",      // Audio extensions
            "SignalProcessing",     // Signal processing
            "DSP",                  // Digital signal processing
            "AudioMixerCore",       // Audio mixer core
            "AudioMixerXAudio2",    // XAudio2 integration (Windows)
            "VulkanRHI",            // Vulkan support
            "D3D12RHI",             // DirectX 12 support
            "OpenGLDrv",            // OpenGL support
            "MetalRHI",             // Metal support (Mac)
            "RayTracing",           // Ray tracing support
            "DLSS",                 // NVIDIA DLSS
            "XeSS",                 // Intel XeSS
            "FSR",                  // AMD FSR
            "VariableRateShading",  // Variable rate shading
            "MeshDescription",      // Mesh description framework
            "StaticMeshDescription", // Static mesh description
            "SkeletalMeshDescription", // Skeletal mesh description
            "GeometryProcessing",   // Geometry processing
            "DynamicMesh",          // Dynamic mesh system
            "ModelingComponents",   // Modeling components
            "InteractiveToolsFramework", // Interactive tools
            "MeshModelingTools",    // Mesh modeling tools
            "GeometryScriptingCore", // Geometry scripting
            "PCG",                  // Procedural Content Generation
            "WorldPartition",       // World partition system
            "DataLayerEditor",      // Data layer system
            "LevelInstance",        // Level instances
            "ActorLayerUtilities",  // Actor layer utilities
            "VirtualTexturing",     // Virtual texturing
            "TextureUtilities",     // Texture utilities
            "MaterialUtilities",    // Material utilities
            "ShaderCore",           // Shader system
            "MaterialShaderQualitySettings", // Material quality
            "RenderTrace",          // Render tracing
            "GPULightmass",         // GPU lightmass
            "Lightmass",            // CPU lightmass
            "MeshUtilities",        // Mesh utilities
            "MeshReduction",        // Mesh reduction
            "MeshMergeUtilities",   // Mesh merging
            "HierarchicalLOD",      // HLOD system
            "VariantManagerContent", // Variant management
            "LevelVariantSets",     // Level variants
            "DataValidation",       // Data validation
            "MessageLog",           // Message logging
            "CollectionManager",    // Collection management
            "DirectoryWatcher",     // Directory watching
            "DesktopPlatform",      // Desktop platform
            "ApplicationCore",      // Application core
            "Projects",             // Project management
            "LauncherServices",     // Launcher integration
            "TargetPlatform",       // Target platform
            "DesktopTargetPlatform", // Desktop target
            "WindowsTargetPlatform", // Windows specific
            "LinuxTargetPlatform",  // Linux specific
            "MacTargetPlatform",    // Mac specific
            "AndroidTargetPlatform", // Android support
            "IOSTargetPlatform",    // iOS support
            "TVOSTargetPlatform",   // tvOS support
            "LuminTargetPlatform",  // Magic Leap support
            "HoloLensTargetPlatform", // HoloLens support
            "SteamVR",              // SteamVR integration
            "OculusVR",             // Oculus integration
            "OpenXR",               // OpenXR standard
            "MixedReality",         // Mixed reality
            "AugmentedReality",     // AR framework
            "LocationServicesBPLibrary", // Location services
            "MobilePatchingUtils",  // Mobile patching
            "BuildPatchServices",   // Build patching
            "PakFile",              // Pak file system
            "RSA",                  // RSA encryption
            "SSL",                  // SSL/TLS
            "WebBrowser",           // Web browser
            "HTTP",                 // HTTP client
            "WebSockets",           // WebSocket support
            "Networking",           // Networking core
            "Sockets",              // Socket system
            "PacketHandler",        // Packet handling
            "ReliableUDP",          // Reliable UDP
            "ICMP",                 // ICMP protocol
            "Voice",                // Voice chat
            "OnlineSubsystem",      // Online subsystem
            "OnlineSubsystemUtils", // Online utilities
            "Party",                // Party system
            "Sessions",             // Session management
            "Lobby",                // Lobby system
            "Tournaments",          // Tournament system
            "Leaderboards",         // Leaderboard system
            "Achievements",         // Achievement system
            "CloudSave",            // Cloud save system
            "Analytics",            // Analytics framework
            "AnalyticsET",          // Analytics event tracking
            "CrashReportCore",      // Crash reporting
            "ProgramAnalytics",     // Program analytics
            "EngineAnalytics",      // Engine analytics
            "EditorAnalytics",      // Editor analytics
            "GameAnalytics",        // Game analytics
            "Telemetry"             // Telemetry system
        });

        // Include paths for UE 5.6 headers
        PublicIncludePaths.AddRange(new string[]
        {
            "Runtime/Engine/Classes/GameFramework",
            "Runtime/Engine/Classes/Components",
            "Runtime/GameplayAbilities/Public",
            "Runtime/GameplayTags/Public",
            "Runtime/EnhancedInput/Public",
            "Runtime/Niagara/Public",
            "Runtime/AudioMixer/Public"
        });

        // Private include paths
        PrivateIncludePaths.AddRange(new string[]
        {
            "AuracronSigilSystem/Private",
            "AuracronSigilSystem/Private/Abilities",
            "AuracronSigilSystem/Private/Effects",
            "AuracronSigilSystem/Private/Components"
        });

        // Preprocessor definitions for UE 5.6
        PublicDefinitions.AddRange(new string[]
        {
            "AURACRON_SIGIL_SYSTEM_VERSION=100",
            "UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6=0",
            "UE_ENABLE_ICU=1"
        });

        // Optimization settings for production
        if (Target.Configuration == UnrealTargetConfiguration.Shipping)
        {
            PublicDefinitions.AddRange(new string[]
            {
                "AURACRON_SIGIL_OPTIMIZE=1",
                "AURACRON_SIGIL_DEBUG=0"
            });
        }
        else
        {
            PublicDefinitions.AddRange(new string[]
            {
                "AURACRON_SIGIL_OPTIMIZE=0", 
                "AURACRON_SIGIL_DEBUG=1"
            });
        }
    }
}
