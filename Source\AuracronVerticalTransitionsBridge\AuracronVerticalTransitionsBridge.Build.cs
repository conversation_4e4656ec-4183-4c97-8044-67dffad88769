﻿using UnrealBuildTool;
public class AuracronVerticalTransitionsBridge : ModuleRules
{
    public AuracronVerticalTransitionsBridge(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;
        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "CoreUObject",
                "Engine",
                "NavigationSystem",
                "GameplayTasks",
                "UMG",
                "Slate",
                "SlateCore",
                "RenderCore",
                "RHI",
                "NiagaraCore",
                "NiagaraShader",
                "CinematicCamera",
                "MovieScene",
                "MovieSceneTracks",
                "ChaosCore",
                "PhysicsCore"
            }
        );
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "AnimGraphRuntime",
                "AnimationCore",
                "AudioMixer",
                "AudioExtensions",
                "SignificanceManager",
                "GameplayAbilities",
                "GameplayTags",
                "GameplayTasks"
            }
        );
        
        // Editor-only dependencies
        if (Target.Type == TargetType.Editor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "ToolMenus",
                    "EditorStyle",
                    "EditorWidgets",
                    "UnrealEd",
                    "LevelEditor",
                    "PropertyEditor",
                    "DetailCustomizations",
                    "ComponentVisualizers"
                }
            );
        }
        // Enable RTTI for this module
        bUseRTTI = true;
        // Enable exceptions for this module
        bEnableExceptions = true;
        // Optimization settings
        OptimizeCode = CodeOptimization.InShippingBuildsOnly;
        // Include paths
        PublicIncludePaths.AddRange(new string[] 
        {
            "AuracronVerticalTransitionsBridge/Public"
        });
        PrivateIncludePaths.AddRange(new string[] 
        {
            "AuracronVerticalTransitionsBridge/Private"
        });
    }
}


