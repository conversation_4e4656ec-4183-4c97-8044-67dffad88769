﻿// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Tutorial Bridge
// IntegraÃ§Ã£o C++ para tutorial progressivo usando APIs modernas do UE 5.6

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Components/ActorComponent.h"
// Forward declaration for ModularGameplay compatibility
class UGameFrameworkComponent;
#include "CommonActivatableWidget.h"
#include "CommonUserWidget.h"
#include "Components/Widget.h"
#include "Blueprint/UserWidget.h"
#include "GameplayTags/Classes/GameplayTagContainer.h"
#include "Engine/DataAsset.h"
#include "Engine/DataTable.h"
#include "UObject/SoftObjectPtr.h"
#include "Net/Core/PushModel/PushModel.h"
#include "TimerManager.h"
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "Internationalization/Text.h"
#include "Internationalization/Internationalization.h"
#include "AuracronTutorialBridge.generated.h"

/**
 * EnumeraÃ§Ã£o para tipos de tutorial
 */
UENUM(BlueprintType)
enum class EAuracronTutorialType : uint8
{
    None                UMETA(DisplayName = "None"),
    Onboarding          UMETA(DisplayName = "Onboarding"),
    BasicMovement       UMETA(DisplayName = "Basic Movement"),
    CombatBasics        UMETA(DisplayName = "Combat Basics"),
    AbilityUsage        UMETA(DisplayName = "Ability Usage"),
    SigiloSystem        UMETA(DisplayName = "Sigilo System"),
    RealmNavigation     UMETA(DisplayName = "Realm Navigation"),
    TeamPlay            UMETA(DisplayName = "Team Play"),
    Objectives          UMETA(DisplayName = "Objectives"),
    Advanced            UMETA(DisplayName = "Advanced"),
    Reboarding          UMETA(DisplayName = "Reboarding")
};

/**
 * EnumeraÃ§Ã£o para estado do tutorial
 */
UENUM(BlueprintType)
enum class EAuracronTutorialState : uint8
{
    NotStarted          UMETA(DisplayName = "Not Started"),
    InProgress          UMETA(DisplayName = "In Progress"),
    Completed           UMETA(DisplayName = "Completed"),
    Skipped             UMETA(DisplayName = "Skipped"),
    Failed              UMETA(DisplayName = "Failed"),
    Paused              UMETA(DisplayName = "Paused")
};

/**
 * EnumeraÃ§Ã£o para tipos de passo do tutorial
 */
UENUM(BlueprintType)
enum class EAuracronTutorialStepType : uint8
{
    Information         UMETA(DisplayName = "Information"),
    Action              UMETA(DisplayName = "Action Required"),
    Demonstration       UMETA(DisplayName = "Demonstration"),
    Practice            UMETA(DisplayName = "Practice"),
    Quiz                UMETA(DisplayName = "Quiz"),
    Checkpoint          UMETA(DisplayName = "Checkpoint"),
    Reward              UMETA(DisplayName = "Reward"),
    Transition          UMETA(DisplayName = "Transition")
};

/**
 * Estrutura para passo do tutorial
 */
USTRUCT(BlueprintType)
struct AURACRONTUTORIALBRIDGE_API FAuracronTutorialStep
{
    GENERATED_BODY()

    /** ID Ãºnico do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FString StepID;

    /** Nome do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FText StepName;

    /** DescriÃ§Ã£o do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FText StepDescription;

    /** Tipo do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    EAuracronTutorialStepType StepType = EAuracronTutorialStepType::Information;

    /** DuraÃ§Ã£o mÃ¡xima do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step", meta = (ClampMin = "1.0", ClampMax = "300.0"))
    float MaxDuration = 30.0f;

    /** Pode ser pulado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    bool bCanBeSkipped = true;

    /** Ã‰ obrigatÃ³rio */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    bool bIsMandatory = false;

    /** AÃ§Ã£o requerida */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FString RequiredAction;

    /** ParÃ¢metros da aÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TMap<FString, FString> ActionParameters;

    /** Widget de UI para o passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TSoftClassPtr<UUserWidget> StepWidget;

    /** LocalizaÃ§Ã£o no mundo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FVector WorldLocation = FVector::ZeroVector;

    /** Ator de foco */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TSoftObjectPtr<AActor> FocusActor;

    /** Usar highlight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    bool bUseHighlight = false;

    /** Cor do highlight */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FLinearColor HighlightColor = FLinearColor::Yellow;

    /** Usar animaÃ§Ã£o de cÃ¢mera */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    bool bUseCameraAnimation = false;

    /** SequÃªncia de cÃ¢mera */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TSoftObjectPtr<ULevelSequence> CameraSequence;

    /** Usar narraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    bool bUseNarration = false;

    /** Ãudio de narraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TSoftObjectPtr<USoundBase> NarrationAudio;

    /** Texto de narraÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FText NarrationText;

    /** CondiÃ§Ãµes de conclusÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TArray<FString> CompletionConditions;

    /** PrÃ©-requisitos */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    TArray<FString> Prerequisites;

    /** Tags do passo */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Step")
    FGameplayTagContainer StepTags;
};

/**
 * Estrutura para configuraÃ§Ã£o de tutorial
 */
USTRUCT(BlueprintType)
struct AURACRONTUTORIALBRIDGE_API FAuracronTutorialConfiguration
{
    GENERATED_BODY()

    /** ID Ãºnico do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FString TutorialID;

    /** Nome do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FText TutorialName;

    /** DescriÃ§Ã£o do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FText TutorialDescription;

    /** Tipo do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    EAuracronTutorialType TutorialType = EAuracronTutorialType::Onboarding;

    /** Passos do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    TArray<FAuracronTutorialStep> TutorialSteps;

    /** DuraÃ§Ã£o estimada */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration", meta = (ClampMin = "1.0", ClampMax = "1800.0"))
    float EstimatedDuration = 300.0f; // 5 minutos

    /** NÃ­vel de dificuldade */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration", meta = (ClampMin = "1", ClampMax = "10"))
    int32 DifficultyLevel = 1;

    /** Pode ser pausado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bCanBePaused = true;

    /** Pode ser reiniciado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bCanBeRestarted = true;

    /** Salvar progresso automaticamente */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bAutoSaveProgress = true;

    /** Usar AI Mentor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bUseAIMentor = true;

    /** Personalidade do AI Mentor */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FString AIMentorPersonality = TEXT("Friendly");

    /** Usar adaptaÃ§Ã£o dinÃ¢mica */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bUseDynamicAdaptation = true;

    /** Usar feedback em tempo real */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bUseRealTimeFeedback = true;

    /** Usar gamificaÃ§Ã£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bUseGamification = true;

    /** Pontos por passo completado */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration", meta = (ClampMin = "1", ClampMax = "1000"))
    int32 PointsPerStep = 10;

    /** Recompensa de conclusÃ£o */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FString CompletionReward;

    /** Usar analytics */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    bool bUseAnalytics = true;

    /** Idiomas suportados */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    TArray<FString> SupportedLanguages;

    /** VersÃ£o do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FString TutorialVersion = TEXT("1.0");

    /** Tags do tutorial */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial Configuration")
    FGameplayTagContainer TutorialTags;
};

/**
 * Classe principal do Bridge para Sistema de Tutorial
 * ResponsÃ¡vel pelo gerenciamento completo de tutoriais progressivos
 */
UCLASS(BlueprintType, Blueprintable, Category = "AURACRON|Tutorial", meta = (DisplayName = "AURACRON Tutorial Bridge", BlueprintSpawnableComponent))
class AURACRONTUTORIALBRIDGE_API UAuracronTutorialBridge : public UGameFrameworkComponent
{
    GENERATED_BODY()

public:
    UAuracronTutorialBridge();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
    virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

    // === Core Tutorial Management ===

    /**
     * Iniciar tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool StartTutorial(const FAuracronTutorialConfiguration& TutorialConfig);

    /**
     * Pausar tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool PauseTutorial();

    /**
     * Retomar tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool ResumeTutorial();

    /**
     * Parar tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool StopTutorial();

    /**
     * Pular tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool SkipTutorial();

    /**
     * Reiniciar tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Core", CallInEditor)
    bool RestartTutorial();

    // === Step Management ===

    /**
     * AvanÃ§ar para prÃ³ximo passo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Steps", CallInEditor)
    bool NextTutorialStep();

    /**
     * Voltar para passo anterior
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Steps", CallInEditor)
    bool PreviousTutorialStep();

    /**
     * Ir para passo especÃ­fico
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Steps", CallInEditor)
    bool GoToTutorialStep(int32 StepIndex);

    /**
     * Completar passo atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Steps", CallInEditor)
    bool CompleteCurrentStep();

    /**
     * Pular passo atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Steps", CallInEditor)
    bool SkipCurrentStep();

    // === AI Mentor ===

    /**
     * Ativar AI Mentor
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|AIMentor", CallInEditor)
    bool ActivateAIMentor();

    /**
     * Desativar AI Mentor
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|AIMentor", CallInEditor)
    bool DeactivateAIMentor();

    /**
     * AI Mentor falar
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|AIMentor", CallInEditor)
    bool AIMentorSpeak(const FText& Message, bool bUseVoice = true);

    /**
     * AI Mentor demonstrar aÃ§Ã£o
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|AIMentor", CallInEditor)
    bool AIMentorDemonstrate(const FString& ActionType, const TMap<FString, FString>& Parameters);

    // === Progress Tracking ===

    /**
     * Salvar progresso do tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Progress", CallInEditor)
    bool SaveTutorialProgress();

    /**
     * Carregar progresso do tutorial
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Progress", CallInEditor)
    bool LoadTutorialProgress();

    /**
     * Obter progresso atual
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Progress", CallInEditor)
    float GetTutorialProgress() const;

    /**
     * Verificar se tutorial foi completado
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Progress", CallInEditor)
    bool IsTutorialCompleted(const FString& TutorialID) const;

    // === Adaptive System ===

    /**
     * Adaptar tutorial ao jogador
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Adaptive", CallInEditor)
    bool AdaptTutorialToPlayer(const TMap<FString, float>& PlayerMetrics);

    /**
     * Ajustar dificuldade
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Adaptive", CallInEditor)
    bool AdjustTutorialDifficulty(int32 NewDifficultyLevel);

    /**
     * Personalizar conteÃºdo
     */
    UFUNCTION(BlueprintCallable, Category = "AURACRON Tutorial|Adaptive", CallInEditor)
    bool PersonalizeTutorialContent(const FString& PlayerProfile);

protected:
    // === Internal Methods ===
    
    /** Inicializar sistema de tutorial */
    bool InitializeTutorialSystem();
    
    /** Configurar UI de tutorial */
    bool SetupTutorialUI();
    
    /** Processar passo atual */
    void ProcessCurrentStep(float DeltaTime);
    
    /** Validar configuraÃ§Ã£o de tutorial */
    bool ValidateTutorialConfiguration(const FAuracronTutorialConfiguration& Config) const;
    
    /** Verificar condiÃ§Ãµes de conclusÃ£o */
    bool CheckCompletionConditions(const FAuracronTutorialStep& Step) const;
    
    /**
     * Verifica condiÃ§Ãµes de gameplay especÃ­ficas
     */
    bool CheckGameplayConditions(const TMap<FString, FString>& Conditions) const;
    
    /**
     * Verifica uma Ãºnica condiÃ§Ã£o de gameplay
     */
    bool CheckSingleCondition(const FString& ConditionType, const FString& ConditionValue) const;

public:
    // === Configuration Properties ===

    /** Tutorial atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    FAuracronTutorialConfiguration CurrentTutorial;

    /** Estado atual do tutorial */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_TutorialState)
    EAuracronTutorialState CurrentTutorialState = EAuracronTutorialState::NotStarted;

    /** Ãndice do passo atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", ReplicatedUsing = OnRep_CurrentStep)
    int32 CurrentStepIndex = 0;

    /** Progresso do tutorial */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    float TutorialProgress = 0.0f;

    /** Tutoriais completados */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
    TArray<FString> CompletedTutorials;

    /** AI Mentor estÃ¡ ativo */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State", Replicated)
    bool bAIMentorActive = false;

    /** Widget de tutorial atual */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UUserWidget> CurrentTutorialWidget;

private:
    // === Internal State ===
    
    /** Sistema inicializado */
    bool bSystemInitialized = false;
    
    /** Timer para passo atual */
    FTimerHandle StepTimer;
    
    /** Timer para timeout */
    FTimerHandle TimeoutTimer;
    
    /** Mutex para thread safety */
    mutable FCriticalSection TutorialMutex;
    
    /** Tempo de inÃ­cio do passo */
    FDateTime StepStartTime;

    // === Replication Callbacks ===
    
    UFUNCTION()
    void OnRep_TutorialState();
    
    UFUNCTION()
    void OnRep_CurrentStep();

public:
    // === Delegates ===
    
    /** Delegate chamado quando tutorial inicia */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTutorialStarted, FAuracronTutorialConfiguration, Tutorial);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Tutorial|Events")
    FOnTutorialStarted OnTutorialStarted;
    
    /** Delegate chamado quando passo Ã© completado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTutorialStepCompleted, int32, StepIndex, FAuracronTutorialStep, Step);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Tutorial|Events")
    FOnTutorialStepCompleted OnTutorialStepCompleted;
    
    /** Delegate chamado quando tutorial Ã© completado */
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTutorialCompleted, FString, TutorialID);
    UPROPERTY(BlueprintAssignable, Category = "AURACRON Tutorial|Events")
    FOnTutorialCompleted OnTutorialCompleted;
};

