// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON PCG Framework - World Partition Bridge Header
// Bridge 3.1: World Partition - Core Setup

#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "Subsystems/EngineSubsystem.h"
// Forward declaration for PCG compatibility
class UAuracronPCGFramework;

// World Partition includes for UE5.6
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionActorDescView.h"
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "GameFramework/WorldSettings.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "Templates/SharedPointer.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Array.h"

#include "AuracronWorldPartition.generated.h"

// Forward declarations
class UAuracronWorldPartitionManager;
class UAuracronWorldPartitionLogger;
class UWorldPartition;
class UDataLayerSubsystem;

// =============================================================================
// WORLD PARTITION TYPES AND ENUMS
// =============================================================================

// World partition states
UENUM(BlueprintType)
enum class EAuracronWorldPartitionState : uint8
{
    Uninitialized           UMETA(DisplayName = "Uninitialized"),
    Initializing            UMETA(DisplayName = "Initializing"),
    Ready                   UMETA(DisplayName = "Ready"),
    Streaming               UMETA(DisplayName = "Streaming"),
    Error                   UMETA(DisplayName = "Error"),
    Shutdown                UMETA(DisplayName = "Shutdown")
};

// Streaming states
UENUM(BlueprintType)
enum class EAuracronStreamingState : uint8
{
    Unloaded                UMETA(DisplayName = "Unloaded"),
    Loading                 UMETA(DisplayName = "Loading"),
    Loaded                  UMETA(DisplayName = "Loaded"),
    Unloading               UMETA(DisplayName = "Unloading"),
    Failed                  UMETA(DisplayName = "Failed")
};

// Cell types
UENUM(BlueprintType)
enum class EAuracronCellType : uint8
{
    Static                  UMETA(DisplayName = "Static"),
    Dynamic                 UMETA(DisplayName = "Dynamic"),
    Streaming               UMETA(DisplayName = "Streaming"),
    AlwaysLoaded            UMETA(DisplayName = "Always Loaded"),
    RuntimeGenerated        UMETA(DisplayName = "Runtime Generated")
};

// Log levels for World Partition
UENUM(BlueprintType)
enum class EAuracronWorldPartitionLogLevel : uint8
{
    None                    UMETA(DisplayName = "None"),
    Error                   UMETA(DisplayName = "Error"),
    Warning                 UMETA(DisplayName = "Warning"),
    Log                     UMETA(DisplayName = "Log"),
    Verbose                 UMETA(DisplayName = "Verbose"),
    VeryVerbose             UMETA(DisplayName = "Very Verbose")
};

// =============================================================================
// WORLD PARTITION CONFIGURATION
// =============================================================================

/**
 * World Partition Configuration
 * Configuration settings for large world management
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronWorldPartitionConfiguration
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition")
    bool bEnableWorldPartition = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition")
    bool bEnableStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World Partition")
    bool bEnableDataLayers = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float StreamingDistance = 10000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    float UnloadingDistance = 15000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Streaming")
    int32 MaxConcurrentStreamingRequests = 8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 DefaultGridSize = 25600; // 256m cells

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 MinGridSize = 6400; // 64m minimum

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
    int32 MaxGridSize = 102400; // 1024m maximum

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableAsyncLoading = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    bool bEnableLevelStreaming = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Performance")
    float StreamingPoolSize = 512.0f; // MB

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bEnableDebugVisualization = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bLogStreamingOperations = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    EAuracronWorldPartitionLogLevel LogLevel = EAuracronWorldPartitionLogLevel::Warning;

    FAuracronWorldPartitionConfiguration()
    {
        bEnableWorldPartition = true;
        bEnableStreaming = true;
        bEnableDataLayers = true;
        StreamingDistance = 10000.0f;
        UnloadingDistance = 15000.0f;
        MaxConcurrentStreamingRequests = 8;
        DefaultGridSize = 25600;
        MinGridSize = 6400;
        MaxGridSize = 102400;
        bEnableAsyncLoading = true;
        bEnableLevelStreaming = true;
        StreamingPoolSize = 512.0f;
        bEnableDebugVisualization = false;
        bLogStreamingOperations = false;
        LogLevel = EAuracronWorldPartitionLogLevel::Warning;
    }
};

// =============================================================================
// CELL INFORMATION
// =============================================================================

/**
 * Cell Information
 * Information about a world partition cell
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronCellInfo
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    FString CellId;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    FIntVector CellCoordinates;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    FBox CellBounds;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    EAuracronCellType CellType = EAuracronCellType::Static;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    EAuracronStreamingState StreamingState = EAuracronStreamingState::Unloaded;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    int32 ActorCount = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    float MemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    float LoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    TArray<FString> DataLayers;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Cell Info")
    FDateTime LastAccessTime;

    FAuracronCellInfo()
    {
        CellType = EAuracronCellType::Static;
        StreamingState = EAuracronStreamingState::Unloaded;
        ActorCount = 0;
        MemoryUsageMB = 0.0f;
        LoadingTime = 0.0f;
        LastAccessTime = FDateTime::Now();
    }
};

// =============================================================================
// STREAMING STATISTICS
// =============================================================================

/**
 * Streaming Statistics
 * Performance and usage statistics for world partition streaming
 */
USTRUCT(BlueprintType)
struct AURACRONWORLDPARTITIONBRIDGE_API FAuracronStreamingStatistics
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 TotalCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 LoadedCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingCells = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float TotalMemoryUsageMB = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float AverageLoadingTime = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 StreamingRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    int32 FailedRequests = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    float StreamingEfficiency = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistics")
    FDateTime LastUpdateTime;

    FAuracronStreamingStatistics()
    {
        TotalCells = 0;
        LoadedCells = 0;
        StreamingCells = 0;
        TotalMemoryUsageMB = 0.0f;
        AverageLoadingTime = 0.0f;
        StreamingRequests = 0;
        FailedRequests = 0;
        StreamingEfficiency = 0.0f;
        LastUpdateTime = FDateTime::Now();
    }

    // Update calculated fields
    void UpdateCalculatedFields();
};

// =============================================================================
// WORLD PARTITION LOGGER
// =============================================================================

/**
 * World Partition Logger
 * Specialized logging system for world partition operations
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionLogger : public UObject
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    static UAuracronWorldPartitionLogger* GetInstance();

    // Logging functions
    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void LogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category = TEXT("WorldPartition"));

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void LogError(const FString& Message, const FString& Category = TEXT("WorldPartition"));

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void LogWarning(const FString& Message, const FString& Category = TEXT("WorldPartition"));

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void LogInfo(const FString& Message, const FString& Category = TEXT("WorldPartition"));

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void LogVerbose(const FString& Message, const FString& Category = TEXT("WorldPartition"));

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void SetLogLevel(EAuracronWorldPartitionLogLevel Level);

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    EAuracronWorldPartitionLogLevel GetLogLevel() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void EnableCategoryLogging(const FString& Category, bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    bool IsCategoryEnabled(const FString& Category) const;

    // Log management
    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void ClearLogs();

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    TArray<FString> GetRecentLogs(int32 MaxCount = 100) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Logger")
    void SaveLogsToFile(const FString& FilePath) const;

private:
    static UAuracronWorldPartitionLogger* Instance;

    UPROPERTY()
    EAuracronWorldPartitionLogLevel CurrentLogLevel = EAuracronWorldPartitionLogLevel::Warning;

    UPROPERTY()
    TSet<FString> EnabledCategories;

    UPROPERTY()
    TArray<FString> LogHistory;

    mutable FCriticalSection LogLock;

    // Internal functions
    bool ShouldLog(EAuracronWorldPartitionLogLevel Level) const;
    FString FormatLogMessage(EAuracronWorldPartitionLogLevel Level, const FString& Message, const FString& Category) const;
    FString LogLevelToString(EAuracronWorldPartitionLogLevel Level) const;
};

// Logging macros for World Partition
#define AURACRON_WP_LOG(Level, Format, ...) \
    if (UAuracronWorldPartitionLogger* Logger = UAuracronWorldPartitionLogger::GetInstance()) \
    { \
        Logger->LogMessage(EAuracronWorldPartitionLogLevel::Level, FString::Printf(Format, ##__VA_ARGS__)); \
    }

#define AURACRON_WP_LOG_ERROR(Format, ...) AURACRON_WP_LOG(Error, Format, ##__VA_ARGS__)
#define AURACRON_WP_LOG_WARNING(Format, ...) AURACRON_WP_LOG(Warning, Format, ##__VA_ARGS__)
#define AURACRON_WP_LOG_INFO(Format, ...) AURACRON_WP_LOG(Log, Format, ##__VA_ARGS__)
#define AURACRON_WP_LOG_VERBOSE(Format, ...) AURACRON_WP_LOG(Verbose, Format, ##__VA_ARGS__)
#define AURACRON_WP_LOG_VVERBOSE(Format, ...) AURACRON_WP_LOG(VeryVerbose, Format, ##__VA_ARGS__)

// =============================================================================
// WORLD PARTITION MANAGER
// =============================================================================

/**
 * World Partition Manager
 * Central manager for large world streaming and partitioning
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONWORLDPARTITIONBRIDGE_API UAuracronWorldPartitionManager : public UEngineSubsystem
{
    GENERATED_BODY()

public:
    // Singleton access
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    static UAuracronWorldPartitionManager* GetInstance();

    // Subsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;

    // Manager lifecycle
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void InitializeWorldPartition(UWorld* World, const FAuracronWorldPartitionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void ShutdownWorldPartition();

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    EAuracronWorldPartitionState GetState() const;

    // World management
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    bool SetupWorldPartition(UWorld* World);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    UWorldPartition* GetWorldPartition() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    UDataLayerSubsystem* GetDataLayerSubsystem() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    UWorldPartitionSubsystem* GetWorldPartitionSubsystem() const;

    // Cell management
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FAuracronCellInfo> GetAllCells() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    FAuracronCellInfo GetCellInfo(const FString& CellId) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FAuracronCellInfo> GetCellsInRadius(const FVector& Location, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FAuracronCellInfo> GetLoadedCells() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FAuracronCellInfo> GetStreamingCells() const;

    // Streaming control
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void RequestCellLoading(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void RequestCellUnloading(const FString& CellId);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void RequestCellsInRadius(const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void SetStreamingSource(const FVector& Location);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void UpdateStreamingState();

    // Data layer management
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void SetDataLayerState(const FString& DataLayerName, bool bIsLoaded);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    bool GetDataLayerState(const FString& DataLayerName) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FString> GetAvailableDataLayers() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    TArray<FString> GetLoadedDataLayers() const;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void SetConfiguration(const FAuracronWorldPartitionConfiguration& Configuration);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    FAuracronWorldPartitionConfiguration GetConfiguration() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void SetStreamingDistance(float Distance);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    float GetStreamingDistance() const;

    // Statistics and monitoring
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    FAuracronStreamingStatistics GetStreamingStatistics() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void ResetStatistics();

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    float GetMemoryUsage() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    int32 GetLoadedCellCount() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    int32 GetTotalCellCount() const;

    // Debug and visualization
    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void EnableDebugVisualization(bool bEnabled);

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    bool IsDebugVisualizationEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void DrawDebugCells(UWorld* World) const;

    UFUNCTION(BlueprintCallable, Category = "World Partition Manager")
    void LogCurrentState() const;

    // Events
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCellLoaded, FString, CellId, float, LoadingTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCellUnloaded, FString, CellId, float, UnloadingTime);
    DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStreamingStateChanged, EAuracronStreamingState, OldState, EAuracronStreamingState, NewState);

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellLoaded OnCellLoaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnCellUnloaded OnCellUnloaded;

    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnStreamingStateChanged OnStreamingStateChanged;

private:
    UPROPERTY()
    bool bIsInitialized = false;

    UPROPERTY()
    EAuracronWorldPartitionState CurrentState = EAuracronWorldPartitionState::Uninitialized;

    UPROPERTY()
    FAuracronWorldPartitionConfiguration Configuration;

    UPROPERTY()
    TWeakObjectPtr<UWorld> ManagedWorld;

    UPROPERTY()
    TWeakObjectPtr<UWorldPartition> WorldPartition;

    UPROPERTY()
    UAuracronWorldPartitionLogger* Logger;

    // Cell tracking
    TMap<FString, FAuracronCellInfo> CellInfoMap;
    TSet<FString> LoadedCells;
    TSet<FString> StreamingCells;

    // Statistics
    FAuracronStreamingStatistics Statistics;
    mutable FCriticalSection StatisticsLock;

    // Thread safety
    mutable FCriticalSection ManagerLock;

    // Internal functions
    void UpdateCellInfo();
    void UpdateStatistics();
    FString GenerateCellId(const FIntVector& Coordinates) const;
    FAuracronCellInfo CreateCellInfo(const FIntVector& Coordinates) const;
    void OnCellLoadedInternal(const FString& CellId, float LoadingTime);
    void OnCellUnloadedInternal(const FString& CellId, float UnloadingTime);
    void ValidateConfiguration();
};
