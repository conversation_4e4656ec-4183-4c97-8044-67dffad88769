/**
 * AuracronAdaptiveJungleAI.h
 * 
 * Advanced AI system for jungle creatures that learns from player behavior patterns
 * and adapts creature behavior, spawning, and environmental responses in real-time.
 * 
 * Features:
 * - Machine learning for player pattern recognition
 * - Dynamic creature behavior adaptation
 * - Procedural spawn optimization
 * - Environmental response system
 * - Performance-aware AI scaling
 * 
 * Uses UE 5.6 modern AI and ML frameworks for production-ready implementation.
 */

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Perception/AIPerceptionComponent.h"
// UE 5.6 Compatible - AISightConfig.h renamed
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "GameplayTagContainer.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffect.h"
#include "TimerManager.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronAdaptiveJungleAI.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class AAuracronJungleCreature;
class UBehaviorTree;
class UBlackboardData;

/**
 * Player behavior pattern types for ML analysis
 */
UENUM(BlueprintType)
enum class EPlayerBehaviorPattern : uint8
{
    Aggressive      UMETA(DisplayName = "Aggressive"),
    Cautious        UMETA(DisplayName = "Cautious"),
    Exploratory     UMETA(DisplayName = "Exploratory"),
    Efficient       UMETA(DisplayName = "Efficient"),
    Social          UMETA(DisplayName = "Social"),
    Stealth         UMETA(DisplayName = "Stealth"),
    Territorial     UMETA(DisplayName = "Territorial"),
    Adaptive        UMETA(DisplayName = "Adaptive")
};

/**
 * Creature adaptation types
 */
UENUM(BlueprintType)
enum class ECreatureAdaptationType : uint8
{
    Behavioral      UMETA(DisplayName = "Behavioral"),
    Statistical     UMETA(DisplayName = "Statistical"),
    Environmental   UMETA(DisplayName = "Environmental"),
    Social          UMETA(DisplayName = "Social"),
    Tactical        UMETA(DisplayName = "Tactical")
};

/**
 * Jungle AI difficulty scaling modes
 */
UENUM(BlueprintType)
enum class EJungleAIDifficulty : uint8
{
    Adaptive        UMETA(DisplayName = "Adaptive"),
    Static          UMETA(DisplayName = "Static"),
    Progressive     UMETA(DisplayName = "Progressive"),
    Reactive        UMETA(DisplayName = "Reactive")
};

/**
 * Player behavior analysis data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronPlayerBehaviorAnalysis
{
    GENERATED_BODY()

    /** Player identifier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    FString PlayerID;

    /** Dominant behavior pattern */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    EPlayerBehaviorPattern DominantPattern;

    /** Secondary behavior patterns */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    TArray<EPlayerBehaviorPattern> SecondaryPatterns;

    /** Aggression level (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float AggressionLevel;

    /** Exploration tendency (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float ExplorationTendency;

    /** Risk tolerance (0.0 to 1.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float RiskTolerance;

    /** Social interaction frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float SocialFrequency;

    /** Skill level estimation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float SkillLevel;

    /** Predictability score */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float PredictabilityScore;

    /** Analysis confidence */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float AnalysisConfidence;

    /** Last analysis time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Analysis")
    float LastAnalysisTime;

    FAuracronPlayerBehaviorAnalysis()
    {
        PlayerID = TEXT("");
        DominantPattern = EPlayerBehaviorPattern::Cautious;
        AggressionLevel = 0.5f;
        ExplorationTendency = 0.5f;
        RiskTolerance = 0.5f;
        SocialFrequency = 0.5f;
        SkillLevel = 0.5f;
        PredictabilityScore = 0.5f;
        AnalysisConfidence = 0.0f;
        LastAnalysisTime = 0.0f;
    }
};

/**
 * Creature adaptation configuration
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronCreatureAdaptation
{
    GENERATED_BODY()

    /** Adaptation type */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    ECreatureAdaptationType AdaptationType;

    /** Target player behavior pattern */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    EPlayerBehaviorPattern TargetPattern;

    /** Adaptation strength (0.0 to 2.0) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationStrength;

    /** Adaptation duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    float AdaptationDuration;

    /** Behavior tree to use */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    TObjectPtr<UBehaviorTree> AdaptedBehaviorTree;

    /** Blackboard overrides */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    TMap<FString, float> BlackboardOverrides;

    /** Gameplay effects to apply */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Adaptation")
    TArray<TSubclassOf<UGameplayEffect>> AdaptationEffects;

    FAuracronCreatureAdaptation()
    {
        AdaptationType = ECreatureAdaptationType::Behavioral;
        TargetPattern = EPlayerBehaviorPattern::Cautious;
        AdaptationStrength = 1.0f;
        AdaptationDuration = 60.0f;
        AdaptedBehaviorTree = nullptr;
    }
};

/**
 * Jungle AI learning data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronJungleAILearningData
{
    GENERATED_BODY()

    /** Total learning sessions */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    int32 TotalLearningSessions;

    /** Model accuracy */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    float ModelAccuracy;

    /** Training iterations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    int32 TrainingIterations;

    /** Data points collected */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    int32 DataPointsCollected;

    /** Last training time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    float LastTrainingTime;

    /** Learning rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    float LearningRate;

    /** Adaptation success rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Learning")
    float AdaptationSuccessRate;

    FAuracronJungleAILearningData()
    {
        TotalLearningSessions = 0;
        ModelAccuracy = 0.5f;
        TrainingIterations = 0;
        DataPointsCollected = 0;
        LastTrainingTime = 0.0f;
        LearningRate = 0.01f;
        AdaptationSuccessRate = 0.5f;
    }
};

/**
 * Auracron Adaptive Jungle AI
 * 
 * Advanced AI system that learns from player behavior and adapts jungle creatures
 * and environmental responses to create dynamic, engaging gameplay experiences.
 * 
 * Key Features:
 * - Real-time player behavior analysis
 * - Machine learning for pattern recognition
 * - Dynamic creature behavior adaptation
 * - Environmental response system
 * - Performance-aware scaling
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronAdaptiveJungleAI : public AActor
{
    GENERATED_BODY()

public:
    AAuracronAdaptiveJungleAI();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // === Core AI Management ===
    
    /** Initialize the adaptive AI system */
    UFUNCTION(BlueprintCallable, Category = "Adaptive AI")
    void InitializeAdaptiveAI();

    /** Update AI learning and adaptation */
    UFUNCTION(BlueprintCallable, Category = "Adaptive AI")
    void UpdateAILearning(float DeltaTime);

    /** Analyze player behavior patterns */
    UFUNCTION(BlueprintCallable, Category = "Adaptive AI")
    FAuracronPlayerBehaviorAnalysis AnalyzePlayerBehavior(APawn* Player);

    /** Apply creature adaptations based on player behavior */
    UFUNCTION(BlueprintCallable, Category = "Adaptive AI")
    void ApplyCreatureAdaptations(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);

    /** Get current AI learning data */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Adaptive AI")
    FAuracronJungleAILearningData GetAILearningData() const;

    // === Player Behavior Analysis ===
    
    /** Start monitoring player behavior */
    UFUNCTION(BlueprintCallable, Category = "Player Analysis")
    void StartPlayerMonitoring(APawn* Player);

    /** Stop monitoring player behavior */
    UFUNCTION(BlueprintCallable, Category = "Player Analysis")
    void StopPlayerMonitoring(APawn* Player);

    /** Update player behavior tracking */
    UFUNCTION(BlueprintCallable, Category = "Player Analysis")
    void UpdatePlayerTracking(APawn* Player, float DeltaTime);

    /** Get player behavior pattern */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Player Analysis")
    EPlayerBehaviorPattern GetPlayerBehaviorPattern(APawn* Player) const;

    // === Creature Adaptation ===
    
    /** Adapt creature to player behavior */
    UFUNCTION(BlueprintCallable, Category = "Creature Adaptation")
    void AdaptCreatureToPlayer(AAuracronJungleCreature* Creature, APawn* Player);

    /** Remove creature adaptations */
    UFUNCTION(BlueprintCallable, Category = "Creature Adaptation")
    void RemoveCreatureAdaptations(AAuracronJungleCreature* Creature);

    /** Update creature AI behavior */
    UFUNCTION(BlueprintCallable, Category = "Creature Adaptation")
    void UpdateCreatureAIBehavior(AAuracronJungleCreature* Creature, const FAuracronCreatureAdaptation& Adaptation);

    // === Creature Management ===

    /** Register creature with AI system */
    UFUNCTION(BlueprintCallable, Category = "Creature Management")
    void RegisterCreature(AAuracronJungleCreature* Creature);

    /** Unregister creature from AI system */
    UFUNCTION(BlueprintCallable, Category = "Creature Management")
    void UnregisterCreature(AAuracronJungleCreature* Creature);

    /** Check if creature is managed by this AI */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Creature Management")
    bool IsCreatureManaged(AAuracronJungleCreature* Creature) const;

    // === Environmental Response ===

    /** Adapt jungle environment to player behavior */
    UFUNCTION(BlueprintCallable, Category = "Environmental Response")
    void AdaptEnvironmentToPlayer(APawn* Player, const FVector& Location);

    /** Update environmental AI responses */
    void UpdateEnvironmentalResponses(float DeltaTime);

    // === Machine Learning ===

    /** Train AI model with collected data */
    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    void TrainAIModel();

    /** Validate AI model performance */
    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    bool ValidateAIModel();

    /** Reset AI learning data */
    UFUNCTION(BlueprintCallable, Category = "Machine Learning")
    void ResetAILearning();

    // === Configuration ===
    
    /** AI difficulty scaling mode */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    EJungleAIDifficulty DifficultyMode;

    /** Base AI learning rate */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    float BaseLearningRate;

    /** Maximum adaptation strength */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    float MaxAdaptationStrength;

    /** Player monitoring radius */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    float MonitoringRadius;

    /** AI update frequency */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    float AIUpdateFrequency;

    /** Enable machine learning */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    bool bEnableMachineLearning;

    /** Enable environmental adaptation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    bool bEnableEnvironmentalAdaptation;

    /** Enable performance scaling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
    bool bEnablePerformanceScaling;

    // === Creature Templates ===
    
    /** Available creature adaptation templates */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Templates")
    TMap<EPlayerBehaviorPattern, FAuracronCreatureAdaptation> AdaptationTemplates;

    /** Behavior trees for different adaptations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Templates")
    TMap<ECreatureAdaptationType, TObjectPtr<UBehaviorTree>> AdaptiveBehaviorTrees;

    /** Blackboard data for AI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Creature Templates")
    TObjectPtr<UBlackboardData> AdaptiveBlackboard;

    // === Events ===
    
    /** Called when AI learns new player pattern */
    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnPlayerPatternLearned(APawn* Player, EPlayerBehaviorPattern Pattern);

    /** Called when creature adaptation is applied */
    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnCreatureAdaptationApplied(AAuracronJungleCreature* Creature, ECreatureAdaptationType AdaptationType);

    /** Called when AI model is retrained */
    UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
    void OnAIModelRetrained(float NewAccuracy);

protected:
    // === Core Components ===
    
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    // === AI State ===
    
    /** Current AI learning data */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI State")
    FAuracronJungleAILearningData AILearningData;

    /** Monitored players */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI State")
    TArray<TObjectPtr<APawn>> MonitoredPlayers;

    /** Player behavior analyses */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI State")
    TMap<TObjectPtr<APawn>, FAuracronPlayerBehaviorAnalysis> PlayerAnalyses;

    /** Active creature adaptations */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI State")
    TMap<TObjectPtr<AAuracronJungleCreature>, FAuracronCreatureAdaptation> ActiveAdaptations;

    /** Jungle creatures under AI control */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI State")
    TArray<TObjectPtr<AAuracronJungleCreature>> ManagedCreatures;

private:
    // === AI Learning Implementation ===
    void InitializeMLSystem();
    void CollectPlayerBehaviorData(APawn* Player);
    void ProcessBehaviorData();
    void UpdateMLModel();
    void ValidateMLPerformance();
    
    // === Player Analysis Implementation ===
    void AnalyzePlayerMovementPatterns(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis);
    void AnalyzePlayerCombatBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis);
    void AnalyzePlayerSocialBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis);
    void AnalyzePlayerExplorationBehavior(APawn* Player, FAuracronPlayerBehaviorAnalysis& Analysis);
    
    // === Creature Adaptation Implementation ===
    void ApplyBehavioralAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void ApplyStatisticalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void ApplyEnvironmentalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void ApplySocialAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void ApplyTacticalAdaptation(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    
    // === Environmental Response Implementation ===
    void UpdateJungleAmbientResponse(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void UpdateCreatureSpawnRates(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void UpdateEnvironmentalHazards(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    void UpdateResourceDistribution(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis);
    
    // === Utility Methods ===
    float CalculateAdaptationStrength(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const;
    ECreatureAdaptationType DetermineOptimalAdaptationType(const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const;
    bool ShouldAdaptCreature(AAuracronJungleCreature* Creature, const FAuracronPlayerBehaviorAnalysis& PlayerAnalysis) const;
    void OptimizeAIPerformance();

    // === Setup and Registration ===
    void SetupAdaptationTemplates();
    void RegisterJungleCreatures();
    void StartMonitoringActivePlayers();
    void ResetCreatureToDefaultBehavior(AAuracronJungleCreature* Creature);

    // === Helper Methods ===
    EPlayerBehaviorPattern DetermineDominantPattern(const FAuracronPlayerBehaviorAnalysis& Analysis) const;
    float CalculateAnalysisConfidence(const FAuracronPlayerBehaviorAnalysis& Analysis) const;
    EPlayerBehaviorPattern PredictBehaviorPattern(const FAuracronPlayerBehaviorAnalysis& Analysis) const;
    float CalculatePatternSimilarity(EPlayerBehaviorPattern Pattern1, EPlayerBehaviorPattern Pattern2) const;
    APawn* FindPlayerByID(const FString& PlayerID) const;
    TArray<AAuracronJungleCreature*> GetCreaturesNearPlayer(APawn* Player, float Radius) const;
    TArray<APawn*> GetPlayersNearLocation(const FVector& Location, float Radius) const;
    float CalculatePlayerActionFrequency(APawn* Player) const;
    void UpdatePlayerSkillEstimate(APawn* Player);
    float CalculateAbilityUsageFrequency(APawn* Player) const;
    float GetPlayerHealthPercentage(APawn* Player) const;
    bool IsPlayerGroupLeader(APawn* Player, const TArray<APawn*>& NearbyPlayers) const;
    float GetPlayerSurvivalTime(APawn* Player) const;
    void UpdateAdaptationSuccessRate();
    void LoadMLData();
    void SaveMLData();
    
    // === Cached References ===
    UPROPERTY()
    TObjectPtr<UAuracronDynamicRealmSubsystem> CachedRealmSubsystem;

    // === ML Data ===
    TArray<FAuracronPlayerBehaviorAnalysis> TrainingDataset;
    TMap<FString, TArray<FVector>> PlayerMovementHistory;
    TMap<FString, TArray<float>> PlayerActionHistory;
    TMap<FString, float> PlayerSkillEstimates;
    
    // === Timers ===
    FTimerHandle AIUpdateTimer;
    FTimerHandle MLTrainingTimer;
    FTimerHandle PerformanceOptimizationTimer;
    
    // === State Tracking ===
    bool bIsInitialized;
    bool bMLModelTrained;
    float LastAIUpdate;
    float LastMLTraining;
    int32 TotalAdaptationsApplied;
};
