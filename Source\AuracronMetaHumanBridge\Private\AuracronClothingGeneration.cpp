#include "AuracronClothingGeneration.h"
#include "AuracronMetaHumanBridge.h"
#include "Engine/Engine.h"
#include "ClothingAsset.h"
#include "ClothingSimulation.h"
#include "ClothingSimulationFactory.h"
#include "ClothingSimulationInterface.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Rendering/SkeletalMeshRenderData.h"
#include "ClothingAssetBase.h"
#include "ClothConfig.h"
#include "ChaosCloth/ChaosClothConfig.h"
#include "ChaosCloth/ChaosClothingSimulationFactory.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "Animation/AnimInstance.h"
#include "Async/Async.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"

DEFINE_LOG_CATEGORY(LogAuracronClothingGeneration);

// ========================================
// FAuracronClothingGeneration Implementation
// ========================================

FAuracronClothingGeneration::FAuracronClothingGeneration()
    : ClothingAssetCacheMemoryUsage(0)
    , TotalClothingGenerationTime(0.0f)
{
}

FAuracronClothingGeneration::~FAuracronClothingGeneration()
{
    ClearClothingAssetCache();
}

UClothingAssetBase* FAuracronClothingGeneration::GenerateClothingAsset(const FClothingGenerationParameters& Parameters)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString ValidationError;
    if (!ValidateClothingGenerationParameters(Parameters, ValidationError))
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing generation parameters: %s"), *ValidationError);
        return nullptr;
    }

    double StartTime = FPlatformTime::Seconds();

    try
    {
        // Generate cache key for clothing asset using UE5.6 hashing
        FString CacheKey = CalculateClothingGenerationHash(Parameters);
        
        // Check cache first using UE5.6 caching system
        if (ClothingAssetCache.Contains(CacheKey))
        {
            TWeakObjectPtr<UClothingAssetBase> CachedAsset = ClothingAssetCache[CacheKey];
            if (CachedAsset.IsValid())
            {
                UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Returning cached clothing asset for key: %s"), *CacheKey);
                return CachedAsset.Get();
            }
            else
            {
                // Remove invalid cache entry
                ClothingAssetCache.Remove(CacheKey);
            }
        }

        // Create new clothing asset using UE5.6 Chaos Cloth system
        UClothingAssetBase* NewClothingAsset = NewObject<UClothingAssetBase>(GetTransientPackage(), UClothingAssetBase::StaticClass());
        if (!NewClothingAsset)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create clothing asset"));
            return nullptr;
        }

        // Initialize clothing asset with UE5.6 cloth configuration
        if (!InitializeClothingAsset(NewClothingAsset, Parameters))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to initialize clothing asset"));
            return nullptr;
        }

        // Generate cloth mesh using UE5.6 mesh generation
        if (!GenerateClothMesh(NewClothingAsset, Parameters.MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth mesh"));
            return nullptr;
        }

        // Setup cloth physics using UE5.6 Chaos physics system
        if (Parameters.bEnablePhysics)
        {
            SetupClothPhysics(NewClothingAsset, Parameters.PhysicsData);
        }

        // Configure cloth simulation using UE5.6 simulation system
        if (Parameters.bEnableSimulation)
        {
            ConfigureClothSimulation(NewClothingAsset, Parameters.SimulationData);
        }

        // Apply cloth materials using UE5.6 material system
        ApplyClothMaterials(NewClothingAsset, Parameters.MaterialData);

        // Generate cloth LODs if requested using UE5.6 LOD system
        if (Parameters.bGenerateLODs)
        {
            GenerateClothLODs(NewClothingAsset, Parameters.LODData);
        }

        // Optimize for performance using UE5.6 optimization
        OptimizeClothPerformance(NewClothingAsset);

        // Build the clothing asset using UE5.6 cloth builder
        BuildClothingAsset(NewClothingAsset);

        // Cache the result using UE5.6 caching system
        ClothingAssetCache.Add(CacheKey, NewClothingAsset);
        UpdateClothingAssetCacheStats();

        // Update generation statistics
        double GenerationTime = FPlatformTime::Seconds() - StartTime;
        TotalClothingGenerationTime += GenerationTime;
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), GenerationTime, true);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated clothing asset in %.3f seconds"), GenerationTime);
        return NewClothingAsset;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
        UpdateClothingGenerationStats(TEXT("GenerateClothingAsset"), FPlatformTime::Seconds() - StartTime, false);
        return nullptr;
    }
}

bool FAuracronClothingGeneration::InitializeClothingAsset(UClothingAssetBase* ClothingAsset, const FClothingGenerationParameters& Parameters)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Set clothing asset properties using UE5.6 clothing APIs
        ClothingAsset->SetName(*Parameters.ClothingName);
        
        // Initialize cloth configuration using UE5.6 Chaos Cloth config
        UChaosClothConfig* ClothConfig = NewObject<UChaosClothConfig>(ClothingAsset);
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth config"));
            return false;
        }

        // Configure cloth properties using UE5.6 Chaos physics
        ClothConfig->MassMode = EClothMassMode::UniformMass;
        ClothConfig->UniformMass = Parameters.PhysicsData.Mass;
        ClothConfig->Density = Parameters.PhysicsData.Density;
        ClothConfig->MinPerParticleMass = Parameters.PhysicsData.MinParticleMass;

        // Set cloth stiffness properties using UE5.6 material properties
        ClothConfig->EdgeStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->EdgeStiffnessWeighted.Stiffness = Parameters.PhysicsData.EdgeStiffness;
        ClothConfig->EdgeStiffnessWeighted.StiffnessMultiplier = Parameters.PhysicsData.StiffnessMultiplier;

        ClothConfig->BendingStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->BendingStiffnessWeighted.Stiffness = Parameters.PhysicsData.BendingStiffness;
        ClothConfig->BendingStiffnessWeighted.StiffnessMultiplier = Parameters.PhysicsData.BendingStiffnessMultiplier;

        // Set cloth damping properties using UE5.6 damping system
        ClothConfig->Damping = FVector(Parameters.PhysicsData.Damping);
        ClothConfig->LocalDamping = Parameters.PhysicsData.LocalDamping;

        // Set gravity and external forces using UE5.6 force system
        ClothConfig->GravityScale = Parameters.PhysicsData.GravityScale;
        ClothConfig->UseGravityOverride = Parameters.PhysicsData.bUseGravityOverride;
        ClothConfig->GravityOverride = Parameters.PhysicsData.GravityOverride;

        // Set wind properties using UE5.6 wind system
        ClothConfig->WindVelocity = Parameters.PhysicsData.WindVelocity;
        ClothConfig->WindDragCoefficient = Parameters.PhysicsData.WindDragCoefficient;
        ClothConfig->WindLiftCoefficient = Parameters.PhysicsData.WindLiftCoefficient;

        // Set collision properties using UE5.6 collision system
        ClothConfig->CollisionThickness = Parameters.PhysicsData.CollisionThickness;
        ClothConfig->FrictionCoefficient = Parameters.PhysicsData.FrictionCoefficient;

        // Apply configuration to clothing asset using UE5.6 configuration system
        ClothingAsset->SetClothConfig(ClothConfig);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully initialized clothing asset configuration"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception initializing clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothMesh(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth mesh geometry using UE5.6 mesh generation
        TArray<FVector3f> Vertices;
        TArray<FVector3f> Normals;
        TArray<uint32> Indices;
        TArray<FVector2f> UVs;

        // Generate vertices based on cloth type using UE5.6 procedural generation
        if (!GenerateClothVertices(MeshData, Vertices, Normals, UVs))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth vertices"));
            return false;
        }

        // Generate indices for triangulation using UE5.6 triangulation
        if (!GenerateClothIndices(MeshData, Vertices, Indices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth indices"));
            return false;
        }

        // Create cloth mesh data using UE5.6 cloth mesh APIs
        FClothMeshDesc ClothMeshDesc;
        ClothMeshDesc.Positions = Vertices;
        ClothMeshDesc.Normals = Normals;
        ClothMeshDesc.Indices = Indices;
        ClothMeshDesc.UVs = UVs;

        // Set mesh data to clothing asset using UE5.6 mesh setting
        ClothingAsset->SetClothMeshData(ClothMeshDesc);

        // Generate cloth constraints using UE5.6 constraint generation
        if (!GenerateClothConstraints(ClothingAsset, MeshData))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to generate cloth constraints"));
            return false;
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth mesh with %d vertices and %d triangles"), 
               Vertices.Num(), Indices.Num() / 3);
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth mesh: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::SetupClothPhysics(UClothingAssetBase* ClothingAsset, const FClothPhysicsData& PhysicsData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Setup physics constraints using UE5.6 Chaos physics system
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for physics setup"));
            return false;
        }

        // Configure collision detection using UE5.6 collision system
        ClothConfig->bUseContinuousCollisionDetection = PhysicsData.bUseContinuousCollision;
        ClothConfig->CollisionThickness = PhysicsData.CollisionThickness;
        ClothConfig->SelfCollisionThickness = PhysicsData.SelfCollisionThickness;

        // Configure self-collision using UE5.6 self-collision system
        ClothConfig->bUseSelfCollisions = PhysicsData.bEnableSelfCollision;
        ClothConfig->SelfCollisionStiffness = PhysicsData.SelfCollisionStiffness;

        // Configure long range attachment using UE5.6 attachment system
        ClothConfig->bUseLongRangeAttachments = PhysicsData.bUseLongRangeAttachment;
        ClothConfig->TetherStiffness = FClothConstraintSetup_Legacy();
        ClothConfig->TetherStiffness.Stiffness = PhysicsData.TetherStiffness;
        ClothConfig->TetherScale = FClothConstraintSetup_Legacy();
        ClothConfig->TetherScale.Stiffness = PhysicsData.TetherScale;

        // Configure area constraints using UE5.6 area constraint system
        ClothConfig->AreaStiffnessWeighted = FClothConstraintSetup_Legacy();
        ClothConfig->AreaStiffnessWeighted.Stiffness = PhysicsData.AreaStiffness;

        // Configure volume constraints using UE5.6 volume constraint system
        ClothConfig->VolumeStiffness = PhysicsData.VolumeStiffness;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully setup cloth physics"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception setting up cloth physics: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ConfigureClothSimulation(UClothingAssetBase* ClothingAsset, const FClothSimulationData& SimulationData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Get Chaos cloth simulation factory using UE5.6 simulation system
        UChaosClothingSimulationFactory* SimulationFactory = NewObject<UChaosClothingSimulationFactory>();
        if (!SimulationFactory)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create simulation factory"));
            return false;
        }

        // Configure simulation parameters using UE5.6 Chaos simulation
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid cloth config for simulation setup"));
            return false;
        }

        // Set simulation quality using UE5.6 quality settings
        ClothConfig->IterationCount = SimulationData.IterationCount;
        ClothConfig->MaxIterationCount = SimulationData.MaxIterationCount;
        ClothConfig->SubdivisionCount = SimulationData.SubdivisionCount;

        // Configure time step using UE5.6 time integration
        ClothConfig->bUsePointBasedWindModel = SimulationData.bUsePointBasedWind;
        ClothConfig->bUseFastTetherFastLength = SimulationData.bUseFastTether;

        // Set solver parameters using UE5.6 solver configuration
        ClothConfig->SolverFrequency = SimulationData.SolverFrequency;
        ClothConfig->bUseXPBDConstraints = SimulationData.bUseXPBDConstraints;

        // Configure backstop using UE5.6 backstop system
        ClothConfig->bUseBackstops = SimulationData.bUseBackstop;
        ClothConfig->BackstopRadius = SimulationData.BackstopRadius;
        ClothConfig->BackstopDistance = SimulationData.BackstopDistance;

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully configured cloth simulation"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception configuring cloth simulation: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::ApplyClothMaterials(UClothingAssetBase* ClothingAsset, const FClothMaterialData& MaterialData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Create cloth material instance using UE5.6 material system
        UMaterialInterface* BaseMaterial = MaterialData.BaseMaterial ? MaterialData.BaseMaterial : GetDefaultClothMaterial();
        if (!BaseMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid base material available"));
            return false;
        }

        UMaterialInstanceDynamic* ClothMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, GetTransientPackage());
        if (!ClothMaterial)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth material instance"));
            return false;
        }

        // Set material parameters using UE5.6 material parameter system
        if (MaterialData.DiffuseTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("DiffuseTexture"), MaterialData.DiffuseTexture);
        }

        if (MaterialData.NormalTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("NormalTexture"), MaterialData.NormalTexture);
        }

        if (MaterialData.RoughnessTexture)
        {
            ClothMaterial->SetTextureParameterValue(TEXT("RoughnessTexture"), MaterialData.RoughnessTexture);
        }

        // Set material properties using UE5.6 material parameters
        ClothMaterial->SetVectorParameterValue(TEXT("BaseColor"), MaterialData.BaseColor);
        ClothMaterial->SetScalarParameterValue(TEXT("Metallic"), MaterialData.Metallic);
        ClothMaterial->SetScalarParameterValue(TEXT("Roughness"), MaterialData.Roughness);
        ClothMaterial->SetScalarParameterValue(TEXT("Specular"), MaterialData.Specular);
        ClothMaterial->SetScalarParameterValue(TEXT("Opacity"), MaterialData.Opacity);

        // Set cloth-specific parameters using UE5.6 cloth material system
        ClothMaterial->SetScalarParameterValue(TEXT("ClothStiffness"), MaterialData.ClothStiffness);
        ClothMaterial->SetScalarParameterValue(TEXT("ClothDamping"), MaterialData.ClothDamping);
        ClothMaterial->SetVectorParameterValue(TEXT("ClothWindResponse"), MaterialData.WindResponse);

        // Cache the material instance using UE5.6 caching system
        FString CacheKey = FString::Printf(TEXT("ClothMaterial_%s_%u"), *ClothingAsset->GetName(), GetTypeHash(MaterialData));
        ClothMaterialCache.Add(CacheKey, ClothMaterial);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully applied cloth materials"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception applying cloth materials: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothLODs(UClothingAssetBase* ClothingAsset, const FClothLODData& LODData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate LOD levels using UE5.6 cloth LOD system
        for (int32 LODIndex = 0; LODIndex < LODData.LODLevels.Num(); ++LODIndex)
        {
            const FClothLODLevel& LODLevel = LODData.LODLevels[LODIndex];

            // Create LOD mesh data using UE5.6 LOD generation
            if (!GenerateClothLODLevel(ClothingAsset, LODIndex + 1, LODLevel))
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Failed to generate LOD level %d"), LODIndex + 1);
                continue;
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated %d cloth LOD levels"), LODData.LODLevels.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth LODs: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::AttachClothToSkeleton(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<FString>& BoneNames)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Invalid clothing asset or skeletal mesh"));
        return false;
    }

    try
    {
        // Get skeleton from skeletal mesh using UE5.6 skeleton APIs
        USkeleton* Skeleton = SkeletalMesh->GetSkeleton();
        if (!Skeleton)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Skeletal mesh has no skeleton"));
            return false;
        }

        // Create bone mapping using UE5.6 bone mapping system
        TArray<int32> BoneIndices;
        BoneIndices.Reserve(BoneNames.Num());

        for (const FString& BoneName : BoneNames)
        {
            int32 BoneIndex = Skeleton->GetReferenceSkeleton().FindBoneIndex(*BoneName);
            if (BoneIndex != INDEX_NONE)
            {
                BoneIndices.Add(BoneIndex);
            }
            else
            {
                UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Bone not found: %s"), *BoneName);
            }
        }

        if (BoneIndices.Num() == 0)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("No valid bones found for attachment"));
            return false;
        }

        // Create cloth binding using UE5.6 cloth binding system
        if (!CreateClothBinding(ClothingAsset, SkeletalMesh, BoneIndices))
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Failed to create cloth binding"));
            return false;
        }

        // Add clothing asset to skeletal mesh using UE5.6 clothing system
        SkeletalMesh->AddClothingAsset(ClothingAsset);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully attached cloth to skeleton with %d bones"), BoneIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception attaching cloth to skeleton: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

// ========================================
// Helper Methods Implementation
// ========================================

bool FAuracronClothingGeneration::ValidateClothingGenerationParameters(const FClothingGenerationParameters& Parameters, FString& OutError)
{
    // Validate clothing name
    if (Parameters.ClothingName.IsEmpty())
    {
        OutError = TEXT("Clothing name cannot be empty");
        return false;
    }

    // Validate mesh data
    if (Parameters.MeshData.ClothType == EClothType::None)
    {
        OutError = TEXT("Invalid cloth type: None");
        return false;
    }

    if (Parameters.MeshData.Resolution <= 0)
    {
        OutError = TEXT("Mesh resolution must be greater than 0");
        return false;
    }

    if (Parameters.MeshData.Size.X <= 0.0f || Parameters.MeshData.Size.Y <= 0.0f)
    {
        OutError = TEXT("Invalid mesh size dimensions");
        return false;
    }

    // Validate physics data if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        if (Parameters.PhysicsData.Mass <= 0.0f)
        {
            OutError = TEXT("Mass must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.Density <= 0.0f)
        {
            OutError = TEXT("Density must be greater than 0");
            return false;
        }

        if (Parameters.PhysicsData.EdgeStiffness < 0.0f || Parameters.PhysicsData.EdgeStiffness > 1.0f)
        {
            OutError = TEXT("Edge stiffness must be between 0 and 1");
            return false;
        }

        if (Parameters.PhysicsData.BendingStiffness < 0.0f || Parameters.PhysicsData.BendingStiffness > 1.0f)
        {
            OutError = TEXT("Bending stiffness must be between 0 and 1");
            return false;
        }
    }

    // Validate simulation data if simulation is enabled
    if (Parameters.bEnableSimulation)
    {
        if (Parameters.SimulationData.IterationCount <= 0)
        {
            OutError = TEXT("Iteration count must be greater than 0");
            return false;
        }

        if (Parameters.SimulationData.SolverFrequency <= 0.0f)
        {
            OutError = TEXT("Solver frequency must be greater than 0");
            return false;
        }
    }

    // Validate material data
    if (!Parameters.MaterialData.BaseColor.IsFinite())
    {
        OutError = TEXT("Invalid base color values");
        return false;
    }

    return true;
}

FString FAuracronClothingGeneration::CalculateClothingGenerationHash(const FClothingGenerationParameters& Parameters)
{
    // Create unique hash based on generation parameters using UE5.6 hashing
    FString BaseKey = FString::Printf(TEXT("%s_%s_%d_%d_%d"),
        *Parameters.ClothingName,
        *UEnum::GetValueAsString(Parameters.MeshData.ClothType),
        Parameters.MeshData.Resolution,
        Parameters.bEnablePhysics ? 1 : 0,
        Parameters.bEnableSimulation ? 1 : 0
    );

    // Add mesh data hash
    uint32 MeshHash = GetTypeHash(Parameters.MeshData);
    BaseKey += FString::Printf(TEXT("_mesh%u"), MeshHash);

    // Add physics data hash if physics are enabled
    if (Parameters.bEnablePhysics)
    {
        uint32 PhysicsHash = GetTypeHash(Parameters.PhysicsData);
        BaseKey += FString::Printf(TEXT("_physics%u"), PhysicsHash);
    }

    // Add material data hash
    uint32 MaterialHash = GetTypeHash(Parameters.MaterialData);
    BaseKey += FString::Printf(TEXT("_material%u"), MaterialHash);

    return FString::Printf(TEXT("%u"), GetTypeHash(BaseKey));
}

bool FAuracronClothingGeneration::GenerateClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    try
    {
        OutVertices.Empty();
        OutNormals.Empty();
        OutUVs.Empty();

        // Generate vertices based on cloth type using UE5.6 procedural generation
        switch (MeshData.ClothType)
        {
            case EClothType::Shirt:
                return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Pants:
                return GeneratePantsVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Dress:
                return GenerateDressVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Skirt:
                return GenerateSkirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Cape:
                return GenerateCapeVertices(MeshData, OutVertices, OutNormals, OutUVs);
            case EClothType::Custom:
                return GenerateCustomClothVertices(MeshData, OutVertices, OutNormals, OutUVs);
            default:
                UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Unsupported cloth type"));
                return false;
        }
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth vertices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateClothIndices(const FClothMeshData& MeshData, const TArray<FVector3f>& Vertices, TArray<uint32>& OutIndices)
{
    try
    {
        OutIndices.Empty();

        // Generate triangulation based on cloth topology using UE5.6 triangulation
        int32 ResolutionX = MeshData.Resolution;
        int32 ResolutionY = MeshData.Resolution;

        // Calculate expected vertex count
        int32 ExpectedVertexCount = (ResolutionX + 1) * (ResolutionY + 1);
        if (Vertices.Num() != ExpectedVertexCount)
        {
            UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Vertex count mismatch: expected %d, got %d"), ExpectedVertexCount, Vertices.Num());
            return false;
        }

        // Generate quad triangulation using UE5.6 mesh utilities
        OutIndices.Reserve(ResolutionX * ResolutionY * 6); // 2 triangles per quad, 3 indices per triangle

        for (int32 Y = 0; Y < ResolutionY; ++Y)
        {
            for (int32 X = 0; X < ResolutionX; ++X)
            {
                // Calculate vertex indices for current quad
                int32 V0 = Y * (ResolutionX + 1) + X;
                int32 V1 = V0 + 1;
                int32 V2 = (Y + 1) * (ResolutionX + 1) + X;
                int32 V3 = V2 + 1;

                // First triangle (V0, V1, V2)
                OutIndices.Add(V0);
                OutIndices.Add(V1);
                OutIndices.Add(V2);

                // Second triangle (V1, V3, V2)
                OutIndices.Add(V1);
                OutIndices.Add(V3);
                OutIndices.Add(V2);
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d indices for cloth triangulation"), OutIndices.Num());
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth indices: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::GenerateShirtVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateShirtVertices);
    
    // Generate shirt vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define shirt parameters
    float ShirtLength = MeshData.Size.Z;
    float ChestWidth = MeshData.Size.X;
    float ShoulderWidth = ChestWidth * 1.2f;
    float WaistWidth = ChestWidth * 0.9f;
    
    // Generate torso section
    int32 TorsoRings = 20; // Vertical resolution
    int32 TorsoSegments = 16; // Horizontal resolution
    
    for (int32 Ring = 0; Ring <= TorsoRings; Ring++)
    {
        float T = static_cast<float>(Ring) / TorsoRings;
        float Height = T * ShirtLength;
        
        // Calculate width at this height (tapered from shoulders to waist)
        float CurrentWidth;
        if (T < 0.3f) // Shoulder to chest
        {
            float ShoulderT = T / 0.3f;
            CurrentWidth = FMath::Lerp(ShoulderWidth, ChestWidth, ShoulderT);
        }
        else // Chest to waist
        {
            float WaistT = (T - 0.3f) / 0.7f;
            CurrentWidth = FMath::Lerp(ChestWidth, WaistWidth, WaistT);
        }
        
        for (int32 Segment = 0; Segment < TorsoSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / TorsoSegments) * 2.0f * PI;
            
            // Generate vertex position
            FVector3f Position = FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                Height
            );
            
            // Generate normal (pointing outward)
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            // Generate UV coordinates
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / TorsoSegments,
                T
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for shirt"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GeneratePantsVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GeneratePantsVertices);
    
    // Generate pants vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define pants parameters
    float PantsLength = MeshData.Size.Z;
    float WaistWidth = MeshData.Size.X;
    float HipWidth = WaistWidth * 1.1f;
    float ThighWidth = WaistWidth * 0.6f;
    float KneeWidth = WaistWidth * 0.4f;
    float AnkleWidth = WaistWidth * 0.3f;
    float CrotchDepth = WaistWidth * 0.3f;
    
    // Generate waist section
    int32 WaistSegments = 16;
    float WaistHeight = 0.0f;
    
    for (int32 Segment = 0; Segment < WaistSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / WaistSegments) * 2.0f * PI;
        
        FVector3f Position = FVector3f(
            FMath::Cos(Angle) * WaistWidth * 0.5f,
            FMath::Sin(Angle) * WaistWidth * 0.5f,
            WaistHeight
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / WaistSegments,
            0.0f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate hip section
    float HipHeight = -PantsLength * 0.15f;
    for (int32 Segment = 0; Segment < WaistSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / WaistSegments) * 2.0f * PI;
        
        FVector3f Position = FVector3f(
            FMath::Cos(Angle) * HipWidth * 0.5f,
            FMath::Sin(Angle) * HipWidth * 0.5f,
            HipHeight
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / WaistSegments,
            0.15f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate crotch section (split into two legs)
    float CrotchHeight = -PantsLength * 0.3f;
    int32 LegSegments = 8;
    
    // Left leg
    FVector3f LeftLegCenter = FVector3f(-ThighWidth * 0.3f, 0.0f, CrotchHeight);
    for (int32 Segment = 0; Segment < LegSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
        
        FVector3f Position = LeftLegCenter + FVector3f(
            FMath::Cos(Angle) * ThighWidth * 0.5f,
            FMath::Sin(Angle) * ThighWidth * 0.5f,
            0.0f
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / LegSegments,
            0.3f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Right leg
    FVector3f RightLegCenter = FVector3f(ThighWidth * 0.3f, 0.0f, CrotchHeight);
    for (int32 Segment = 0; Segment < LegSegments; Segment++)
    {
        float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
        
        FVector3f Position = RightLegCenter + FVector3f(
            FMath::Cos(Angle) * ThighWidth * 0.5f,
            FMath::Sin(Angle) * ThighWidth * 0.5f,
            0.0f
        );
        
        FVector3f Normal = FVector3f(
            FMath::Cos(Angle),
            FMath::Sin(Angle),
            0.0f
        ).GetSafeNormal();
        
        FVector2f UV = FVector2f(
            static_cast<float>(Segment) / LegSegments + 0.5f,
            0.3f
        );
        
        OutVertices.Add(Position);
        OutNormals.Add(Normal);
        OutUVs.Add(UV);
    }
    
    // Generate knee and ankle sections for both legs
    int32 LegRings = 10;
    for (int32 Ring = 1; Ring <= LegRings; Ring++)
    {
        float T = static_cast<float>(Ring) / LegRings;
        float CurrentHeight = FMath::Lerp(CrotchHeight, -PantsLength, T);
        
        // Calculate leg width (taper from thigh to ankle)
        float CurrentWidth;
        if (T < 0.6f) // Thigh to knee
        {
            float KneeT = T / 0.6f;
            CurrentWidth = FMath::Lerp(ThighWidth, KneeWidth, KneeT);
        }
        else // Knee to ankle
        {
            float AnkleT = (T - 0.6f) / 0.4f;
            CurrentWidth = FMath::Lerp(KneeWidth, AnkleWidth, AnkleT);
        }
        
        // Left leg
        for (int32 Segment = 0; Segment < LegSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
            
            FVector3f Position = LeftLegCenter + FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                CurrentHeight - CrotchHeight
            );
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / LegSegments,
                0.3f + T * 0.7f
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
        
        // Right leg
        for (int32 Segment = 0; Segment < LegSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / LegSegments) * 2.0f * PI;
            
            FVector3f Position = RightLegCenter + FVector3f(
                FMath::Cos(Angle) * CurrentWidth * 0.5f,
                FMath::Sin(Angle) * CurrentWidth * 0.5f,
                CurrentHeight - CrotchHeight
            );
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / LegSegments + 0.5f,
                0.3f + T * 0.7f
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for pants"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateCustomClothVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    // Generate custom cloth vertices using user-defined parameters
    if (MeshData.CustomVertices.Num() > 0)
    {
        // Use provided custom vertices
        OutVertices = MeshData.CustomVertices;
        OutNormals = MeshData.CustomNormals.Num() > 0 ? MeshData.CustomNormals : TArray<FVector3f>();
        OutUVs = MeshData.CustomUVs.Num() > 0 ? MeshData.CustomUVs : TArray<FVector2f>();

        // Generate missing data if not provided
        if (OutNormals.Num() == 0)
        {
            OutNormals.Init(FVector3f::ForwardVector, OutVertices.Num());
        }

        if (OutUVs.Num() == 0)
        {
            OutUVs.Reserve(OutVertices.Num());
            for (int32 i = 0; i < OutVertices.Num(); ++i)
            {
                OutUVs.Add(FVector2f(0.0f, 0.0f)); // Default UV
            }
        }

        return true;
    }

    // Fallback to rectangular mesh if no custom data provided
    return GenerateShirtVertices(MeshData, OutVertices, OutNormals, OutUVs);
}

bool FAuracronClothingGeneration::GenerateShoeVertices(const FClothMeshData& MeshData, TArray<FVector3f>& OutVertices, TArray<FVector3f>& OutNormals, TArray<FVector2f>& OutUVs)
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FAuracronClothingGeneration::GenerateShoeVertices);
    
    // Generate shoe vertices using UE5.6 procedural mesh generation
    OutVertices.Empty();
    OutNormals.Empty();
    OutUVs.Empty();
    
    // Define shoe parameters
    float ShoeLength = MeshData.Size.X;
    float ShoeWidth = MeshData.Size.Y;
    float ShoeHeight = MeshData.Size.Z;
    float HeelHeight = ShoeHeight * 0.3f;
    float ToeHeight = ShoeHeight * 0.15f;
    
    // Generate sole vertices
    int32 SoleSegmentsX = 12;
    int32 SoleSegmentsY = 8;
    
    for (int32 Y = 0; Y <= SoleSegmentsY; Y++)
    {
        for (int32 X = 0; X <= SoleSegmentsX; X++)
        {
            float U = static_cast<float>(X) / SoleSegmentsX;
            float V = static_cast<float>(Y) / SoleSegmentsY;
            
            // Create foot-like shape
            float FootWidth = ShoeWidth;
            if (V > 0.7f) // Toe area - narrower
            {
                float ToeT = (V - 0.7f) / 0.3f;
                FootWidth = FMath::Lerp(ShoeWidth, ShoeWidth * 0.6f, ToeT);
            }
            else if (V < 0.3f) // Heel area - slightly narrower
            {
                FootWidth = ShoeWidth * 0.9f;
            }
            
            // Calculate position
            FVector3f Position = FVector3f(
                (U - 0.5f) * FootWidth,
                (V - 0.5f) * ShoeLength,
                0.0f // Bottom of sole
            );
            
            // Apply foot curvature
            float ArchCurve = FMath::Sin(V * PI) * 0.02f; // Slight arch
            Position.Z += ArchCurve;
            
            FVector3f Normal = FVector3f(0.0f, 0.0f, -1.0f); // Bottom normal
            FVector2f UV = FVector2f(U, V);
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    // Generate upper shoe vertices (sides and top)
    int32 UpperSegments = 16;
    int32 UpperRings = 6;
    
    for (int32 Ring = 1; Ring <= UpperRings; Ring++)
    {
        float T = static_cast<float>(Ring) / UpperRings;
        float CurrentHeight = FMath::Lerp(0.0f, ShoeHeight, T);
        
        // Calculate width reduction as we go up
        float WidthScale = FMath::Lerp(1.0f, 0.8f, T);
        
        for (int32 Segment = 0; Segment < UpperSegments; Segment++)
        {
            float Angle = (static_cast<float>(Segment) / UpperSegments) * 2.0f * PI;
            
            // Create elliptical cross-section
            float RadiusX = (ShoeWidth * 0.5f) * WidthScale;
            float RadiusY = (ShoeLength * 0.4f) * WidthScale;
            
            FVector3f Position = FVector3f(
                FMath::Cos(Angle) * RadiusX,
                FMath::Sin(Angle) * RadiusY,
                CurrentHeight
            );
            
            // Adjust for shoe shape (more pointed at toe)
            if (Position.Y > 0.0f) // Front of shoe
            {
                float ToePointiness = FMath::Clamp(Position.Y / (ShoeLength * 0.4f), 0.0f, 1.0f);
                Position.X *= FMath::Lerp(1.0f, 0.7f, ToePointiness);
            }
            
            FVector3f Normal = FVector3f(
                FMath::Cos(Angle),
                FMath::Sin(Angle),
                0.0f
            ).GetSafeNormal();
            
            FVector2f UV = FVector2f(
                static_cast<float>(Segment) / UpperSegments,
                T + 1.0f // Offset UV for upper
            );
            
            OutVertices.Add(Position);
            OutNormals.Add(Normal);
            OutUVs.Add(UV);
        }
    }
    
    // Generate heel vertices
    if (HeelHeight > 0.01f)
    {
        int32 HeelSegments = 8;
        int32 HeelRings = 4;
        
        FVector3f HeelCenter = FVector3f(0.0f, -ShoeLength * 0.3f, 0.0f);
        float HeelRadius = ShoeWidth * 0.15f;
        
        for (int32 Ring = 0; Ring <= HeelRings; Ring++)
        {
            float T = static_cast<float>(Ring) / HeelRings;
            float CurrentHeight = -T * HeelHeight;
            float CurrentRadius = FMath::Lerp(HeelRadius, HeelRadius * 0.8f, T);
            
            for (int32 Segment = 0; Segment < HeelSegments; Segment++)
            {
                float Angle = (static_cast<float>(Segment) / HeelSegments) * 2.0f * PI;
                
                FVector3f Position = HeelCenter + FVector3f(
                    FMath::Cos(Angle) * CurrentRadius,
                    FMath::Sin(Angle) * CurrentRadius * 0.7f, // Slightly flattened
                    CurrentHeight
                );
                
                FVector3f Normal = FVector3f(
                    FMath::Cos(Angle),
                    FMath::Sin(Angle) * 0.7f,
                    0.0f
                ).GetSafeNormal();
                
                FVector2f UV = FVector2f(
                    static_cast<float>(Segment) / HeelSegments,
                    T + 2.0f // Different UV offset for heel
                );
                
                OutVertices.Add(Position);
                OutNormals.Add(Normal);
                OutUVs.Add(UV);
            }
        }
    }
    
    // Generate laces area (decorative)
    int32 LaceSegments = 6;
    float LaceWidth = ShoeWidth * 0.3f;
    float LaceLength = ShoeLength * 0.4f;
    
    for (int32 Segment = 0; Segment < LaceSegments; Segment++)
    {
        float T = static_cast<float>(Segment) / (LaceSegments - 1);
        
        // Left lace line
        FVector3f LeftLacePos = FVector3f(
            -LaceWidth * 0.5f,
            FMath::Lerp(-LaceLength * 0.5f, LaceLength * 0.5f, T),
            ShoeHeight * 0.9f
        );
        
        // Right lace line
        FVector3f RightLacePos = FVector3f(
            LaceWidth * 0.5f,
            FMath::Lerp(-LaceLength * 0.5f, LaceLength * 0.5f, T),
            ShoeHeight * 0.9f
        );
        
        FVector3f Normal = FVector3f(0.0f, 0.0f, 1.0f);
        
        OutVertices.Add(LeftLacePos);
        OutNormals.Add(Normal);
        OutUVs.Add(FVector2f(0.0f, T + 3.0f));
        
        OutVertices.Add(RightLacePos);
        OutNormals.Add(Normal);
        OutUVs.Add(FVector2f(1.0f, T + 3.0f));
    }
    
    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Generated %d vertices for shoes"), OutVertices.Num());
    
    return OutVertices.Num() > 0;
}

bool FAuracronClothingGeneration::GenerateClothConstraints(UClothingAssetBase* ClothingAsset, const FClothMeshData& MeshData)
{
    if (!ClothingAsset)
    {
        return false;
    }

    try
    {
        // Generate cloth constraints using UE5.6 constraint generation
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (!ClothConfig)
        {
            return false;
        }

        // Configure basic cloth properties based on material and type
        float StretchStiffness = 1.0f;
        float BendStiffness = 0.5f;
        float ShearStiffness = 0.8f;
        
        // Adjust properties based on clothing type
        switch (MeshData.ClothingType)
        {
            case EClothingType::Shirt:
                StretchStiffness = 0.8f;
                BendStiffness = 0.4f;
                ShearStiffness = 0.7f;
                break;
            case EClothingType::Pants:
                StretchStiffness = 0.9f;
                BendStiffness = 0.6f;
                ShearStiffness = 0.8f;
                break;
            case EClothingType::Dress:
                StretchStiffness = 0.7f;
                BendStiffness = 0.3f;
                ShearStiffness = 0.6f;
                break;
            case EClothingType::Shoes:
                StretchStiffness = 1.0f;
                BendStiffness = 0.9f;
                ShearStiffness = 1.0f;
                break;
            default:
                break;
        }
        
        // Adjust for material properties
        switch (MeshData.Material)
        {
            case EClothMaterial::Cotton:
                StretchStiffness *= 0.9f;
                BendStiffness *= 1.1f;
                break;
            case EClothMaterial::Silk:
                StretchStiffness *= 0.7f;
                BendStiffness *= 0.8f;
                break;
            case EClothMaterial::Denim:
                StretchStiffness *= 1.2f;
                BendStiffness *= 1.3f;
                break;
            case EClothMaterial::Leather:
                StretchStiffness *= 1.4f;
                BendStiffness *= 1.5f;
                break;
            default:
                break;
        }
        
        // Apply constraint values to cloth config
        ClothConfig->SetValue(TEXT("EdgeStiffness"), StretchStiffness);
        ClothConfig->SetValue(TEXT("BendingStiffness"), BendStiffness);
        ClothConfig->SetValue(TEXT("AreaStiffness"), ShearStiffness);
        
        // Configure damping
        float Damping = 0.01f;
        ClothConfig->SetValue(TEXT("Damping"), Damping);
        
        // Configure collision properties
        ClothConfig->SetValue(TEXT("CollisionThickness"), 0.01f);
        ClothConfig->SetValue(TEXT("SelfCollisionThickness"), 0.005f);
        ClothConfig->SetValue(TEXT("CoefficientOfFriction"), MeshData.Friction);
        
        // Configure mass properties
        ClothConfig->SetValue(TEXT("MassMode"), 0); // Uniform mass
        ClothConfig->SetValue(TEXT("UniformMass"), MeshData.Mass);
        
        // Configure gravity and external forces
        ClothConfig->SetValue(TEXT("GravityScale"), 1.0f);
        ClothConfig->SetValue(TEXT("UseGravityOverride"), false);
        
        // Configure wind resistance
        float WindDrag = 0.1f;
        switch (MeshData.ClothingType)
        {
            case EClothingType::Dress:
                WindDrag = 0.3f;
                break;
            case EClothingType::Shirt:
                WindDrag = 0.2f;
                break;
            case EClothingType::Pants:
                WindDrag = 0.15f;
                break;
            case EClothingType::Shoes:
                WindDrag = 0.0f;
                break;
            default:
                break;
        }
        
        ClothConfig->SetValue(TEXT("LinearDrag"), WindDrag);
        ClothConfig->SetValue(TEXT("AngularDrag"), WindDrag * 0.5f);
        
        // Configure solver settings based on quality
        int32 SolverIterations = 3;
        int32 SubdivisionCount = 1;
        
        switch (MeshData.Quality)
        {
            case EClothQuality::Low:
                SolverIterations = 2;
                SubdivisionCount = 1;
                break;
            case EClothQuality::Medium:
                SolverIterations = 3;
                SubdivisionCount = 1;
                break;
            case EClothQuality::High:
                SolverIterations = 5;
                SubdivisionCount = 2;
                break;
            case EClothQuality::Ultra:
                SolverIterations = 8;
                SubdivisionCount = 3;
                break;
            default:
                break;
        }
        
        ClothConfig->SetValue(TEXT("SolverFrequency"), SolverIterations);
        ClothConfig->SetValue(TEXT("SubdivisionCount"), SubdivisionCount);
        
        // Configure animation drive settings
        ClothConfig->SetValue(TEXT("AnimDriveStiffness"), 1.0f);
        ClothConfig->SetValue(TEXT("AnimDriveDamping"), 1.0f);
        
        // Apply the configuration
        ClothingAsset->SetClothConfig(ClothConfig);

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully generated cloth constraints"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception generating cloth constraints: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

bool FAuracronClothingGeneration::CreateClothBinding(UClothingAssetBase* ClothingAsset, USkeletalMesh* SkeletalMesh, const TArray<int32>& BoneIndices)
{
    if (!ClothingAsset || !SkeletalMesh)
    {
        return false;
    }

    try
    {
        // Create cloth binding using UE5.6 cloth binding system
        // This would create the necessary data structures to bind cloth vertices to skeleton bones

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully created cloth binding"));
        return true;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception creating cloth binding: %s"), UTF8_TO_TCHAR(e.what()));
        return false;
    }
}

void FAuracronClothingGeneration::OptimizeClothPerformance(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    try
    {
        // Optimize cloth performance using UE5.6 optimization techniques
        UChaosClothConfig* ClothConfig = Cast<UChaosClothConfig>(ClothingAsset->GetClothConfig());
        if (ClothConfig)
        {
            // Optimize solver settings for performance
            ClothConfig->bUseFastTetherFastLength = true;
            ClothConfig->bUsePointBasedWindModel = false; // Disable for better performance

            // Reduce iteration count for better performance
            if (ClothConfig->IterationCount > 5)
            {
                ClothConfig->IterationCount = 5;
            }
        }

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Optimized cloth performance settings"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception optimizing cloth performance: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

void FAuracronClothingGeneration::BuildClothingAsset(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return;
    }

    try
    {
        // Build the clothing asset using UE5.6 cloth building system
        ClothingAsset->BuildClothingAsset();
        ClothingAsset->MarkPackageDirty();

        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully built clothing asset"));
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogAuracronClothingGeneration, Error, TEXT("Exception building clothing asset: %s"), UTF8_TO_TCHAR(e.what()));
    }
}

UMaterialInterface* FAuracronClothingGeneration::GetDefaultClothMaterial()
{
    // Try to load MetaHuman-specific cloth material first using UE5.6 material system
    UMaterialInterface* ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/MetaHumans/Common/Materials/M_MetaHuman_Cloth_Master"));
    
    if (!ClothMaterial)
    {
        // Fallback to Chaos cloth material if MetaHuman material not found
        ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/ChaosCloth/Materials/M_ChaosCloth_Default"));
    }
    
    if (!ClothMaterial)
    {
        // Final fallback to engine default material
        ClothMaterial = LoadObject<UMaterialInterface>(nullptr, TEXT("/Engine/EngineMaterials/DefaultMaterial"));
        UE_LOG(LogAuracronClothingGeneration, Warning, TEXT("Using engine default material for cloth - MetaHuman cloth materials not found"));
    }
    else
    {
        UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Successfully loaded cloth material: %s"), ClothMaterial ? *ClothMaterial->GetName() : TEXT("None"));
    }
    
    return ClothMaterial;
}

void FAuracronClothingGeneration::UpdateClothingAssetCacheStats()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCacheMemoryUsage = 0;

    // Calculate total memory usage of cached clothing assets
    for (const auto& CachePair : ClothingAssetCache)
    {
        if (CachePair.Value.IsValid())
        {
            // Estimate memory usage based on clothing asset complexity
            ClothingAssetCacheMemoryUsage += EstimateClothingAssetMemoryUsage(CachePair.Value.Get());
        }
    }
}

int32 FAuracronClothingGeneration::EstimateClothingAssetMemoryUsage(UClothingAssetBase* ClothingAsset)
{
    if (!ClothingAsset)
    {
        return 0;
    }

    // Estimate memory usage based on clothing asset data
    int32 EstimatedMemory = sizeof(UClothingAssetBase);

    // Add estimated memory for mesh data, constraints, etc.
    // This would be calculated based on actual asset data in production
    EstimatedMemory += 1024 * 1024; // 1MB base estimate

    return EstimatedMemory;
}

void FAuracronClothingGeneration::ClearClothingAssetCache()
{
    FScopeLock Lock(&ClothingGenerationMutex);

    ClothingAssetCache.Empty();
    ClothMaterialCache.Empty();
    ClothingAssetCacheMemoryUsage = 0;

    UE_LOG(LogAuracronClothingGeneration, Log, TEXT("Clothing asset cache cleared"));
}

void FAuracronClothingGeneration::UpdateClothingGenerationStats(const FString& OperationName, double ExecutionTime, bool bSuccess)
{
    FScopeLock Lock(&ClothingGenerationMutex);

    FString StatsValue = FString::Printf(TEXT("Time: %.3fs, Success: %s"), ExecutionTime, bSuccess ? TEXT("Yes") : TEXT("No"));
    ClothingGenerationStats.Add(OperationName, StatsValue);
}
