#include "AuracronRealmManager.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronDynamicRealmSubsystem.h"
#include "AuracronLayerComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "PCGComponent.h"
#include "Landscape/Landscape.h"
#include "Foliage/FoliageInstancedStaticMeshComponent.h"
#include "TimerManager.h"

AAuracronRealmManager::AAuracronRealmManager()
{
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;
    
    // Create root component
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    RootComponent = RootSceneComponent;
    
    // Create layer component
    LayerComponent = CreateDefaultSubobject<UAuracronLayerComponent>(TEXT("LayerComponent"));
    
    // Initialize default values
    ManagedLayer = EAuracronRealmLayer::Terrestrial;
    bAutoGenerateContent = true;
    bEnableEvolution = true;
    bIsInitialized = false;
    bIsVisible = true;
    CurrentLODLevel = 0;
    LastUpdateTime = 0.0f;
    FrameTime = 0.0f;
    DrawCalls = 0;
    MemoryUsage = 0.0f;
    
    // Set default environment settings
    EnvironmentSettings.AmbientLightColor = FLinearColor::White;
    EnvironmentSettings.AmbientLightIntensity = 1.0f;
    EnvironmentSettings.FogColor = FLinearColor::Gray;
    EnvironmentSettings.FogDensity = 0.02f;
    EnvironmentSettings.FogStartDistance = 1000.0f;
    EnvironmentSettings.bHasWeatherEffects = true;
    EnvironmentSettings.WeatherIntensity = 0.5f;
    EnvironmentSettings.GravityScale = 1.0f;
    EnvironmentSettings.WindDirection = FVector(1.0f, 0.0f, 0.0f);
    EnvironmentSettings.WindStrength = 100.0f;
}

void AAuracronRealmManager::BeginPlay()
{
    Super::BeginPlay();
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Manager Begin Play - Layer: %d"), (int32)ManagedLayer);
    
    // Initialize the layer
    InitializeLayer();
    
    // Register with realm subsystem
    if (UWorld* World = GetWorld())
    {
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            RealmSubsystem->RegisterActorToLayer(this, ManagedLayer);
        }
    }
    
    // Start update timers
    GetWorld()->GetTimerManager().SetTimer(ContentUpdateTimer, [this]()
    {
        if (bEnableEvolution)
        {
            UpdateLayerEvolution(ERealmEvolutionPhase::Despertar); // Will be updated by subsystem
        }
    }, 5.0f, true);
    
    GetWorld()->GetTimerManager().SetTimer(PerformanceUpdateTimer, [this]()
    {
        UpdatePerformanceMetrics();
        OptimizeLayerPerformance();
    }, 2.0f, true);
    
    GetWorld()->GetTimerManager().SetTimer(EnvironmentUpdateTimer, [this]()
    {
        UpdateWeatherEffects(2.0f);
    }, 2.0f, true);
    
    bIsInitialized = true;
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Manager Initialized Successfully - Layer: %d"), (int32)ManagedLayer);
}

void AAuracronRealmManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    AURACRON_REALM_LOG(Log, TEXT("Realm Manager End Play - Layer: %d"), (int32)ManagedLayer);
    
    // Clear timers
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().ClearTimer(ContentUpdateTimer);
        World->GetTimerManager().ClearTimer(PerformanceUpdateTimer);
        World->GetTimerManager().ClearTimer(EnvironmentUpdateTimer);
    }
    
    // Despawn content
    DespawnRealmContent();
    
    // Unregister from realm subsystem
    if (UWorld* World = GetWorld())
    {
        if (UAuracronDynamicRealmSubsystem* RealmSubsystem = World->GetSubsystem<UAuracronDynamicRealmSubsystem>())
        {
            RealmSubsystem->UnregisterActorFromLayer(this, ManagedLayer);
        }
    }
    
    bIsInitialized = false;
    
    Super::EndPlay(EndPlayReason);
}

void AAuracronRealmManager::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
    
    if (!bIsInitialized)
    {
        return;
    }
    
    LastUpdateTime += DeltaTime;
    
    // Update layer-specific behavior
    switch (ManagedLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            UpdateTerrestrialBehavior(DeltaTime);
            break;
        case EAuracronRealmLayer::Celestial:
            UpdateCelestialBehavior(DeltaTime);
            break;
        case EAuracronRealmLayer::Abyssal:
            UpdateAbyssalBehavior(DeltaTime);
            break;
        default:
            break;
    }
    
    // Update performance metrics
    FrameTime = DeltaTime * 1000.0f; // Convert to milliseconds
}

void AAuracronRealmManager::InitializeLayer()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Layer: %d"), (int32)ManagedLayer);
    
    // Layer-specific initialization
    switch (ManagedLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            InitializeTerrestrialLayer();
            break;
        case EAuracronRealmLayer::Celestial:
            InitializeCelestialLayer();
            break;
        case EAuracronRealmLayer::Abyssal:
            InitializeAbyssalLayer();
            break;
        default:
            AURACRON_REALM_LOG(Error, TEXT("Unknown layer type: %d"), (int32)ManagedLayer);
            return;
    }
    
    // Apply environmental effects
    ApplyEnvironmentalEffects();
    
    // Generate content if auto-generation is enabled
    if (bAutoGenerateContent)
    {
        GenerateLayerContent();
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Layer Initialized: %d"), (int32)ManagedLayer);
}

void AAuracronRealmManager::InitializeTerrestrialLayer()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Terrestrial Layer (Planície Radiante)"));
    
    // Set layer component
    if (LayerComponent)
    {
        LayerComponent->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
    }
    
    // Set position at terrestrial height
    SetActorLocation(FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::TERRESTRIAL_LAYER_HEIGHT));
    
    // Configure environment for terrestrial layer
    EnvironmentSettings.AmbientLightColor = FLinearColor(1.0f, 0.95f, 0.8f, 1.0f); // Warm sunlight
    EnvironmentSettings.AmbientLightIntensity = 1.2f;
    EnvironmentSettings.FogColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Light blue
    EnvironmentSettings.FogDensity = 0.01f;
    EnvironmentSettings.GravityScale = 1.0f;
    EnvironmentSettings.bHasWeatherEffects = true;
    EnvironmentSettings.WeatherIntensity = 0.6f;
    
    AURACRON_REALM_LOG(Log, TEXT("Terrestrial Layer Initialized"));
}

void AAuracronRealmManager::InitializeCelestialLayer()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Celestial Layer (Firmamento Zephyr)"));
    
    // Set layer component
    if (LayerComponent)
    {
        LayerComponent->SetCurrentLayer(EAuracronRealmLayer::Celestial);
    }
    
    // Set position at celestial height
    SetActorLocation(FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::CELESTIAL_LAYER_HEIGHT));
    
    // Configure environment for celestial layer
    EnvironmentSettings.AmbientLightColor = FLinearColor(0.8f, 0.9f, 1.0f, 1.0f); // Cool ethereal light
    EnvironmentSettings.AmbientLightIntensity = 0.8f;
    EnvironmentSettings.FogColor = FLinearColor(0.9f, 0.95f, 1.0f, 1.0f); // Ethereal white
    EnvironmentSettings.FogDensity = 0.03f;
    EnvironmentSettings.GravityScale = 0.6f; // Reduced gravity
    EnvironmentSettings.bHasWeatherEffects = true;
    EnvironmentSettings.WeatherIntensity = 0.8f;
    EnvironmentSettings.WindStrength = 200.0f; // Stronger winds
    
    AURACRON_REALM_LOG(Log, TEXT("Celestial Layer Initialized"));
}

void AAuracronRealmManager::InitializeAbyssalLayer()
{
    AURACRON_REALM_LOG(Log, TEXT("Initializing Abyssal Layer (Abismo Umbrio)"));
    
    // Set layer component
    if (LayerComponent)
    {
        LayerComponent->SetCurrentLayer(EAuracronRealmLayer::Abyssal);
    }
    
    // Set position at abyssal height
    SetActorLocation(FVector(0.0f, 0.0f, AuracronDynamicRealmConstants::ABYSSAL_LAYER_HEIGHT));
    
    // Configure environment for abyssal layer
    EnvironmentSettings.AmbientLightColor = FLinearColor(0.2f, 0.1f, 0.3f, 1.0f); // Dark purple
    EnvironmentSettings.AmbientLightIntensity = 0.3f;
    EnvironmentSettings.FogColor = FLinearColor(0.1f, 0.05f, 0.2f, 1.0f); // Dark purple fog
    EnvironmentSettings.FogDensity = 0.05f;
    EnvironmentSettings.GravityScale = 1.2f; // Increased gravity
    EnvironmentSettings.bHasWeatherEffects = true;
    EnvironmentSettings.WeatherIntensity = 1.0f;
    EnvironmentSettings.WindStrength = 50.0f; // Reduced winds
    
    AURACRON_REALM_LOG(Log, TEXT("Abyssal Layer Initialized"));
}

void AAuracronRealmManager::GenerateLayerContent()
{
    AURACRON_REALM_LOG(Log, TEXT("Generating Content for Layer: %d"), (int32)ManagedLayer);
    
    // Clear existing content
    DespawnRealmContent();
    
    // Generate layer-specific content
    switch (ManagedLayer)
    {
        case EAuracronRealmLayer::Terrestrial:
            GenerateTerrestrialContent();
            break;
        case EAuracronRealmLayer::Celestial:
            GenerateCelestialContent();
            break;
        case EAuracronRealmLayer::Abyssal:
            GenerateAbyssalContent();
            break;
        default:
            break;
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Content Generated for Layer: %d"), (int32)ManagedLayer);
}

void AAuracronRealmManager::GenerateTerrestrialContent()
{
    AURACRON_REALM_LOG(Log, TEXT("Generating Terrestrial Content (Planície Radiante)"));
    
    if (UWorld* World = GetWorld())
    {
        // Spawn Platôs Cristalinos (Crystal Plateaus)
        for (int32 i = 0; i < 5; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-5000.0f, 5000.0f),
                FMath::RandRange(-5000.0f, 5000.0f),
                FMath::RandRange(-100.0f, 500.0f)
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Plateau = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Plateau);
                
                // Add static mesh component for plateau
                UStaticMeshComponent* PlateauMesh = NewObject<UStaticMeshComponent>(Plateau);
                Plateau->SetRootComponent(PlateauMesh);
                PlateauMesh->RegisterComponent();
                
                // Add layer component
                UAuracronLayerComponent* PlateauLayerComp = NewObject<UAuracronLayerComponent>(Plateau);
                Plateau->AddInstanceComponent(PlateauLayerComp);
                PlateauLayerComp->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
                PlateauLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Cânions Vivos (Living Canyons)
        for (int32 i = 0; i < 3; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-8000.0f, 8000.0f),
                FMath::RandRange(-8000.0f, 8000.0f),
                -200.0f
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Canyon = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Canyon);
                
                // Add layer component
                UAuracronLayerComponent* CanyonLayerComp = NewObject<UAuracronLayerComponent>(Canyon);
                Canyon->AddInstanceComponent(CanyonLayerComp);
                CanyonLayerComp->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
                CanyonLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Florestas Respirantes (Breathing Forests)
        for (int32 i = 0; i < 4; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-6000.0f, 6000.0f),
                FMath::RandRange(-6000.0f, 6000.0f),
                0.0f
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Forest = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Forest);
                
                // Add breathing animation component
                UNiagaraComponent* BreathingEffect = NewObject<UNiagaraComponent>(Forest);
                Forest->AddInstanceComponent(BreathingEffect);
                BreathingEffect->RegisterComponent();
                EffectComponents.Add(BreathingEffect);
                
                // Add layer component
                UAuracronLayerComponent* ForestLayerComp = NewObject<UAuracronLayerComponent>(Forest);
                Forest->AddInstanceComponent(ForestLayerComp);
                ForestLayerComp->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
                ForestLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Guardião Prismal (Prismal Guardian)
        FVector GuardianLocation = GetActorLocation() + FVector(0.0f, 0.0f, 200.0f);
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;
        SpawnParams.Name = FName("PrismalGuardian_Terrestrial");
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        if (AActor* Guardian = World->SpawnActor<AActor>(AActor::StaticClass(), GuardianLocation, FRotator::ZeroRotator, SpawnParams))
        {
            SpawnedContent.Add(Guardian);
            
            // Add guardian-specific components
            UStaticMeshComponent* GuardianMesh = NewObject<UStaticMeshComponent>(Guardian);
            Guardian->SetRootComponent(GuardianMesh);
            GuardianMesh->RegisterComponent();
            
            UNiagaraComponent* GuardianEffect = NewObject<UNiagaraComponent>(Guardian);
            Guardian->AddInstanceComponent(GuardianEffect);
            GuardianEffect->RegisterComponent();
            EffectComponents.Add(GuardianEffect);
            
            // Add layer component
            UAuracronLayerComponent* GuardianLayerComp = NewObject<UAuracronLayerComponent>(Guardian);
            Guardian->AddInstanceComponent(GuardianLayerComp);
            GuardianLayerComp->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
            GuardianLayerComp->RegisterComponent();
        }
        
        // Spawn Torre Prisma (Prisma Tower)
        FVector TowerLocation = GetActorLocation() + FVector(2000.0f, 2000.0f, 800.0f);
        SpawnParams.Name = FName("PrismaTower_Terrestrial");
        
        if (AActor* Tower = World->SpawnActor<AActor>(AActor::StaticClass(), TowerLocation, FRotator::ZeroRotator, SpawnParams))
        {
            SpawnedContent.Add(Tower);
            
            // Add tower-specific components
            UStaticMeshComponent* TowerMesh = NewObject<UStaticMeshComponent>(Tower);
            Tower->SetRootComponent(TowerMesh);
            TowerMesh->RegisterComponent();
            
            UNiagaraComponent* TowerEffect = NewObject<UNiagaraComponent>(Tower);
            Tower->AddInstanceComponent(TowerEffect);
            TowerEffect->RegisterComponent();
            EffectComponents.Add(TowerEffect);
            
            // Add layer component
            UAuracronLayerComponent* TowerLayerComp = NewObject<UAuracronLayerComponent>(Tower);
            Tower->AddInstanceComponent(TowerLayerComp);
            TowerLayerComp->SetCurrentLayer(EAuracronRealmLayer::Terrestrial);
            TowerLayerComp->RegisterComponent();
        }
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Terrestrial Content Generated: %d objects"), SpawnedContent.Num());
}

void AAuracronRealmManager::GenerateCelestialContent()
{
    AURACRON_REALM_LOG(Log, TEXT("Generating Celestial Content (Firmamento Zephyr)"));
    
    if (UWorld* World = GetWorld())
    {
        // Spawn Arquipélagos Orbitais (Orbital Archipelagos)
        for (int32 i = 0; i < 6; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-8000.0f, 8000.0f),
                FMath::RandRange(-8000.0f, 8000.0f),
                FMath::RandRange(-500.0f, 500.0f)
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Archipelago = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Archipelago);
                
                // Add orbital movement component
                UNiagaraComponent* OrbitalEffect = NewObject<UNiagaraComponent>(Archipelago);
                Archipelago->AddInstanceComponent(OrbitalEffect);
                OrbitalEffect->RegisterComponent();
                EffectComponents.Add(OrbitalEffect);
                
                // Add layer component
                UAuracronLayerComponent* ArchipelagoLayerComp = NewObject<UAuracronLayerComponent>(Archipelago);
                Archipelago->AddInstanceComponent(ArchipelagoLayerComp);
                ArchipelagoLayerComp->SetCurrentLayer(EAuracronRealmLayer::Celestial);
                ArchipelagoLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Pontes Aurora (Aurora Bridges)
        for (int32 i = 0; i < 4; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-6000.0f, 6000.0f),
                FMath::RandRange(-6000.0f, 6000.0f),
                FMath::RandRange(-200.0f, 200.0f)
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Bridge = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Bridge);
                
                // Add aurora effect
                UNiagaraComponent* AuroraEffect = NewObject<UNiagaraComponent>(Bridge);
                Bridge->AddInstanceComponent(AuroraEffect);
                AuroraEffect->RegisterComponent();
                EffectComponents.Add(AuroraEffect);
                
                // Add layer component
                UAuracronLayerComponent* BridgeLayerComp = NewObject<UAuracronLayerComponent>(Bridge);
                Bridge->AddInstanceComponent(BridgeLayerComp);
                BridgeLayerComp->SetCurrentLayer(EAuracronRealmLayer::Celestial);
                BridgeLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Núcleo de Tempestade (Storm Core)
        FVector StormCoreLocation = GetActorLocation();
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;
        SpawnParams.Name = FName("StormCore_Celestial");
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        if (AActor* StormCore = World->SpawnActor<AActor>(AActor::StaticClass(), StormCoreLocation, FRotator::ZeroRotator, SpawnParams))
        {
            SpawnedContent.Add(StormCore);
            
            // Add storm effects
            UNiagaraComponent* StormEffect = NewObject<UNiagaraComponent>(StormCore);
            StormCore->AddInstanceComponent(StormEffect);
            StormEffect->RegisterComponent();
            EffectComponents.Add(StormEffect);
            
            // Add layer component
            UAuracronLayerComponent* StormLayerComp = NewObject<UAuracronLayerComponent>(StormCore);
            StormCore->AddInstanceComponent(StormLayerComp);
            StormLayerComp->SetCurrentLayer(EAuracronRealmLayer::Celestial);
            StormLayerComp->RegisterComponent();
        }
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Celestial Content Generated: %d objects"), SpawnedContent.Num());
}

void AAuracronRealmManager::GenerateAbyssalContent()
{
    AURACRON_REALM_LOG(Log, TEXT("Generating Abyssal Content (Abismo Umbrio)"));
    
    if (UWorld* World = GetWorld())
    {
        // Spawn Cavernas Bioluminescentes (Bioluminescent Caves)
        for (int32 i = 0; i < 8; i++)
        {
            FVector SpawnLocation = GetActorLocation() + FVector(
                FMath::RandRange(-4000.0f, 4000.0f),
                FMath::RandRange(-4000.0f, 4000.0f),
                FMath::RandRange(-800.0f, -200.0f)
            );
            
            FActorSpawnParameters SpawnParams;
            SpawnParams.Owner = this;
            SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
            
            if (AActor* Cave = World->SpawnActor<AActor>(AActor::StaticClass(), SpawnLocation, FRotator::ZeroRotator, SpawnParams))
            {
                SpawnedContent.Add(Cave);
                
                // Add bioluminescent effect
                UNiagaraComponent* BioEffect = NewObject<UNiagaraComponent>(Cave);
                Cave->AddInstanceComponent(BioEffect);
                BioEffect->RegisterComponent();
                EffectComponents.Add(BioEffect);
                
                // Add layer component
                UAuracronLayerComponent* CaveLayerComp = NewObject<UAuracronLayerComponent>(Cave);
                Cave->AddInstanceComponent(CaveLayerComp);
                CaveLayerComp->SetCurrentLayer(EAuracronRealmLayer::Abyssal);
                CaveLayerComp->RegisterComponent();
            }
        }
        
        // Spawn Leviatã Umbrático (Umbral Leviathan)
        FVector LeviathanLocation = GetActorLocation() + FVector(0.0f, 0.0f, -500.0f);
        FActorSpawnParameters SpawnParams;
        SpawnParams.Owner = this;
        SpawnParams.Name = FName("UmbralLeviathan_Abyssal");
        SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
        
        if (AActor* Leviathan = World->SpawnActor<AActor>(AActor::StaticClass(), LeviathanLocation, FRotator::ZeroRotator, SpawnParams))
        {
            SpawnedContent.Add(Leviathan);
            
            // Add leviathan effects
            UNiagaraComponent* LeviathanEffect = NewObject<UNiagaraComponent>(Leviathan);
            Leviathan->AddInstanceComponent(LeviathanEffect);
            LeviathanEffect->RegisterComponent();
            EffectComponents.Add(LeviathanEffect);
            
            // Add layer component
            UAuracronLayerComponent* LeviathanLayerComp = NewObject<UAuracronLayerComponent>(Leviathan);
            Leviathan->AddInstanceComponent(LeviathanLayerComp);
            LeviathanLayerComp->SetCurrentLayer(EAuracronRealmLayer::Abyssal);
            LeviathanLayerComp->RegisterComponent();
        }
    }
    
    AURACRON_REALM_LOG(Log, TEXT("Abyssal Content Generated: %d objects"), SpawnedContent.Num());
}

void AAuracronRealmManager::UpdateLayerEvolution(ERealmEvolutionPhase Phase)
{
    AURACRON_REALM_LOG(Log, TEXT("Updating Layer Evolution - Layer: %d, Phase: %d"), (int32)ManagedLayer, (int32)Phase);
    
    // Update content based on evolution phase
    UpdateContentBasedOnPhase(Phase);
    
    // Apply phase-specific environmental changes
    switch (Phase)
    {
        case ERealmEvolutionPhase::Despertar:
            EnvironmentSettings.WeatherIntensity = 0.3f;
            break;
        case ERealmEvolutionPhase::Convergencia:
            EnvironmentSettings.WeatherIntensity = 0.6f;
            break;
        case ERealmEvolutionPhase::Intensificacao:
            EnvironmentSettings.WeatherIntensity = 0.9f;
            break;
        case ERealmEvolutionPhase::Resolucao:
            EnvironmentSettings.WeatherIntensity = 1.2f;
            break;
    }
    
    // Apply updated environment settings
    ApplyEnvironmentalEffects();
}

void AAuracronRealmManager::UpdateContentBasedOnPhase(ERealmEvolutionPhase Phase)
{
    // Update effect intensity based on phase
    float PhaseIntensity = 0.5f;
    switch (Phase)
    {
        case ERealmEvolutionPhase::Despertar: PhaseIntensity = 0.5f; break;
        case ERealmEvolutionPhase::Convergencia: PhaseIntensity = 0.7f; break;
        case ERealmEvolutionPhase::Intensificacao: PhaseIntensity = 0.9f; break;
        case ERealmEvolutionPhase::Resolucao: PhaseIntensity = 1.2f; break;
    }
    
    // Update all effect components
    for (TObjectPtr<UNiagaraComponent> EffectComp : EffectComponents)
    {
        if (EffectComp.IsValid())
        {
            EffectComp->SetFloatParameter(FName("Intensity"), PhaseIntensity);
        }
    }
}

void AAuracronRealmManager::SetLayerVisibility(bool bVisible)
{
    bIsVisible = bVisible;
    
    AURACRON_REALM_LOG(Log, TEXT("Setting Layer Visibility - Layer: %d, Visible: %s"), 
        (int32)ManagedLayer, bVisible ? TEXT("True") : TEXT("False"));
    
    // Update visibility of all spawned content
    for (TObjectPtr<AActor> Actor : SpawnedContent)
    {
        if (Actor.IsValid())
        {
            Actor->SetActorHiddenInGame(!bVisible);
            Actor->SetActorEnableCollision(bVisible);
            Actor->SetActorTickEnabled(bVisible);
        }
    }
    
    // Update effect components
    for (TObjectPtr<UNiagaraComponent> EffectComp : EffectComponents)
    {
        if (EffectComp.IsValid())
        {
            EffectComp->SetVisibility(bVisible);
            if (bVisible)
            {
                EffectComp->Activate();
            }
            else
            {
                EffectComp->Deactivate();
            }
        }
    }
}

void AAuracronRealmManager::SetLayerLODLevel(int32 LODLevel)
{
    CurrentLODLevel = FMath::Clamp(LODLevel, 0, 3);
    
    AURACRON_REALM_LOG(Log, TEXT("Setting Layer LOD Level - Layer: %d, LOD: %d"), 
        (int32)ManagedLayer, CurrentLODLevel);
    
    ApplyLODOptimizations();
}

void AAuracronRealmManager::ApplyEnvironmentalEffects()
{
    AURACRON_REALM_LOG(VeryVerbose, TEXT("Applying Environmental Effects - Layer: %d"), (int32)ManagedLayer);
    
    // Environmental effects are applied through the world's lighting and atmosphere systems
    // This would integrate with UE5's lighting and atmosphere components
    
    // For now, log the settings that would be applied
    AURACRON_REALM_LOG(VeryVerbose, TEXT("Ambient Light: %s, Intensity: %.2f"), 
        *EnvironmentSettings.AmbientLightColor.ToString(), EnvironmentSettings.AmbientLightIntensity);
    AURACRON_REALM_LOG(VeryVerbose, TEXT("Fog Color: %s, Density: %.4f"), 
        *EnvironmentSettings.FogColor.ToString(), EnvironmentSettings.FogDensity);
    AURACRON_REALM_LOG(VeryVerbose, TEXT("Gravity Scale: %.2f"), EnvironmentSettings.GravityScale);
}

void AAuracronRealmManager::UpdateWeatherEffects(float DeltaTime)
{
    if (!EnvironmentSettings.bHasWeatherEffects)
    {
        return;
    }
    
    // Update weather intensity based on time and layer
    float TimeBasedIntensity = FMath::Sin(LastUpdateTime * 0.1f) * 0.3f + 0.7f;
    float CurrentWeatherIntensity = EnvironmentSettings.WeatherIntensity * TimeBasedIntensity;
    
    // Apply weather effects to all effect components
    for (TObjectPtr<UNiagaraComponent> EffectComp : EffectComponents)
    {
        if (EffectComp.IsValid())
        {
            EffectComp->SetFloatParameter(FName("WeatherIntensity"), CurrentWeatherIntensity);
        }
    }
}

void AAuracronRealmManager::UpdateTerrestrialBehavior(float DeltaTime)
{
    // Terrestrial-specific behavior updates
    // Update crystal formations, living canyons, breathing forests
}

void AAuracronRealmManager::UpdateCelestialBehavior(float DeltaTime)
{
    // Celestial-specific behavior updates  
    // Update orbital movements, aurora effects, wind patterns
}

void AAuracronRealmManager::UpdateAbyssalBehavior(float DeltaTime)
{
    // Abyssal-specific behavior updates
    // Update bioluminescence, magma flows, shadow effects
}

void AAuracronRealmManager::UpdatePerformanceMetrics()
{
    // Update performance tracking
    DrawCalls = SpawnedContent.Num(); // Simplified metric
    MemoryUsage = SpawnedContent.Num() * 0.5f; // Simplified metric in MB
}

void AAuracronRealmManager::OptimizeLayerPerformance()
{
    // Apply LOD optimizations
    ApplyLODOptimizations();
    
    // Manage content streaming
    ManageContentStreaming();
}

void AAuracronRealmManager::ApplyLODOptimizations()
{
    // Apply LOD based on current LOD level
    for (TObjectPtr<AActor> Actor : SpawnedContent)
    {
        if (Actor.IsValid())
        {
            if (UStaticMeshComponent* MeshComp = Actor->FindComponentByClass<UStaticMeshComponent>())
            {
                MeshComp->SetForcedLodModel(CurrentLODLevel + 1);
            }
        }
    }
}

void AAuracronRealmManager::ManageContentStreaming()
{
    // Manage content streaming based on player proximity
    if (UWorld* World = GetWorld())
    {
        // Find nearest player
        float NearestPlayerDistance = FLT_MAX;
        for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
        {
            if (APlayerController* PC = Iterator->Get())
            {
                if (APawn* PlayerPawn = PC->GetPawn())
                {
                    float Distance = FVector::Dist(GetActorLocation(), PlayerPawn->GetActorLocation());
                    NearestPlayerDistance = FMath::Min(NearestPlayerDistance, Distance);
                }
            }
        }
        
        // Cull content based on distance
        if (NearestPlayerDistance > 10000.0f)
        {
            CullDistantContent(GetActorLocation(), 10000.0f);
        }
    }
}

void AAuracronRealmManager::CullDistantContent(const FVector& ViewerLocation, float CullDistance)
{
    for (TObjectPtr<AActor> Actor : SpawnedContent)
    {
        if (Actor.IsValid())
        {
            float Distance = FVector::Dist(ViewerLocation, Actor->GetActorLocation());
            bool bShouldBeVisible = Distance <= CullDistance;
            
            Actor->SetActorHiddenInGame(!bShouldBeVisible);
            Actor->SetActorTickEnabled(bShouldBeVisible);
        }
    }
}

void AAuracronRealmManager::DespawnRealmContent()
{
    AURACRON_REALM_LOG(Log, TEXT("Despawning Realm Content - Layer: %d"), (int32)ManagedLayer);
    
    // Destroy all spawned content
    for (TObjectPtr<AActor> Actor : SpawnedContent)
    {
        if (Actor.IsValid())
        {
            Actor->Destroy();
        }
    }
    
    SpawnedContent.Empty();
    EffectComponents.Empty();
    EnvironmentComponents.Empty();
    
    AURACRON_REALM_LOG(Log, TEXT("Realm Content Despawned"));
}

float AAuracronRealmManager::GetCurrentPerformanceMetric() const
{
    return FrameTime; // Return frame time in milliseconds
}

bool AAuracronRealmManager::ValidateLayerConfiguration() const
{
    // Validate that the layer configuration is correct
    if (ManagedLayer == EAuracronRealmLayer::None)
    {
        return false;
    }
    
    if (!LayerComponent)
    {
        return false;
    }
    
    return true;
}

void AAuracronRealmManager::LogLayerStatus() const
{
    AURACRON_REALM_LOG(Log, TEXT("Layer Status - Type: %d, Visible: %s, LOD: %d, Content: %d, Performance: %.2fms"), 
        (int32)ManagedLayer, bIsVisible ? TEXT("Yes") : TEXT("No"), CurrentLODLevel, 
        SpawnedContent.Num(), FrameTime);
}
