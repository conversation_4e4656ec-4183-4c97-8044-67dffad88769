// Production-ready vertical connector actor for Auracron vertical transitions
#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "AuracronVerticalConnectorSystem.h" // for enums/structs used in public API
#include "AuracronVerticalConnector.generated.h"

class USceneComponent;
class UStaticMeshComponent;
class USphereComponent;
class UNiagaraComponent;
class USoundBase;
class UAudioComponent;
class ACharacter;

UCLASS(BlueprintType, Blueprintable)
class AURACRONVERTICALTRANSITIONSBRIDGE_API AAuracronVerticalConnectorActor : public AActor
{
    GENERATED_BODY()

public:
    AAuracronVerticalConnector();

    // AActor
    virtual void OnConstruction(const FTransform& Transform) override;
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaSeconds) override;

    // Configuration
    UFUNCTION(BlueprintCallable, Category = "Vertical Connector")
    void InitializeFromConfig(const FVerticalConnectorConfig& InConfig);

    UFUNCTION(BlueprintCallable, Category = "Vertical Connector")
    void SetConnectorState(EVerticalConnectorState NewState);

    UFUNCTION(BlueprintPure, Category = "Vertical Connector")
    EVerticalConnectorState GetConnectorState() const { return ConnectorState; }

    UFUNCTION(BlueprintPure, Category = "Vertical Connector")
    EVerticalConnectorType GetConnectorType() const { return ConnectorConfig.ConnectorType; }

    UFUNCTION(BlueprintPure, Category = "Vertical Connector")
    const FVerticalConnectorConfig& GetConnectorConfiguration() const { return ConnectorConfig; }

    // Transition effects hook used by the subsystem
    UFUNCTION(BlueprintCallable, Category = "Vertical Connector")
    void ApplyTransitionEffects(ACharacter* Character, bool bStarting);

    // Called by subsystem each tick to maintain internal state
    void UpdateConnectorState(float DeltaTime);

    // Called by subsystem for auto-activation behavior (e.g., geothermal vents)
    void HandleAutoActivation(float DeltaTime);

protected:
    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> Root;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UStaticMeshComponent> VisualMesh;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USphereComponent> ActivationSphere;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UNiagaraComponent> NiagaraFX;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAudioComponent> AudioComponent;

    // Data
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Vertical Connector", meta = (ShowOnlyInnerProperties))
    FVerticalConnectorConfig ConnectorConfig;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Vertical Connector")
    EVerticalConnectorState ConnectorState = EVerticalConnectorState::Inactive;

    // VFX/SFX assets (optional)
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "VFX")
    TObjectPtr<UNiagaraSystem> ActivationFX;

    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Audio")
    TObjectPtr<USoundBase> ActivationSound;

private:
    void UpdateComponentsFromConfig();
    void UpdateFXForState();

    // Timers/state for cooldown and auto activation
    float CooldownRemaining = 0.f;
    float AutoActivationTimer = 0.f;
    float AutoActiveRemaining = 0.f;
    bool bAutoActive = false;
};

