#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInterface.h"
#include "Particles/ParticleSystemComponent.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "PCGComponent.h"
#include "Landscape/Landscape.h"
#include "Foliage/FoliageInstancedStaticMeshComponent.h"
#include "AuracronDynamicRealmBridge.h"
#include "AuracronRealmManager.generated.h"

// Forward declarations
class UAuracronDynamicRealmSubsystem;
class UAuracronLayerComponent;
class UWorldPartitionSubsystem;

/**
 * Realm content configuration data
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronRealmContentConfig
{
    GENERATED_BODY()

    /** Static meshes for this realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<UStaticMesh>> RealmMeshes;

    /** Materials for this realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<UMaterialInterface>> RealmMaterials;

    /** Niagara effects for this realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<UNiagaraSystem>> RealmEffects;

    /** PCG graphs for procedural generation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<UPCGGraph>> PCGGraphs;

    /** Foliage types for this realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<UFoliageType>> FoliageTypes;

    /** Audio assets for this realm */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
    TArray<TObjectPtr<USoundBase>> RealmAudio;

    FAuracronRealmContentConfig()
    {
        // Initialize arrays
    }
};

/**
 * Realm environmental settings
 */
USTRUCT(BlueprintType)
struct AURACRONDYNAMICREALMBRIDGE_API FAuracronRealmEnvironment
{
    GENERATED_BODY()

    /** Lighting settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    FLinearColor AmbientLightColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float AmbientLightIntensity;

    /** Atmospheric settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    FLinearColor FogColor;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float FogDensity;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float FogStartDistance;

    /** Weather effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    bool bHasWeatherEffects;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float WeatherIntensity;

    /** Gravity settings */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float GravityScale;

    /** Wind effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    FVector WindDirection;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
    float WindStrength;

    FAuracronRealmEnvironment()
    {
        AmbientLightColor = FLinearColor::White;
        AmbientLightIntensity = 1.0f;
        FogColor = FLinearColor::Gray;
        FogDensity = 0.02f;
        FogStartDistance = 1000.0f;
        bHasWeatherEffects = false;
        WeatherIntensity = 0.5f;
        GravityScale = 1.0f;
        WindDirection = FVector(1.0f, 0.0f, 0.0f);
        WindStrength = 100.0f;
    }
};

/**
 * Auracron Realm Manager
 * 
 * Manages a specific realm layer (Terrestrial, Celestial, or Abyssal):
 * - Handles layer-specific content generation
 * - Manages environmental effects
 * - Controls layer visibility and LOD
 * - Integrates with PCG for procedural content
 * - Optimizes performance for the layer
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONDYNAMICREALMBRIDGE_API AAuracronRealmManager : public AActor
{
    GENERATED_BODY()

public:
    AAuracronRealmManager();

protected:
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

public:
    // Core configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    EAuracronRealmLayer ManagedLayer;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FAuracronRealmContentConfig ContentConfig;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    FAuracronRealmEnvironment EnvironmentSettings;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    bool bAutoGenerateContent;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Realm Configuration")
    bool bEnableEvolution;

    // Components
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<USceneComponent> RootSceneComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TObjectPtr<UAuracronLayerComponent> LayerComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UPCGComponent>> PCGComponents;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<TObjectPtr<UNiagaraComponent>> EffectComponents;

    // Layer management functions
    UFUNCTION(BlueprintCallable, Category = "Realm Management")
    void InitializeLayer();

    UFUNCTION(BlueprintCallable, Category = "Realm Management")
    void GenerateLayerContent();

    UFUNCTION(BlueprintCallable, Category = "Realm Management")
    void UpdateLayerEvolution(ERealmEvolutionPhase Phase);

    UFUNCTION(BlueprintCallable, Category = "Realm Management")
    void SetLayerVisibility(bool bVisible);

    UFUNCTION(BlueprintCallable, Category = "Realm Management")
    void SetLayerLODLevel(int32 LODLevel);

    // Content management
    UFUNCTION(BlueprintCallable, Category = "Content Management")
    void SpawnRealmContent();

    UFUNCTION(BlueprintCallable, Category = "Content Management")
    void DespawnRealmContent();

    UFUNCTION(BlueprintCallable, Category = "Content Management")
    void UpdateContentBasedOnPhase(ERealmEvolutionPhase Phase);

    // Environmental effects
    UFUNCTION(BlueprintCallable, Category = "Environment")
    void ApplyEnvironmentalEffects();

    UFUNCTION(BlueprintCallable, Category = "Environment")
    void UpdateWeatherEffects(float DeltaTime);

    UFUNCTION(BlueprintCallable, Category = "Environment")
    void SetEnvironmentSettings(const FAuracronRealmEnvironment& NewSettings);

    // Performance optimization
    UFUNCTION(BlueprintCallable, Category = "Performance")
    void OptimizeLayerPerformance();

    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Performance")
    float GetCurrentPerformanceMetric() const;

    UFUNCTION(BlueprintCallable, Category = "Performance")
    void CullDistantContent(const FVector& ViewerLocation, float CullDistance);

    // Debug functions
    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugShowLayerBounds();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugRegenerateContent();

    UFUNCTION(BlueprintCallable, Category = "Debug", CallInEditor)
    void DebugShowPerformanceStats();

protected:
    // Internal state
    UPROPERTY()
    bool bIsInitialized;

    UPROPERTY()
    bool bIsVisible;

    UPROPERTY()
    int32 CurrentLODLevel;

    UPROPERTY()
    float LastUpdateTime;

    UPROPERTY()
    TArray<TObjectPtr<AActor>> SpawnedContent;

    UPROPERTY()
    TArray<TObjectPtr<UActorComponent>> EnvironmentComponents;

    // Performance metrics
    UPROPERTY()
    float FrameTime;

    UPROPERTY()
    int32 DrawCalls;

    UPROPERTY()
    float MemoryUsage;

private:
    // Layer-specific initialization
    void InitializeTerrestrialLayer();
    void InitializeCelestialLayer();
    void InitializeAbyssalLayer();
    
    // Content generation helpers
    void GenerateTerrestrialContent();
    void GenerateCelestialContent();
    void GenerateAbyssalContent();
    
    // Performance helpers
    void UpdatePerformanceMetrics();
    void ApplyLODOptimizations();
    void ManageContentStreaming();
    
    // Environment helpers
    void SetupLayerLighting();
    void SetupLayerAtmosphere();
    void SetupLayerPhysics();
    
    // Utility functions
    bool ValidateLayerConfiguration() const;
    void LogLayerStatus() const;
    
    // Timers
    FTimerHandle ContentUpdateTimer;
    FTimerHandle PerformanceUpdateTimer;
    FTimerHandle EnvironmentUpdateTimer;
};
