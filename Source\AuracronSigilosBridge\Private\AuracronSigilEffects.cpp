/**
 * Auracron Sigil Effects - Implementation
 * 
 * Implements specific effects for all 16 Sigil subtypes using UE 5.6 APIs
 * 
 * Author: Auracron Development Team
 * Version: 1.0.0
 * Date: 2025-08-07
 * UE Version: 5.6+
 */

#include "AuracronSigilEffects.h"
#include "AuracronSigilGameplayTags.h"
#include "AbilitySystemComponent.h"
#include "GameplayEffectExtension.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"

// === UAuracronSigilAttributeSet Implementation ===

UAuracronSigilAttributeSet::UAuracronSigilAttributeSet()
{
    // Initialize default values using UE 5.6 FGameplayAttributeData
    SigilPower = 100.0f;
    SigilDuration = 5.0f;
    SigilCooldownReduction = 0.0f;
    FusionEnergy = 100.0f;
    ArchetypeLevel = 1.0f;

    // Aegis attributes
    ShieldStrength = 100.0f;
    DamageReflection = 0.0f;
    TimeDistortion = 1.0f;

    // Ruin attributes
    ElementalDamage = 0.0f;
    ArmorPenetration = 0.0f;
    CriticalChance = 0.05f;

    // Vesper attributes
    HealingPower = 50.0f;
    MovementSpeed = 600.0f;
    VisionRange = 1200.0f;
    TeleportRange = 800.0f;
}

void UAuracronSigilAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, SigilPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, SigilDuration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, SigilCooldownReduction, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, FusionEnergy, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, ArchetypeLevel, COND_None, REPNOTIFY_Always);

    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, ShieldStrength, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, DamageReflection, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, TimeDistortion, COND_None, REPNOTIFY_Always);

    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, ElementalDamage, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, ArmorPenetration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);

    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, HealingPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, VisionRange, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(UAuracronSigilAttributeSet, TeleportRange, COND_None, REPNOTIFY_Always);
}

// === Replication Callbacks ===

void UAuracronSigilAttributeSet::OnRep_SigilPower(const FGameplayAttributeData& OldSigilPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, SigilPower, OldSigilPower);
}

void UAuracronSigilAttributeSet::OnRep_SigilDuration(const FGameplayAttributeData& OldSigilDuration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, SigilDuration, OldSigilDuration);
}

void UAuracronSigilAttributeSet::OnRep_SigilCooldownReduction(const FGameplayAttributeData& OldSigilCooldownReduction)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, SigilCooldownReduction, OldSigilCooldownReduction);
}

void UAuracronSigilAttributeSet::OnRep_FusionEnergy(const FGameplayAttributeData& OldFusionEnergy)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, FusionEnergy, OldFusionEnergy);
}

void UAuracronSigilAttributeSet::OnRep_ArchetypeLevel(const FGameplayAttributeData& OldArchetypeLevel)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, ArchetypeLevel, OldArchetypeLevel);
}

void UAuracronSigilAttributeSet::OnRep_ShieldStrength(const FGameplayAttributeData& OldShieldStrength)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, ShieldStrength, OldShieldStrength);
}

void UAuracronSigilAttributeSet::OnRep_DamageReflection(const FGameplayAttributeData& OldDamageReflection)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, DamageReflection, OldDamageReflection);
}

void UAuracronSigilAttributeSet::OnRep_TimeDistortion(const FGameplayAttributeData& OldTimeDistortion)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, TimeDistortion, OldTimeDistortion);
}

void UAuracronSigilAttributeSet::OnRep_ElementalDamage(const FGameplayAttributeData& OldElementalDamage)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, ElementalDamage, OldElementalDamage);
}

void UAuracronSigilAttributeSet::OnRep_ArmorPenetration(const FGameplayAttributeData& OldArmorPenetration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, ArmorPenetration, OldArmorPenetration);
}

void UAuracronSigilAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, CriticalChance, OldCriticalChance);
}

void UAuracronSigilAttributeSet::OnRep_HealingPower(const FGameplayAttributeData& OldHealingPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, HealingPower, OldHealingPower);
}

void UAuracronSigilAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, MovementSpeed, OldMovementSpeed);
}

void UAuracronSigilAttributeSet::OnRep_VisionRange(const FGameplayAttributeData& OldVisionRange)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, VisionRange, OldVisionRange);
}

void UAuracronSigilAttributeSet::OnRep_TeleportRange(const FGameplayAttributeData& OldTeleportRange)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(UAuracronSigilAttributeSet, TeleportRange, OldTeleportRange);
}





// === UAuracronSigilAbility Implementation ===

UAuracronSigilAbility::UAuracronSigilAbility()
{
    // Configure ability using UE 5.6 APIs
    InstancingPolicy = EGameplayAbilityInstancingPolicy::InstancedPerActor;
    NetExecutionPolicy = EGameplayAbilityNetExecutionPolicy::LocalPredicted;
    
    // Set default ability tags
    AbilityTags.AddTag(AuracronSigilTags::Sigil);
    
    // Set default activation requirements
    ActivationOwnedTags.AddTag(AuracronSigilTags::Sigil_State_Active);
    
    // Set default blocking tags
    ActivationBlockedTags.AddTag(AuracronSigilTags::Sigil_State_Cooldown);
    
    // Set default cancel tags
    CancelAbilitiesWithTag.AddTag(AuracronSigilTags::Sigil_State_Charging);
}

void UAuracronSigilAbility::ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData)
{
    if (!CommitAbility(Handle, ActorInfo, ActivationInfo))
    {
        EndAbility(Handle, ActorInfo, ActivationInfo, true, true);
        return;
    }

    // Apply Sigil effect
    ApplySigilEffect();

    // Call Blueprint event
    OnSigilActivated();

    // Set timer to end ability
    FTimerHandle TimerHandle;
    GetWorld()->GetTimerManager().SetTimer(
        TimerHandle,
        [this, Handle, ActorInfo, ActivationInfo]()
        {
            EndAbility(Handle, ActorInfo, ActivationInfo, true, false);
        },
        BaseDuration,
        false
    );
}

void UAuracronSigilAbility::EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled)
{
    // Call Blueprint event
    OnSigilDeactivated();

    Super::EndAbility(Handle, ActorInfo, ActivationInfo, bReplicateEndAbility, bWasCancelled);
}

bool UAuracronSigilAbility::CanActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayTagContainer* SourceTags, const FGameplayTagContainer* TargetTags, FGameplayTagContainer* OptionalRelevantTags) const
{
    if (!Super::CanActivateAbility(Handle, ActorInfo, SourceTags, TargetTags, OptionalRelevantTags))
    {
        return false;
    }

    // Additional Sigil-specific checks can be added here
    return true;
}

void UAuracronSigilAbility::ApplySigilEffect_Implementation()
{
    // Base implementation - override in derived classes
    UE_LOG(LogTemp, Log, TEXT("AURACRON: Base Sigil effect applied"));
}
